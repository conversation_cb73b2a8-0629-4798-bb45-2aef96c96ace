import * as vscode from 'vscode';
import * as WebSocket from 'ws';

export interface WebSocketMessage {
    type: string;
    data: any;
    id?: string;
    timestamp?: number;
}

export interface WebSocketConfig {
    url: string;
    reconnectInterval: number;
    maxReconnectAttempts: number;
    heartbeatInterval: number;
}

export class WebSocketService {
    private ws: WebSocket | null = null;
    private config: WebSocketConfig;
    private isConnected = false;
    private reconnectAttempts = 0;
    private heartbeatTimer: NodeJS.Timeout | null = null;
    private reconnectTimer: NodeJS.Timeout | null = null;
    private messageHandlers = new Map<string, (data: any) => void>();
    private pendingMessages: WebSocketMessage[] = [];
    private outputChannel: vscode.OutputChannel;

    constructor(config: WebSocketConfig) {
        this.config = config;
        this.outputChannel = vscode.window.createOutputChannel('Agent Zero WebSocket');
    }

    public async connect(): Promise<boolean> {
        if (this.isConnected) {
            return true;
        }

        try {
            this.outputChannel.appendLine(`Connecting to WebSocket: ${this.config.url}`);
            
            this.ws = new WebSocket(this.config.url);
            
            return new Promise((resolve, reject) => {
                if (!this.ws) {
                    reject(new Error('WebSocket instance is null'));
                    return;
                }

                const timeout = setTimeout(() => {
                    reject(new Error('Connection timeout'));
                }, 10000);

                this.ws.on('open', () => {
                    clearTimeout(timeout);
                    this.isConnected = true;
                    this.reconnectAttempts = 0;
                    this.outputChannel.appendLine('WebSocket connected successfully');
                    
                    this.startHeartbeat();
                    this.processPendingMessages();
                    
                    resolve(true);
                });

                this.ws.on('message', (data: WebSocket.Data) => {
                    this.handleMessage(data);
                });

                this.ws.on('close', (code: number, reason: string) => {
                    this.handleClose(code, reason);
                });

                this.ws.on('error', (error: Error) => {
                    clearTimeout(timeout);
                    this.handleError(error);
                    reject(error);
                });
            });

        } catch (error) {
            this.outputChannel.appendLine(`WebSocket connection failed: ${error}`);
            return false;
        }
    }

    public disconnect(): void {
        if (this.ws) {
            this.isConnected = false;
            this.stopHeartbeat();
            this.stopReconnect();
            this.ws.close();
            this.ws = null;
            this.outputChannel.appendLine('WebSocket disconnected');
        }
    }

    public send(message: WebSocketMessage): boolean {
        if (!this.isConnected || !this.ws) {
            // Queue message for later sending
            this.pendingMessages.push(message);
            this.outputChannel.appendLine(`Message queued (not connected): ${message.type}`);
            return false;
        }

        try {
            const messageWithMetadata: WebSocketMessage = {
                ...message,
                id: message.id || this.generateMessageId(),
                timestamp: Date.now()
            };

            this.ws.send(JSON.stringify(messageWithMetadata));
            this.outputChannel.appendLine(`Message sent: ${message.type}`);
            return true;

        } catch (error) {
            this.outputChannel.appendLine(`Failed to send message: ${error}`);
            return false;
        }
    }

    public onMessage(type: string, handler: (data: any) => void): void {
        this.messageHandlers.set(type, handler);
    }

    public removeMessageHandler(type: string): void {
        this.messageHandlers.delete(type);
    }

    private handleMessage(data: WebSocket.Data): void {
        try {
            const message: WebSocketMessage = JSON.parse(data.toString());
            
            this.outputChannel.appendLine(`Message received: ${message.type}`);
            
            // Handle heartbeat responses
            if (message.type === 'pong') {
                return;
            }

            // Call registered handler
            const handler = this.messageHandlers.get(message.type);
            if (handler) {
                handler(message.data);
            } else {
                this.outputChannel.appendLine(`No handler for message type: ${message.type}`);
            }

        } catch (error) {
            this.outputChannel.appendLine(`Failed to parse message: ${error}`);
        }
    }

    private handleClose(code: number, reason: string): void {
        this.isConnected = false;
        this.stopHeartbeat();
        
        this.outputChannel.appendLine(`WebSocket closed: ${code} - ${reason}`);
        
        // Attempt to reconnect if not manually disconnected
        if (code !== 1000 && this.reconnectAttempts < this.config.maxReconnectAttempts) {
            this.scheduleReconnect();
        }
    }

    private handleError(error: Error): void {
        this.outputChannel.appendLine(`WebSocket error: ${error.message}`);
        this.isConnected = false;
    }

    private scheduleReconnect(): void {
        if (this.reconnectTimer) {
            return;
        }

        this.reconnectAttempts++;
        const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000); // Exponential backoff, max 30s
        
        this.outputChannel.appendLine(`Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);
        
        this.reconnectTimer = setTimeout(async () => {
            this.reconnectTimer = null;
            
            try {
                await this.connect();
            } catch (error) {
                this.outputChannel.appendLine(`Reconnect attempt ${this.reconnectAttempts} failed: ${error}`);
                
                if (this.reconnectAttempts < this.config.maxReconnectAttempts) {
                    this.scheduleReconnect();
                } else {
                    this.outputChannel.appendLine('Max reconnect attempts reached');
                    vscode.window.showErrorMessage('Agent Zero WebSocket connection lost. Please restart the service.');
                }
            }
        }, delay);
    }

    private stopReconnect(): void {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
        this.reconnectAttempts = 0;
    }

    private startHeartbeat(): void {
        this.stopHeartbeat();
        
        this.heartbeatTimer = setInterval(() => {
            if (this.isConnected && this.ws) {
                this.send({
                    type: 'ping',
                    data: {}
                });
            }
        }, this.config.heartbeatInterval);
    }

    private stopHeartbeat(): void {
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
        }
    }

    private processPendingMessages(): void {
        if (this.pendingMessages.length === 0) {
            return;
        }

        this.outputChannel.appendLine(`Processing ${this.pendingMessages.length} pending messages`);
        
        const messages = [...this.pendingMessages];
        this.pendingMessages = [];
        
        for (const message of messages) {
            this.send(message);
        }
    }

    private generateMessageId(): string {
        return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    public getConnectionStatus(): {
        connected: boolean;
        reconnectAttempts: number;
        pendingMessages: number;
    } {
        return {
            connected: this.isConnected,
            reconnectAttempts: this.reconnectAttempts,
            pendingMessages: this.pendingMessages.length
        };
    }

    public dispose(): void {
        this.disconnect();
        this.outputChannel.dispose();
    }
}

// WebSocket message types for Agent Zero
export const MessageTypes = {
    // Chat messages
    CHAT_MESSAGE: 'chat_message',
    CHAT_RESPONSE: 'chat_response',
    
    // Agent management
    AGENT_CREATE: 'agent_create',
    AGENT_DELETE: 'agent_delete',
    AGENT_STATUS: 'agent_status',
    AGENT_LIST: 'agent_list',
    
    // Code execution
    CODE_EXECUTE: 'code_execute',
    CODE_RESULT: 'code_result',
    
    // File operations
    FILE_READ: 'file_read',
    FILE_WRITE: 'file_write',
    FILE_LIST: 'file_list',
    
    // Memory operations
    MEMORY_SAVE: 'memory_save',
    MEMORY_LOAD: 'memory_load',
    MEMORY_SEARCH: 'memory_search',
    
    // Tool operations
    TOOL_EXECUTE: 'tool_execute',
    TOOL_RESULT: 'tool_result',
    TOOL_LIST: 'tool_list',
    
    // System messages
    SYSTEM_STATUS: 'system_status',
    SYSTEM_ERROR: 'system_error',
    SYSTEM_LOG: 'system_log',
    
    // Heartbeat
    PING: 'ping',
    PONG: 'pong'
} as const;
