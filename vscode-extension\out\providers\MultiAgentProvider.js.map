{"version": 3, "file": "MultiAgentProvider.js", "sourceRoot": "", "sources": ["../../src/providers/MultiAgentProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAIjC,MAAa,kBAAkB;IAI3B,YACY,OAAgC,EAChC,gBAAkC;QADlC,YAAO,GAAP,OAAO,CAAyB;QAChC,qBAAgB,GAAhB,gBAAgB,CAAkB;QALtC,yBAAoB,GAAyD,IAAI,MAAM,CAAC,YAAY,EAAmC,CAAC;QACvI,wBAAmB,GAAkD,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;QAM1G,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC5B,CAAC;IAEO,gBAAgB;QACpB,0BAA0B;QAC1B,MAAM,kBAAkB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YACxG,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,MAAM,wBAAwB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;YACxG,MAAM,IAAI,CAAC,6BAA6B,EAAE,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,4BAA4B;QAC5B,MAAM,kBAAkB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,wBAAwB,EAAE,KAAK,EAAE,KAAY,EAAE,EAAE;YACxG,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,kBAAkB,CACvD,0CAA0C,KAAK,CAAC,IAAI,2BAA2B,EAC/E,EAAE,KAAK,EAAE,IAAI,EAAE,EACf,QAAQ,CACX,CAAC;YAEF,IAAI,YAAY,KAAK,QAAQ,EAAE;gBAC3B,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;gBAClD,IAAI,CAAC,OAAO,EAAE,CAAC;gBACf,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,kBAAkB,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;aACxE;QACL,CAAC,CAAC,CAAC;QAEH,MAAM,iBAAiB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,uBAAuB,EAAE,KAAK,EAAE,KAAY,EAAE,EAAE;YACtG,MAAM,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,MAAM,uBAAuB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,6BAA6B,EAAE,CAAC,KAAY,EAAE,EAAE;YAC5G,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,MAAM,qBAAqB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,2BAA2B,EAAE,CAAC,KAAY,EAAE,EAAE;YACxG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,MAAM,qBAAqB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,2BAA2B,EAAE,KAAK,EAAE,WAAkB,EAAE,EAAE;YACpH,MAAM,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,MAAM,yBAAyB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACpG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,wCAAwC;QACxC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAC3B,kBAAkB,EAClB,wBAAwB,EACxB,kBAAkB,EAClB,iBAAiB,EACjB,uBAAuB,EACvB,qBAAqB,EACrB,qBAAqB,EACrB,yBAAyB,CAC5B,CAAC;IACN,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,QAAiB;QACnD,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,CAAC;QAE5D,MAAM,aAAa,GAAG,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAC7C,KAAK,EAAE,QAAQ,CAAC,IAAI;YACpB,WAAW,EAAE,QAAQ,CAAC,WAAW;YACjC,MAAM,EAAE,iBAAiB,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YAC3D,QAAQ,EAAE,QAAQ;SACrB,CAAC,CAAC,CAAC;QAEJ,MAAM,gBAAgB,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,aAAa,EAAE;YACtE,WAAW,EAAE,0BAA0B;YACvC,kBAAkB,EAAE,IAAI;YACxB,aAAa,EAAE,IAAI;SACtB,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,EAAE;YACnB,OAAO;SACV;QAED,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YAC/C,MAAM,EAAE,kBAAkB;YAC1B,KAAK,EAAE,gBAAgB,CAAC,QAAQ,CAAC,IAAI;YACrC,aAAa,EAAE,CAAC,KAAK,EAAE,EAAE;gBACrB,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE;oBACrC,OAAO,4BAA4B,CAAC;iBACvC;gBACD,OAAO,IAAI,CAAC;YAChB,CAAC;SACJ,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,EAAE;YACZ,OAAO;SACV;QAED,IAAI;YACA,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,uBAAuB,CAC7D,gBAAgB,CAAC,QAAQ,CAAC,EAAE,EAC5B,SAAS,EACT,QAAQ,CACX,CAAC;YAEF,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,kBAAkB,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;YAErE,gCAAgC;YAChC,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAC1D,UAAU,KAAK,CAAC,IAAI,yBAAyB,EAC7C,cAAc,CACjB,CAAC;YAEF,IAAI,WAAW,KAAK,cAAc,EAAE;gBAChC,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;aACrC;SAEJ;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,2BAA2B,KAAK,EAAE,CAAC,CAAC;SACtE;IACL,CAAC;IAEO,KAAK,CAAC,6BAA6B;QACvC,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC1C,mBAAmB,EACnB,qBAAqB,EACrB,MAAM,CAAC,UAAU,CAAC,GAAG,EACrB;YACI,aAAa,EAAE,IAAI;YACnB,uBAAuB,EAAE,IAAI;SAChC,CACJ,CAAC;QAEF,KAAK,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAEvD,KAAK,CAAC,OAAO,CAAC,mBAAmB,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YAChD,QAAQ,OAAO,CAAC,OAAO,EAAE;gBACrB,KAAK,aAAa;oBACd,IAAI;wBACA,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;wBAC5E,IAAI,CAAC,OAAO,EAAE,CAAC;wBACf,KAAK,CAAC,OAAO,EAAE,CAAC;wBAChB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,yBAAyB,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;qBAC/E;oBAAC,OAAO,KAAK,EAAE;wBACZ,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;4BACtB,OAAO,EAAE,OAAO;4BAChB,OAAO,EAAE,KAAK,CAAC,QAAQ,EAAE;yBAC5B,CAAC,CAAC;qBACN;oBACD,MAAM;aACb;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAAC,KAAY;QAC/C,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YAC3C,MAAM,EAAE,kBAAkB;YAC1B,WAAW,EAAE,YAAY;SAC5B,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,EAAE;YACR,OAAO;SACV;QAED,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YACjD,MAAM,EAAE,wBAAwB;YAChC,WAAW,EAAE,2BAA2B;SAC3C,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,EAAE;YACd,OAAO;SACV;QAED,MAAM,aAAa,GAAG;YAClB,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAc,EAAE;YACvC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAiB,EAAE;YAC7C,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAe,EAAE;YACzC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAiB,EAAE;SAChD,CAAC;QAEF,MAAM,gBAAgB,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,aAAa,EAAE;YACtE,WAAW,EAAE,sBAAsB;SACtC,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,EAAE;YACnB,OAAO;SACV;QAED,IAAI;YACA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,EAAE;gBAC5D,KAAK;gBACL,WAAW;gBACX,MAAM,EAAE,SAAS;gBACjB,QAAQ,EAAE,gBAAgB,CAAC,KAAK;gBAChC,YAAY,EAAE,EAAE;gBAChB,UAAU,EAAE,MAAM;aACrB,CAAC,CAAC;YAEH,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,SAAS,KAAK,iBAAiB,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;SAErF;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,0BAA0B,KAAK,EAAE,CAAC,CAAC;SACrE;IACL,CAAC;IAEO,qBAAqB,CAAC,KAAY;QACtC,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC1C,cAAc,EACd,kBAAkB,KAAK,CAAC,IAAI,EAAE,EAC9B,MAAM,CAAC,UAAU,CAAC,GAAG,EACrB;YACI,aAAa,EAAE,IAAI;YACnB,uBAAuB,EAAE,IAAI;SAChC,CACJ,CAAC;QAEF,KAAK,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;QAErD,KAAK,CAAC,OAAO,CAAC,mBAAmB,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YAChD,QAAQ,OAAO,CAAC,OAAO,EAAE;gBACrB,KAAK,YAAY;oBACb,MAAM,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;oBAC3C,KAAK,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU;oBAChE,MAAM;gBACV,KAAK,aAAa;oBACd,mCAAmC;oBACnC,MAAM;aACb;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,mBAAmB,CAAC,KAAY;QACpC,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC1C,YAAY,EACZ,UAAU,KAAK,CAAC,IAAI,EAAE,EACtB,MAAM,CAAC,UAAU,CAAC,GAAG,EACrB;YACI,aAAa,EAAE,IAAI;YACnB,uBAAuB,EAAE,IAAI;SAChC,CACJ,CAAC;QAEF,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC5D,KAAK,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAC9D,CAAC;IAEO,kBAAkB;QACtB,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC1C,gBAAgB,EAChB,uBAAuB,EACvB,MAAM,CAAC,UAAU,CAAC,GAAG,EACrB;YACI,aAAa,EAAE,IAAI;YACnB,uBAAuB,EAAE,IAAI;SAChC,CACJ,CAAC;QAEF,KAAK,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAElD,KAAK,CAAC,OAAO,CAAC,mBAAmB,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YAChD,QAAQ,OAAO,CAAC,OAAO,EAAE;gBACrB,KAAK,aAAa;oBACd,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC;oBACrC,KAAK,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC,CAAC,UAAU;oBAC7D,MAAM;gBACV,KAAK,WAAW;oBACZ,uBAAuB;oBACvB,MAAM;aACb;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,0BAA0B;QAC9B,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAoIC,CAAC;IACb,CAAC;IAEO,mBAAmB,CAAC,KAAY;QACpC,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC5D,MAAM,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,MAAM,CAAC;QACtE,MAAM,cAAc,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC,MAAM,CAAC;QAC1E,MAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,QAAyB,CAAC;QAEzD,OAAO;;;;;;;;;;;;;;;;;;;;;;0CAsB2B,QAAQ,EAAE,IAAI,IAAI,IAAI;sBAC1C,KAAK,CAAC,IAAI;mDACmB,KAAK,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,CAAC,WAAW,EAAE;;;;;;gDAM9C,KAAK,CAAC,IAAI;mDACP,KAAK,CAAC,OAAO,CAAC,cAAc,EAAE;yDACxB,KAAK,CAAC,YAAY,CAAC,cAAc,EAAE;oDACxC,KAAK,CAAC,QAAQ,CAAC,MAAM;;;;;yDAKhB,YAAY;2DACV,cAAc;uDAClB,KAAK,CAAC,MAAM;;;;;;;sBAO7C,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,qBAAqB,GAAG,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;;;;;;;sBAOzF,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,qBAAqB,IAAI,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;gBA2B1F,CAAC;IACb,CAAC;IAEO,iBAAiB,CAAC,KAAY,EAAE,KAAkB;QACtD,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;8BACb,IAAI,CAAC,MAAM;sBACnB,IAAI,CAAC,KAAK;sBACV,IAAI,CAAC,MAAM;sBACX,IAAI,CAAC,QAAQ;sBACb,IAAI,CAAC,SAAS,CAAC,kBAAkB,EAAE;sBACnC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC,GAAG;;SAE3E,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAEZ,OAAO;;;;;;;;;;;;;;;4BAea,KAAK,CAAC,IAAI;;;;;;;;;;;;;sBAahB,QAAQ;;;;gBAId,CAAC;IACb,CAAC;IAEO,qBAAqB;QACzB,6DAA6D;QAC7D,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBA6BC,CAAC;IACb,CAAC;IAED,kCAAkC;IAClC,WAAW,CAAC,OAAc;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IACtD,CAAC;IAED,WAAW,CAAC,OAAe;QACvB,OAAO,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IACtD,CAAC;IAED,OAAO;QACH,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC;QACjC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;IACpC,CAAC;CACJ;AAvlBD,gDAulBC"}