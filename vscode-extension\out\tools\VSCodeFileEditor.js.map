{"version": 3, "file": "VSCodeFileEditor.js", "sourceRoot": "", "sources": ["../../src/tools/VSCodeFileEditor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,2CAA6B;AAuB7B,MAAa,gBAAgB;IAGzB;QACI,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,wBAAwB,CAAC,CAAC;IACrF,CAAC;IAEM,KAAK,CAAC,gBAAgB,CAAC,SAA4B;QACtD,IAAI;YACA,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,6BAA6B,SAAS,CAAC,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;YAElG,QAAQ,SAAS,CAAC,IAAI,EAAE;gBACpB,KAAK,MAAM;oBACP,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBAC/C,KAAK,OAAO;oBACR,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC;gBACzE,KAAK,QAAQ;oBACT,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC;gBAC5E,KAAK,QAAQ;oBACT,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBACjD,KAAK,QAAQ;oBACT,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC;gBAC1E,KAAK,MAAM;oBACP,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC;gBACxE,KAAK,MAAM;oBACP,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC;gBACxE;oBACI,MAAM,IAAI,KAAK,CAAC,0BAA0B,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;aACnE;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,0BAA0B,KAAK,EAAE,CAAC,CAAC;YACjE,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE;aAC1B,CAAC;SACL;IACL,CAAC;IAEO,KAAK,CAAC,QAAQ,CAAC,QAAgB;QACnC,IAAI;YACA,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YACtC,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YACzD,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YACvD,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAEjD,OAAO;gBACH,OAAO,EAAE,IAAI;gBACb,OAAO;gBACP,QAAQ,EAAE;oBACN,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,QAAQ,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;oBAC9B,OAAO,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;oBAC7B,WAAW,EAAE,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC,QAAQ,CAAC,SAAS;oBACpD,WAAW,EAAE,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;iBAC/C;aACJ,CAAC;SACL;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,wBAAwB,KAAK,EAAE;aACzC,CAAC;SACL;IACL,CAAC;IAEO,KAAK,CAAC,SAAS,CAAC,QAAgB,EAAE,OAAe;QACrD,IAAI;YACA,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YACtC,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAClC,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,EAAE,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;YAElE,mDAAmD;YACnD,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE;gBAC9B,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;gBAC9D,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;aAClD;YAED,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACjD,OAAO;gBACH,OAAO,EAAE,IAAI;gBACb,QAAQ,EAAE;oBACN,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,QAAQ,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;oBAC9B,OAAO,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;oBAC7B,WAAW,EAAE,KAAK;oBAClB,WAAW,EAAE,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;iBAC/C;aACJ,CAAC;SACL;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,yBAAyB,KAAK,EAAE;aAC1C,CAAC;SACL;IACL,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,QAAgB,EAAE,OAAe;QACxD,IAAI;YACA,8BAA8B;YAC9B,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACjD,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE;gBACrB,gCAAgC;gBAChC,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;aAClD;YAED,MAAM,UAAU,GAAG,CAAC,UAAU,CAAC,OAAO,IAAI,EAAE,CAAC,GAAG,OAAO,CAAC;YACxD,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;SACrD;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,6BAA6B,KAAK,EAAE;aAC9C,CAAC;SACL;IACL,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,QAAgB;QACrC,IAAI;YACA,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YACtC,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;YAE3E,OAAO;gBACH,OAAO,EAAE,IAAI;aAChB,CAAC;SACL;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,0BAA0B,KAAK,EAAE;aAC3C,CAAC;SACL;IACL,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,QAAgB,EAAE,UAAkB,EAAE;QAC3D,IAAI;YACA,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YAEtC,uCAAuC;YACvC,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;YACzD,IAAI;gBACA,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;aACrD;YAAC,MAAM;gBACJ,gCAAgC;aACnC;YAED,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;SAClD;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,0BAA0B,KAAK,EAAE;aAC3C,CAAC;SACL;IACL,CAAC;IAEO,KAAK,CAAC,QAAQ,CAAC,UAAkB,EAAE,UAAkB;QACzD,IAAI;YACA,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YAC9C,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YAE9C,8CAA8C;YAC9C,MAAM,YAAY,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;YACrE,IAAI;gBACA,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;aAC3D;YAAC,MAAM;gBACJ,gCAAgC;aACnC;YAED,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,EAAE,SAAS,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC;YAE7E,OAAO;gBACH,OAAO,EAAE,IAAI;aAChB,CAAC;SACL;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,wBAAwB,KAAK,EAAE;aACzC,CAAC;SACL;IACL,CAAC;IAEO,KAAK,CAAC,QAAQ,CAAC,UAAkB,EAAE,UAAkB;QACzD,IAAI;YACA,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YAC9C,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YAE9C,8CAA8C;YAC9C,MAAM,YAAY,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;YACrE,IAAI;gBACA,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;aAC3D;YAAC,MAAM;gBACJ,gCAAgC;aACnC;YAED,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC;YAE3E,OAAO;gBACH,OAAO,EAAE,IAAI;aAChB,CAAC;SACL;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,wBAAwB,KAAK,EAAE;aACzC,CAAC;SACL;IACL,CAAC;IAEM,KAAK,CAAC,SAAS,CAAC,aAAqB,EAAE,OAAgB;QAC1D,IAAI;YACA,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;YAC3C,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;YAE7D,IAAI,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;gBACvC,IAAI;gBACJ,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC;gBACpC,WAAW,EAAE,IAAI,KAAK,MAAM,CAAC,QAAQ,CAAC,SAAS;gBAC/C,MAAM,EAAE,IAAI,KAAK,MAAM,CAAC,QAAQ,CAAC,IAAI;aACxC,CAAC,CAAC,CAAC;YAEJ,mCAAmC;YACnC,IAAI,OAAO,EAAE;gBACT,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;gBACvC,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;aACvD;YAED,OAAO;gBACH,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;aAC1C,CAAC;SACL;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,yBAAyB,KAAK,EAAE;aAC1C,CAAC;SACL;IACL,CAAC;IAEM,KAAK,CAAC,aAAa,CAAC,UAAkB,EAAE,KAAa,EAAE,WAAoB;QAC9E,IAAI;YACA,MAAM,OAAO,GAAU,EAAE,CAAC;YAE1B,qCAAqC;YACrC,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,eAAe,CACxD,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,EAClC,EAAE,OAAO,EAAE,WAAW,IAAI,MAAM,EAAE,OAAO,EAAE,oBAAoB,EAAE,CACpE,CAAC;YAEF,KAAK,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,IAAI,aAAa,EAAE;gBACxC,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE;oBACzB,OAAO,CAAC,IAAI,CAAC;wBACT,IAAI,EAAE,GAAG,CAAC,MAAM;wBAChB,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC;wBAChC,MAAM,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC;wBACvC,IAAI,EAAE,KAAK,CAAC,IAAI;wBAChB,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,IAAI;qBAC9B,CAAC,CAAC;iBACN;aACJ;YAED,OAAO;gBACH,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;aAC5C,CAAC;SACL;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,8BAA8B,KAAK,EAAE;aAC/C,CAAC;SACL;IACL,CAAC;IAEO,UAAU,CAAC,QAAgB;QAC/B,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;YAC3B,OAAO,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACpC;QAED,gCAAgC;QAChC,MAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/D,IAAI,eAAe,EAAE;YACjB,OAAO,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC;SAC3E;QAED,4BAA4B;QAC5B,OAAO,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;IACnD,CAAC;IAEO,aAAa,CAAC,QAAgB;QAClC,MAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/D,IAAI,CAAC,eAAe,EAAE;YAClB,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC5C,OAAO,YAAY,CAAC,UAAU,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAC/D,CAAC;IAEO,oBAAoB,CAAC,IAAqB;QAC9C,+EAA+E;QAC/E,IAAI,WAAW,GAAG,EAAE,CAAC;QACrB,WAAW,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QACnE,WAAW,IAAI,KAAK,CAAC,CAAC,sCAAsC;QAC5D,WAAW,IAAI,KAAK,CAAC,CAAC,6BAA6B;QACnD,WAAW,IAAI,KAAK,CAAC,CAAC,8BAA8B;QACpD,OAAO,WAAW,CAAC;IACvB,CAAC;IAEM,OAAO;QACV,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;IACjC,CAAC;CACJ;AAjTD,4CAiTC"}