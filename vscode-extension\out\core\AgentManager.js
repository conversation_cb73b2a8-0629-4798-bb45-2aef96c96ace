"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentManager = void 0;
const vscode = __importStar(require("vscode"));
class AgentManager {
    constructor(modelManager, memoryManager, toolManager, enhancedMemoryManager) {
        this.modelManager = modelManager;
        this.memoryManager = memoryManager;
        this.toolManager = toolManager;
        this.enhancedMemoryManager = enhancedMemoryManager;
        this.agents = new Map();
        this.activeAgent = null;
        this.agentCounter = 0;
        this.apiService = null;
    }
    async initialize(apiService) {
        this.apiService = apiService;
        // Initialize API service
        await this.apiService.initialize();
        // Load agents from Agent Zero service
        await this.loadAgentsFromService();
        // If no agents exist, create the main Agent Zero
        if (this.agents.size === 0) {
            const mainAgent = await this.createAgentViaService('Agent Zero', 'General Assistant');
            this.activeAgent = mainAgent.id;
        }
        else {
            // Set the first agent as active
            const firstAgent = Array.from(this.agents.values())[0];
            this.activeAgent = firstAgent.id;
        }
        // Load any saved agents from workspace state
        await this.loadAgentsFromState();
    }
    async createAgent(name, type, parentId) {
        if (this.apiService) {
            // Use API service to create agent
            return await this.createAgentViaService(name, type, parentId);
        }
        else {
            // Fallback to local creation
            const agent = {
                id: `agent-${++this.agentCounter}`,
                name,
                type,
                status: 'idle',
                created: new Date(),
                lastActivity: new Date(),
                parentId,
                children: [],
                context: {}
            };
            this.agents.set(agent.id, agent);
            // Add to parent's children if parent exists
            if (parentId && this.agents.has(parentId)) {
                const parent = this.agents.get(parentId);
                parent.children.push(agent.id);
            }
            this.saveAgentsToState();
            return agent;
        }
    }
    getAgent(id) {
        return this.agents.get(id);
    }
    getAllAgents() {
        return Array.from(this.agents.values());
    }
    getActiveAgent() {
        return this.activeAgent ? this.agents.get(this.activeAgent) || null : null;
    }
    setActiveAgent(id) {
        if (this.agents.has(id)) {
            this.activeAgent = id;
            return true;
        }
        return false;
    }
    async deleteAgent(id) {
        const agent = this.agents.get(id);
        if (!agent) {
            return false;
        }
        // Remove from parent's children
        if (agent.parentId && this.agents.has(agent.parentId)) {
            const parent = this.agents.get(agent.parentId);
            parent.children = parent.children.filter(childId => childId !== id);
        }
        // Delete all children recursively
        for (const childId of agent.children) {
            await this.deleteAgent(childId);
        }
        // Remove the agent
        this.agents.delete(id);
        // If this was the active agent, set a new one
        if (this.activeAgent === id) {
            const remainingAgents = this.getAllAgents();
            this.activeAgent = remainingAgents.length > 0 ? remainingAgents[0].id : null;
        }
        this.saveAgentsToState();
        return true;
    }
    async executeCode(code, language) {
        const activeAgent = this.getActiveAgent();
        if (!activeAgent) {
            vscode.window.showErrorMessage('No active agent found');
            return;
        }
        if (!this.apiService) {
            vscode.window.showErrorMessage('API service not initialized');
            return;
        }
        try {
            activeAgent.status = 'working';
            activeAgent.lastActivity = new Date();
            // Execute code via API service
            const result = await this.apiService.executeCode({
                code,
                language,
                agentId: activeAgent.id
            });
            if (result.success) {
                vscode.window.showInformationMessage('Code executed successfully');
                if (result.output) {
                    // Show output in a new document or output channel
                    const outputChannel = vscode.window.createOutputChannel('Agent Zero Code Output');
                    outputChannel.appendLine(result.output);
                    outputChannel.show();
                }
            }
            else {
                throw new Error(result.error || 'Code execution failed');
            }
            activeAgent.status = 'idle';
        }
        catch (error) {
            activeAgent.status = 'error';
            vscode.window.showErrorMessage(`Code execution failed: ${error}`);
        }
    }
    async analyzeFile(uri) {
        const activeAgent = this.getActiveAgent();
        if (!activeAgent) {
            vscode.window.showErrorMessage('No active agent found');
            return;
        }
        try {
            activeAgent.status = 'working';
            activeAgent.lastActivity = new Date();
            const document = await vscode.workspace.openTextDocument(uri);
            const content = document.getText();
            // Use the model manager to analyze the file
            const analysis = await this.modelManager.analyzeCode(content, document.languageId);
            // Show analysis result
            vscode.window.showInformationMessage('File analysis completed');
            activeAgent.status = 'idle';
        }
        catch (error) {
            activeAgent.status = 'error';
            vscode.window.showErrorMessage(`File analysis failed: ${error}`);
        }
    }
    async generateCode(prompt, language) {
        const activeAgent = this.getActiveAgent();
        if (!activeAgent) {
            vscode.window.showErrorMessage('No active agent found');
            return;
        }
        try {
            activeAgent.status = 'working';
            activeAgent.lastActivity = new Date();
            // Use the model manager to generate code
            const generatedCode = await this.modelManager.generateCode(prompt, language);
            // Insert generated code into active editor
            const editor = vscode.window.activeTextEditor;
            if (editor) {
                await editor.edit(editBuilder => {
                    editBuilder.insert(editor.selection.active, generatedCode);
                });
            }
            vscode.window.showInformationMessage('Code generated successfully');
            activeAgent.status = 'idle';
        }
        catch (error) {
            activeAgent.status = 'error';
            vscode.window.showErrorMessage(`Code generation failed: ${error}`);
        }
    }
    async sendMessage(message, agentId) {
        const targetAgent = agentId ? this.getAgent(agentId) : this.getActiveAgent();
        if (!targetAgent) {
            throw new Error('No target agent found');
        }
        if (!this.apiService) {
            throw new Error('API service not initialized');
        }
        try {
            targetAgent.status = 'working';
            targetAgent.lastActivity = new Date();
            // Get project context and relevant information
            let enhancedContext = targetAgent.context;
            if (this.enhancedMemoryManager) {
                const projectContext = await this.enhancedMemoryManager.getProjectContext();
                const relevantContext = await this.enhancedMemoryManager.getRelevantContext(message);
                enhancedContext = {
                    ...targetAgent.context,
                    projectContext,
                    relevantContext,
                    systemMessage: this.buildContextualSystemMessage(projectContext, targetAgent.context.systemMessage)
                };
            }
            // Send message via API service with enhanced context
            const response = await this.apiService.sendChatMessage(message, targetAgent.id);
            // Update agent context
            targetAgent.context.lastMessage = message;
            targetAgent.context.lastResponse = response;
            // Save conversation to memory
            if (this.enhancedMemoryManager) {
                await this.enhancedMemoryManager.saveMemory(`User: ${message}\nAgent: ${response}`, 'conversation', `Conversation with ${targetAgent.name}`, ['conversation', targetAgent.type, 'chat'], 5, {
                    agentId: targetAgent.id,
                    agentName: targetAgent.name,
                    timestamp: new Date().toISOString()
                });
            }
            targetAgent.status = 'idle';
            return response;
        }
        catch (error) {
            targetAgent.status = 'error';
            throw error;
        }
    }
    async createAgentViaService(name, type, parentId) {
        if (!this.apiService) {
            throw new Error('API service not initialized');
        }
        try {
            const agentInfo = await this.apiService.createAgent(name, type, parentId);
            const agent = {
                id: agentInfo.id,
                name: agentInfo.name,
                type: agentInfo.type,
                status: agentInfo.status,
                created: new Date(agentInfo.created),
                lastActivity: new Date(agentInfo.lastActivity),
                parentId: agentInfo.parentId,
                children: agentInfo.children,
                context: {}
            };
            this.agents.set(agent.id, agent);
            this.saveAgentsToState();
            return agent;
        }
        catch (error) {
            throw new Error(`Failed to create agent via service: ${error}`);
        }
    }
    async loadAgentsFromService() {
        if (!this.apiService) {
            return;
        }
        try {
            const agentsInfo = await this.apiService.getAgents();
            for (const agentInfo of agentsInfo) {
                const agent = {
                    id: agentInfo.id,
                    name: agentInfo.name,
                    type: agentInfo.type,
                    status: agentInfo.status,
                    created: new Date(agentInfo.created),
                    lastActivity: new Date(agentInfo.lastActivity),
                    parentId: agentInfo.parentId,
                    children: agentInfo.children,
                    context: {}
                };
                this.agents.set(agent.id, agent);
            }
        }
        catch (error) {
            console.error('Failed to load agents from service:', error);
        }
    }
    async loadAgentsFromState() {
        // Implementation to load agents from VS Code workspace state
        // This would restore agents between sessions
    }
    saveAgentsToState() {
        // Implementation to save agents to VS Code workspace state
        // This would persist agents between sessions
    }
    buildContextualSystemMessage(projectContext, originalSystemMessage) {
        if (!projectContext) {
            return originalSystemMessage || 'You are a helpful AI assistant.';
        }
        const contextualMessage = `
${originalSystemMessage || 'You are a helpful AI assistant.'}

CURRENT PROJECT CONTEXT:
${projectContext}

IMPORTANT INSTRUCTIONS:
- Always consider the current project context when providing responses
- Reference specific files, technologies, and patterns from the project when relevant
- Provide solutions that fit the project's architecture and conventions
- When suggesting code changes, consider the existing codebase structure
- If asked about the project, use the context information to provide accurate details
- Adapt your responses to match the project's technology stack and patterns

Remember: You are working within this specific project context. All your responses should be relevant to this codebase.
        `.trim();
        return contextualMessage;
    }
    async getProjectSummary() {
        if (!this.enhancedMemoryManager) {
            return 'Enhanced memory system not available.';
        }
        const projectContext = await this.enhancedMemoryManager.getProjectContext();
        if (!projectContext) {
            return 'No project context available. Please ensure a workspace is open.';
        }
        return projectContext;
    }
    async askAboutProject(question) {
        if (!this.enhancedMemoryManager) {
            return 'Enhanced memory system not available.';
        }
        const projectContext = await this.enhancedMemoryManager.getProjectContext();
        const relevantContext = await this.enhancedMemoryManager.getRelevantContext(question);
        const contextualQuestion = `
Based on the current project context, please answer this question: ${question}

Project Context:
${projectContext}

Relevant Information:
${relevantContext}
        `.trim();
        // Use the active agent or create a temporary one
        const activeAgent = this.getActiveAgent();
        if (activeAgent) {
            return await this.sendMessage(contextualQuestion, activeAgent.id);
        }
        else {
            // Use model manager directly
            return await this.modelManager.processMessage(contextualQuestion, {
                systemMessage: 'You are a helpful AI assistant with deep knowledge of the current project. Provide accurate, contextual responses based on the project information provided.'
            });
        }
    }
    dispose() {
        // Clean up resources
        this.agents.clear();
        this.activeAgent = null;
    }
}
exports.AgentManager = AgentManager;
//# sourceMappingURL=AgentManager.js.map