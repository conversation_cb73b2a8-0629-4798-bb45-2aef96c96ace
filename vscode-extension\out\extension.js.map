{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,qEAAkE;AAClE,yEAAsE;AACtE,uEAAoE;AACpE,sDAAmD;AACnD,sDAAmD;AACnD,oDAAiD;AACjD,wDAAqD;AACrD,wEAAqE;AACrE,8DAA2D;AAC3D,wEAAqE;AACrE,sEAAmE;AACnE,kEAA+D;AAC/D,sDAAmD;AAEnD,IAAI,iBAAoC,CAAC;AACzC,IAAI,mBAAwC,CAAC;AAC7C,IAAI,kBAAsC,CAAC;AAC3C,IAAI,YAA0B,CAAC;AAC/B,IAAI,YAA0B,CAAC;AAC/B,IAAI,WAAwB,CAAC;AAC7B,IAAI,aAA4B,CAAC;AACjC,IAAI,qBAA4C,CAAC;AACjD,IAAI,gBAAkC,CAAC;AACvC,IAAI,qBAA4C,CAAC;AACjD,IAAI,oBAA0C,CAAC;AAC/C,IAAI,gBAAkC,CAAC;AACvC,IAAI,UAAsB,CAAC;AAE3B,SAAgB,QAAQ,CAAC,OAAgC;IACrD,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;IAEnD,2BAA2B;IAC3B,oBAAoB,GAAG,IAAI,2CAAoB,EAAE,CAAC;IAClD,YAAY,GAAG,IAAI,2BAAY,CAAC,oBAAoB,CAAC,CAAC;IACtD,aAAa,GAAG,IAAI,6BAAa,CAAC,OAAO,CAAC,CAAC;IAC3C,WAAW,GAAG,IAAI,yBAAW,CAAC,OAAO,CAAC,CAAC;IAEvC,8BAA8B;IAC9B,qBAAqB,GAAG,IAAI,6CAAqB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;IACzE,qBAAqB,GAAG,IAAI,6CAAqB,CAAC,qBAAqB,EAAE,YAAY,CAAC,CAAC;IACvF,gBAAgB,GAAG,IAAI,mCAAgB,CAAC,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,qBAAqB,CAAC,CAAC;IAEnG,sBAAsB;IACtB,gBAAgB,GAAG,IAAI,mCAAgB,CAAC,oBAAoB,EAAE,OAAO,CAAC,CAAC;IACvE,UAAU,GAAG,IAAI,uBAAU,CAAC,gBAAgB,CAAC,CAAC;IAE9C,gDAAgD;IAChD,YAAY,GAAG,IAAI,2BAAY,CAAC,YAAY,EAAE,aAAa,EAAE,WAAW,EAAE,qBAAqB,CAAC,CAAC;IAEjG,uBAAuB;IACvB,iBAAiB,GAAG,IAAI,qCAAiB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;IACjE,mBAAmB,GAAG,IAAI,yCAAmB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;IACrE,kBAAkB,GAAG,IAAI,uCAAkB,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;IAEvE,+BAA+B;IAC/B,MAAM,CAAC,MAAM,CAAC,wBAAwB,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,CAAC;IAC/E,MAAM,CAAC,MAAM,CAAC,wBAAwB,CAAC,mBAAmB,EAAE,aAAa,CAAC,CAAC;IAC3E,MAAM,CAAC,MAAM,CAAC,wBAAwB,CAAC,4BAA4B,EAAE,qBAAqB,CAAC,CAAC;IAC5F,MAAM,CAAC,MAAM,CAAC,wBAAwB,CAAC,yBAAyB,EAAE,kBAAkB,CAAC,CAAC;IACtF,MAAM,CAAC,MAAM,CAAC,wBAAwB,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC;IAExE,4BAA4B;IAC5B,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,iBAAiB,EAAE,mBAAmB,CAAC,CACpF,CAAC;IAEF,oBAAoB;IACpB,gBAAgB,CAAC,OAAO,CAAC,CAAC;IAE1B,4CAA4C;IAC5C,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY,EAAE,oBAAoB,EAAE,IAAI,CAAC,CAAC;IAEzE,gCAAgC;IAChC,0BAA0B,EAAE,CAAC;AACjC,CAAC;AA9CD,4BA8CC;AAED,SAAS,gBAAgB,CAAC,OAAgC;IACtD,oBAAoB;IACpB,MAAM,eAAe,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,qBAAqB,EAAE,GAAG,EAAE;QAChF,mBAAmB,CAAC,IAAI,EAAE,CAAC;IAC/B,CAAC,CAAC,CAAC;IAEH,oBAAoB;IACpB,MAAM,eAAe,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,qBAAqB,EAAE,KAAK,IAAI,EAAE;QACtF,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YAC/C,MAAM,EAAE,kBAAkB;YAC1B,WAAW,EAAE,UAAU;SAC1B,CAAC,CAAC;QAEH,IAAI,SAAS,EAAE;YACX,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC;gBAChD,mBAAmB;gBACnB,gBAAgB;gBAChB,YAAY;gBACZ,cAAc;gBACd,QAAQ;aACX,EAAE;gBACC,WAAW,EAAE,mBAAmB;aACnC,CAAC,CAAC;YAEH,IAAI,SAAS,EAAE;gBACX,YAAY,CAAC,WAAW,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;gBAC/C,iBAAiB,CAAC,OAAO,EAAE,CAAC;gBAC5B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,UAAU,SAAS,yBAAyB,CAAC,CAAC;aACtF;SACJ;IACL,CAAC,CAAC,CAAC;IAEH,wBAAwB;IACxB,MAAM,mBAAmB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACxF,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,+BAA+B,EAAE,YAAY,CAAC,CAAC;IAClF,CAAC,CAAC,CAAC;IAEH,uBAAuB;IACvB,MAAM,kBAAkB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,wBAAwB,EAAE,KAAK,IAAI,EAAE;QAC5F,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,MAAM,EAAE;YACT,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,CAAC;YACzD,OAAO;SACV;QAED,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;QACnC,MAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;QAEhF,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE;YACd,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,CAAC;YAC5D,OAAO;SACV;QAED,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;QAC5C,MAAM,YAAY,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IACnD,CAAC,CAAC,CAAC;IAEH,uBAAuB;IACvB,MAAM,kBAAkB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,wBAAwB,EAAE,KAAK,EAAE,GAAgB,EAAE,EAAE;QAC5G,IAAI,OAAO,GAAG,GAAG,CAAC;QAElB,IAAI,CAAC,OAAO,EAAE;YACV,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;YAC9C,IAAI,MAAM,EAAE;gBACR,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC;aACjC;SACJ;QAED,IAAI,CAAC,OAAO,EAAE;YACV,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;YACnD,OAAO;SACV;QAED,MAAM,YAAY,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAC5C,CAAC,CAAC,CAAC;IAEH,wBAAwB;IACxB,MAAM,mBAAmB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,yBAAyB,EAAE,KAAK,IAAI,EAAE;QAC9F,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YAC5C,MAAM,EAAE,yCAAyC;YACjD,WAAW,EAAE,6CAA6C;SAC7D,CAAC,CAAC;QAEH,IAAI,MAAM,EAAE;YACR,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;YAC9C,MAAM,QAAQ,GAAG,MAAM,EAAE,QAAQ,CAAC,UAAU,IAAI,YAAY,CAAC;YAC7D,MAAM,YAAY,CAAC,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;SACrD;IACL,CAAC,CAAC,CAAC;IAEH,2BAA2B;IAC3B,MAAM,sBAAsB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,4BAA4B,EAAE,KAAK,IAAI,EAAE;QACpG,MAAM,qBAAqB,CAAC,mBAAmB,EAAE,CAAC;IACtD,CAAC,CAAC,CAAC;IAEH,MAAM,iBAAiB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,uBAAuB,EAAE,KAAK,IAAI,EAAE;QAC1F,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YAC7C,MAAM,EAAE,iCAAiC;YACzC,WAAW,EAAE,mBAAmB;SACnC,CAAC,CAAC;QAEH,IAAI,OAAO,EAAE;YACT,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gBAC3C,MAAM,EAAE,+BAA+B;gBACvC,WAAW,EAAE,iBAAiB;aACjC,CAAC,CAAC;YAEH,MAAM,qBAAqB,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;YAC/D,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,2BAA2B,CAAC,CAAC;SACrE;IACL,CAAC,CAAC,CAAC;IAEH,8BAA8B;IAC9B,MAAM,yBAAyB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,+BAA+B,EAAE,GAAG,EAAE;QACpG,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,+BAA+B,CAAC,CAAC;IACpE,CAAC,CAAC,CAAC;IAEH,2BAA2B;IAC3B,MAAM,yBAAyB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;QAC1G,IAAI;YACA,MAAM,OAAO,GAAG,MAAM,YAAY,CAAC,iBAAiB,EAAE,CAAC;YAEvD,yBAAyB;YACzB,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;gBAChD,OAAO,EAAE,OAAO;gBAChB,QAAQ,EAAE,UAAU;aACvB,CAAC,CAAC;YACH,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;SAC7C;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,kCAAkC,KAAK,EAAE,CAAC,CAAC;SAC7E;IACL,CAAC,CAAC,CAAC;IAEH,MAAM,sBAAsB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,4BAA4B,EAAE,KAAK,IAAI,EAAE;QACpG,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YAC9C,MAAM,EAAE,iDAAiD;YACzD,WAAW,EAAE,oFAAoF;SACpG,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ;YAAE,OAAO;QAEtB,IAAI;YACA,MAAM,MAAM,GAAG,MAAM,YAAY,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;YAE5D,yBAAyB;YACzB,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;gBAChD,OAAO,EAAE,aAAa,QAAQ,gBAAgB,MAAM,EAAE;gBACtD,QAAQ,EAAE,UAAU;aACvB,CAAC,CAAC;YACH,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;SAC7C;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,sCAAsC,KAAK,EAAE,CAAC,CAAC;SACjF;IACL,CAAC,CAAC,CAAC;IAEH,MAAM,4BAA4B,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;QAChH,IAAI;YACA,MAAM,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;YACpD,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,yCAAyC,CAAC,CAAC;SACnF;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,sCAAsC,KAAK,EAAE,CAAC,CAAC;SACjF;IACL,CAAC,CAAC,CAAC;IAEH,oCAAoC;IACpC,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,eAAe,EACf,eAAe,EACf,mBAAmB,EACnB,kBAAkB,EAClB,kBAAkB,EAClB,mBAAmB,EACnB,sBAAsB,EACtB,iBAAiB,EACjB,yBAAyB,EACzB,yBAAyB,EACzB,sBAAsB,EACtB,4BAA4B,CAC/B,CAAC;AACN,CAAC;AAED,KAAK,UAAU,0BAA0B;IACrC,IAAI;QACA,4CAA4C;QAC5C,MAAM,YAAY,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;QAC1C,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,8CAA8C,CAAC,CAAC;KACxF;IAAC,OAAO,KAAK,EAAE;QACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,4CAA4C,KAAK,EAAE,CAAC,CAAC;KACvF;AACL,CAAC;AAED,SAAgB,UAAU;IACtB,qBAAqB;IACrB,IAAI,YAAY,EAAE;QACd,YAAY,CAAC,OAAO,EAAE,CAAC;KAC1B;IACD,IAAI,YAAY,EAAE;QACd,YAAY,CAAC,OAAO,EAAE,CAAC;KAC1B;IACD,IAAI,WAAW,EAAE;QACb,WAAW,CAAC,OAAO,EAAE,CAAC;KACzB;IACD,IAAI,aAAa,EAAE;QACf,aAAa,CAAC,OAAO,EAAE,CAAC;KAC3B;IACD,IAAI,qBAAqB,EAAE;QACvB,qBAAqB,CAAC,OAAO,EAAE,CAAC;KACnC;IACD,IAAI,gBAAgB,EAAE;QAClB,gBAAgB,CAAC,OAAO,EAAE,CAAC;KAC9B;IACD,IAAI,qBAAqB,EAAE;QACvB,qBAAqB,CAAC,OAAO,EAAE,CAAC;KACnC;IACD,IAAI,UAAU,EAAE;QACZ,UAAU,CAAC,OAAO,EAAE,CAAC;KACxB;IACD,IAAI,gBAAgB,EAAE;QAClB,gBAAgB,CAAC,OAAO,EAAE,CAAC;KAC9B;AACL,CAAC;AA7BD,gCA6BC"}