import * as vscode from 'vscode';

export interface BrowserRequest {
    action: 'open' | 'navigate' | 'search' | 'extract' | 'screenshot' | 'close';
    url?: string;
    query?: string;
    selector?: string;
    options?: {
        waitForSelector?: string;
        timeout?: number;
        fullPage?: boolean;
        viewport?: { width: number; height: number };
    };
}

export interface BrowserResult {
    success: boolean;
    data?: any;
    error?: string;
    url?: string;
    title?: string;
    content?: string;
    screenshot?: string;
}

export class VSCodeBrowser {
    private outputChannel: vscode.OutputChannel;
    private webviewPanel: vscode.WebviewPanel | null = null;

    constructor() {
        this.outputChannel = vscode.window.createOutputChannel('Agent Zero Browser');
    }

    public async executeRequest(request: BrowserRequest): Promise<BrowserResult> {
        try {
            this.outputChannel.appendLine(`Executing browser action: ${request.action}`);

            switch (request.action) {
                case 'open':
                    return await this.openUrl(request.url || '');
                case 'navigate':
                    return await this.navigateToUrl(request.url || '');
                case 'search':
                    return await this.searchWeb(request.query || '');
                case 'extract':
                    return await this.extractContent(request.selector);
                case 'screenshot':
                    return await this.takeScreenshot(request.options);
                case 'close':
                    return await this.closeBrowser();
                default:
                    throw new Error(`Unsupported browser action: ${request.action}`);
            }
        } catch (error) {
            this.outputChannel.appendLine(`Browser action failed: ${error}`);
            return {
                success: false,
                error: error.toString()
            };
        }
    }

    private async openUrl(url: string): Promise<BrowserResult> {
        try {
            // Validate URL
            if (!url || !this.isValidUrl(url)) {
                throw new Error('Invalid URL provided');
            }

            // Create webview panel for browsing
            this.webviewPanel = vscode.window.createWebviewPanel(
                'agentZeroBrowser',
                'Agent Zero Browser',
                vscode.ViewColumn.Two,
                {
                    enableScripts: true,
                    retainContextWhenHidden: true,
                    enableFindWidget: true
                }
            );

            // Set up webview content
            this.webviewPanel.webview.html = this.getBrowserHtml(url);

            // Handle webview messages
            this.webviewPanel.webview.onDidReceiveMessage(
                (message) => {
                    this.handleWebviewMessage(message);
                }
            );

            // For external URLs, open in default browser as well
            if (url.startsWith('http://') || url.startsWith('https://')) {
                await vscode.env.openExternal(vscode.Uri.parse(url));
            }

            return {
                success: true,
                url: url,
                data: { message: 'Browser opened successfully' }
            };

        } catch (error) {
            return {
                success: false,
                error: `Failed to open URL: ${error}`
            };
        }
    }

    private async navigateToUrl(url: string): Promise<BrowserResult> {
        if (!this.webviewPanel) {
            return await this.openUrl(url);
        }

        try {
            // Update webview content
            this.webviewPanel.webview.html = this.getBrowserHtml(url);
            
            // Open in external browser as well
            if (url.startsWith('http://') || url.startsWith('https://')) {
                await vscode.env.openExternal(vscode.Uri.parse(url));
            }

            return {
                success: true,
                url: url,
                data: { message: 'Navigation successful' }
            };

        } catch (error) {
            return {
                success: false,
                error: `Failed to navigate: ${error}`
            };
        }
    }

    private async searchWeb(query: string): Promise<BrowserResult> {
        try {
            // Use DuckDuckGo as default search engine
            const searchUrl = `https://duckduckgo.com/?q=${encodeURIComponent(query)}`;
            
            const result = await this.openUrl(searchUrl);
            
            if (result.success) {
                result.data = {
                    ...result.data,
                    query: query,
                    searchEngine: 'DuckDuckGo'
                };
            }

            return result;

        } catch (error) {
            return {
                success: false,
                error: `Failed to search: ${error}`
            };
        }
    }

    private async extractContent(selector?: string): Promise<BrowserResult> {
        if (!this.webviewPanel) {
            return {
                success: false,
                error: 'No browser session active'
            };
        }

        try {
            // Send message to webview to extract content
            this.webviewPanel.webview.postMessage({
                command: 'extractContent',
                selector: selector
            });

            // Note: In a real implementation, you would need to set up
            // a proper communication mechanism to get the extracted content
            // back from the webview. For now, we'll return a placeholder.

            return {
                success: true,
                content: 'Content extraction initiated. Check webview for results.',
                data: { selector: selector }
            };

        } catch (error) {
            return {
                success: false,
                error: `Failed to extract content: ${error}`
            };
        }
    }

    private async takeScreenshot(options?: any): Promise<BrowserResult> {
        // Note: VS Code webviews don't support taking screenshots directly.
        // This would require integration with a headless browser like Puppeteer.
        
        return {
            success: false,
            error: 'Screenshot functionality requires headless browser integration'
        };
    }

    private async closeBrowser(): Promise<BrowserResult> {
        try {
            if (this.webviewPanel) {
                this.webviewPanel.dispose();
                this.webviewPanel = null;
            }

            return {
                success: true,
                data: { message: 'Browser closed successfully' }
            };

        } catch (error) {
            return {
                success: false,
                error: `Failed to close browser: ${error}`
            };
        }
    }

    private getBrowserHtml(url: string): string {
        return `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Agent Zero Browser</title>
            <style>
                body {
                    margin: 0;
                    padding: 0;
                    font-family: var(--vscode-font-family);
                    background-color: var(--vscode-editor-background);
                    color: var(--vscode-foreground);
                }
                .browser-header {
                    display: flex;
                    align-items: center;
                    padding: 10px;
                    background-color: var(--vscode-titleBar-activeBackground);
                    border-bottom: 1px solid var(--vscode-panel-border);
                }
                .url-bar {
                    flex: 1;
                    padding: 5px 10px;
                    margin: 0 10px;
                    border: 1px solid var(--vscode-input-border);
                    background-color: var(--vscode-input-background);
                    color: var(--vscode-input-foreground);
                    border-radius: 3px;
                }
                .nav-button {
                    padding: 5px 10px;
                    margin: 0 2px;
                    background-color: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                    border: none;
                    border-radius: 3px;
                    cursor: pointer;
                }
                .nav-button:hover {
                    background-color: var(--vscode-button-hoverBackground);
                }
                .browser-content {
                    height: calc(100vh - 60px);
                    width: 100%;
                    border: none;
                }
                .info-message {
                    padding: 20px;
                    text-align: center;
                    background-color: var(--vscode-editor-inactiveSelectionBackground);
                    margin: 20px;
                    border-radius: 5px;
                }
            </style>
        </head>
        <body>
            <div class="browser-header">
                <button class="nav-button" onclick="goBack()">←</button>
                <button class="nav-button" onclick="goForward()">→</button>
                <button class="nav-button" onclick="refresh()">⟳</button>
                <input type="text" class="url-bar" id="urlBar" value="${url}" onkeypress="handleUrlKeyPress(event)">
                <button class="nav-button" onclick="navigate()">Go</button>
                <button class="nav-button" onclick="openExternal()">🔗</button>
            </div>
            
            <div class="info-message">
                <h3>Agent Zero Browser</h3>
                <p>Current URL: <strong>${url}</strong></p>
                <p>This is a simplified browser interface. For full browsing experience, the URL has been opened in your default browser.</p>
                <p>Use the controls above to navigate or click the link button (🔗) to open in external browser.</p>
            </div>

            <script>
                const vscode = acquireVsCodeApi();
                
                function navigate() {
                    const url = document.getElementById('urlBar').value;
                    vscode.postMessage({
                        command: 'navigate',
                        url: url
                    });
                }
                
                function handleUrlKeyPress(event) {
                    if (event.key === 'Enter') {
                        navigate();
                    }
                }
                
                function goBack() {
                    vscode.postMessage({ command: 'back' });
                }
                
                function goForward() {
                    vscode.postMessage({ command: 'forward' });
                }
                
                function refresh() {
                    vscode.postMessage({ command: 'refresh' });
                }
                
                function openExternal() {
                    const url = document.getElementById('urlBar').value;
                    vscode.postMessage({
                        command: 'openExternal',
                        url: url
                    });
                }
                
                function extractContent(selector) {
                    // This would extract content from the page
                    // In a real implementation, this would work with the actual page content
                    vscode.postMessage({
                        command: 'contentExtracted',
                        content: 'Extracted content would appear here',
                        selector: selector
                    });
                }
                
                // Listen for messages from extension
                window.addEventListener('message', event => {
                    const message = event.data;
                    switch (message.command) {
                        case 'extractContent':
                            extractContent(message.selector);
                            break;
                    }
                });
            </script>
        </body>
        </html>`;
    }

    private handleWebviewMessage(message: any): void {
        switch (message.command) {
            case 'navigate':
                this.navigateToUrl(message.url);
                break;
            case 'openExternal':
                vscode.env.openExternal(vscode.Uri.parse(message.url));
                break;
            case 'contentExtracted':
                this.outputChannel.appendLine(`Content extracted: ${message.content}`);
                break;
            default:
                this.outputChannel.appendLine(`Unknown webview message: ${message.command}`);
        }
    }

    private isValidUrl(url: string): boolean {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    }

    public async openUrlInExternalBrowser(url: string): Promise<BrowserResult> {
        try {
            await vscode.env.openExternal(vscode.Uri.parse(url));
            return {
                success: true,
                url: url,
                data: { message: 'URL opened in external browser' }
            };
        } catch (error) {
            return {
                success: false,
                error: `Failed to open external browser: ${error}`
            };
        }
    }

    public dispose(): void {
        if (this.webviewPanel) {
            this.webviewPanel.dispose();
            this.webviewPanel = null;
        }
        this.outputChannel.dispose();
    }
}
