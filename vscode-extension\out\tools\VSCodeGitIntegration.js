"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VSCodeGitIntegration = void 0;
const vscode = __importStar(require("vscode"));
const child_process_1 = require("child_process");
class VSCodeGitIntegration {
    constructor() {
        this.outputChannel = vscode.window.createOutputChannel('Agent Zero Git');
        this.gitExtension = vscode.extensions.getExtension('vscode.git');
    }
    async executeGitOperation(operation) {
        try {
            this.outputChannel.appendLine(`Executing Git operation: ${operation.command}`);
            // Try to use VS Code Git extension first
            if (this.gitExtension && this.gitExtension.isActive) {
                const result = await this.executeViaGitExtension(operation);
                if (result) {
                    return result;
                }
            }
            // Fallback to command line git
            return await this.executeViaCommandLine(operation);
        }
        catch (error) {
            this.outputChannel.appendLine(`Git operation failed: ${error}`);
            return {
                success: false,
                output: '',
                error: error.toString()
            };
        }
    }
    async executeViaGitExtension(operation) {
        try {
            const gitApi = this.gitExtension?.exports.getAPI(1);
            if (!gitApi) {
                return null;
            }
            const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
            if (!workspaceFolder) {
                throw new Error('No workspace folder found');
            }
            const repository = gitApi.getRepository(workspaceFolder.uri);
            if (!repository) {
                throw new Error('No Git repository found in workspace');
            }
            switch (operation.command) {
                case 'status':
                    return await this.getStatusViaExtension(repository);
                case 'add':
                    return await this.addFilesViaExtension(repository, operation.files || []);
                case 'commit':
                    return await this.commitViaExtension(repository, operation.message || '');
                case 'push':
                    return await this.pushViaExtension(repository);
                case 'pull':
                    return await this.pullViaExtension(repository);
                case 'branch':
                    return await this.getBranchesViaExtension(repository);
                case 'checkout':
                    return await this.checkoutViaExtension(repository, operation.branch || '');
                default:
                    return null; // Fallback to command line
            }
        }
        catch (error) {
            this.outputChannel.appendLine(`Git extension operation failed: ${error}`);
            return null;
        }
    }
    async getStatusViaExtension(repository) {
        try {
            const state = repository.state;
            const status = {
                branch: state.HEAD?.name || 'unknown',
                ahead: state.HEAD?.ahead || 0,
                behind: state.HEAD?.behind || 0,
                staged: state.indexChanges.map((change) => change.uri.fsPath),
                modified: state.workingTreeChanges.map((change) => change.uri.fsPath),
                untracked: state.untrackedChanges?.map((change) => change.uri.fsPath) || [],
                conflicted: state.mergeChanges?.map((change) => change.uri.fsPath) || []
            };
            return {
                success: true,
                output: JSON.stringify(status, null, 2),
                data: status
            };
        }
        catch (error) {
            throw new Error(`Failed to get status: ${error}`);
        }
    }
    async addFilesViaExtension(repository, files) {
        try {
            if (files.length === 0) {
                // Add all changes
                await repository.add([]);
            }
            else {
                // Add specific files
                const uris = files.map(file => vscode.Uri.file(file));
                await repository.add(uris);
            }
            return {
                success: true,
                output: `Added ${files.length || 'all'} files to staging area`
            };
        }
        catch (error) {
            throw new Error(`Failed to add files: ${error}`);
        }
    }
    async commitViaExtension(repository, message) {
        try {
            if (!message) {
                throw new Error('Commit message is required');
            }
            await repository.commit(message);
            return {
                success: true,
                output: `Committed with message: "${message}"`
            };
        }
        catch (error) {
            throw new Error(`Failed to commit: ${error}`);
        }
    }
    async pushViaExtension(repository) {
        try {
            await repository.push();
            return {
                success: true,
                output: 'Successfully pushed to remote repository'
            };
        }
        catch (error) {
            throw new Error(`Failed to push: ${error}`);
        }
    }
    async pullViaExtension(repository) {
        try {
            await repository.pull();
            return {
                success: true,
                output: 'Successfully pulled from remote repository'
            };
        }
        catch (error) {
            throw new Error(`Failed to pull: ${error}`);
        }
    }
    async getBranchesViaExtension(repository) {
        try {
            const refs = repository.state.refs;
            const branches = refs.filter((ref) => ref.type === 0); // Local branches
            const remoteBranches = refs.filter((ref) => ref.type === 1); // Remote branches
            const branchInfo = {
                current: repository.state.HEAD?.name,
                local: branches.map((branch) => branch.name),
                remote: remoteBranches.map((branch) => branch.name)
            };
            return {
                success: true,
                output: JSON.stringify(branchInfo, null, 2),
                data: branchInfo
            };
        }
        catch (error) {
            throw new Error(`Failed to get branches: ${error}`);
        }
    }
    async checkoutViaExtension(repository, branch) {
        try {
            await repository.checkout(branch);
            return {
                success: true,
                output: `Checked out branch: ${branch}`
            };
        }
        catch (error) {
            throw new Error(`Failed to checkout branch: ${error}`);
        }
    }
    async executeViaCommandLine(operation) {
        return new Promise((resolve) => {
            const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
            const cwd = workspaceFolder ? workspaceFolder.uri.fsPath : process.cwd();
            let args = [];
            switch (operation.command) {
                case 'status':
                    args = ['status', '--porcelain', '-b'];
                    break;
                case 'add':
                    args = ['add', ...(operation.files || ['.'])];
                    break;
                case 'commit':
                    args = ['commit', '-m', operation.message || 'Automated commit'];
                    break;
                case 'push':
                    args = ['push', operation.remote || 'origin', operation.branch || 'HEAD'];
                    break;
                case 'pull':
                    args = ['pull', operation.remote || 'origin', operation.branch || 'HEAD'];
                    break;
                case 'branch':
                    args = ['branch', '-a'];
                    break;
                case 'checkout':
                    args = ['checkout', operation.branch || 'main'];
                    break;
                case 'diff':
                    args = ['diff', ...(operation.files || [])];
                    break;
                case 'log':
                    args = ['log', '--oneline', '-10'];
                    break;
                case 'clone':
                    args = ['clone', operation.url || '', operation.args?.[0] || ''];
                    break;
                default:
                    args = [operation.command, ...(operation.args || [])];
            }
            const process = (0, child_process_1.spawn)('git', args, { cwd });
            let stdout = '';
            let stderr = '';
            process.stdout?.on('data', (data) => {
                stdout += data.toString();
            });
            process.stderr?.on('data', (data) => {
                stderr += data.toString();
            });
            process.on('close', (code) => {
                const success = code === 0;
                resolve({
                    success,
                    output: stdout,
                    error: success ? undefined : stderr,
                    data: success ? this.parseGitOutput(operation.command, stdout) : undefined
                });
            });
            process.on('error', (error) => {
                resolve({
                    success: false,
                    output: '',
                    error: error.toString()
                });
            });
        });
    }
    parseGitOutput(command, output) {
        switch (command) {
            case 'status':
                return this.parseStatusOutput(output);
            case 'branch':
                return this.parseBranchOutput(output);
            case 'log':
                return this.parseLogOutput(output);
            default:
                return { raw: output };
        }
    }
    parseStatusOutput(output) {
        const lines = output.split('\n').filter(line => line.trim());
        const branchLine = lines[0];
        const branch = branchLine.match(/## (.+?)(?:\.\.\.|$)/)?.[1] || 'unknown';
        const staged = [];
        const modified = [];
        const untracked = [];
        const conflicted = [];
        for (let i = 1; i < lines.length; i++) {
            const line = lines[i];
            const status = line.substring(0, 2);
            const file = line.substring(3);
            if (status.includes('U') || status.includes('A') || status.includes('D')) {
                conflicted.push(file);
            }
            else if (status[0] !== ' ') {
                staged.push(file);
            }
            else if (status[1] !== ' ') {
                modified.push(file);
            }
            else if (status === '??') {
                untracked.push(file);
            }
        }
        return {
            branch,
            ahead: 0,
            behind: 0,
            staged,
            modified,
            untracked,
            conflicted
        };
    }
    parseBranchOutput(output) {
        const lines = output.split('\n').filter(line => line.trim());
        const local = [];
        const remote = [];
        let current = '';
        for (const line of lines) {
            const trimmed = line.trim();
            if (trimmed.startsWith('* ')) {
                current = trimmed.substring(2);
                local.push(current);
            }
            else if (trimmed.startsWith('remotes/')) {
                remote.push(trimmed.substring(8));
            }
            else if (trimmed && !trimmed.startsWith('remotes/')) {
                local.push(trimmed);
            }
        }
        return { current, local, remote };
    }
    parseLogOutput(output) {
        const lines = output.split('\n').filter(line => line.trim());
        return {
            commits: lines.map(line => {
                const [hash, ...messageParts] = line.split(' ');
                return {
                    hash: hash,
                    message: messageParts.join(' ')
                };
            })
        };
    }
    async showGitStatus() {
        const result = await this.executeGitOperation({ command: 'status' });
        if (result.success && result.data) {
            const status = result.data;
            const panel = vscode.window.createWebviewPanel('gitStatus', 'Git Status', vscode.ViewColumn.Two, { enableScripts: true });
            panel.webview.html = this.getGitStatusHtml(status);
        }
        else {
            vscode.window.showErrorMessage(`Failed to get Git status: ${result.error}`);
        }
    }
    getGitStatusHtml(status) {
        return `
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body { font-family: var(--vscode-font-family); padding: 20px; }
                .status-section { margin-bottom: 20px; }
                .file-list { list-style: none; padding: 0; }
                .file-item { padding: 5px; margin: 2px 0; border-radius: 3px; }
                .staged { background-color: var(--vscode-gitDecoration-addedResourceForeground); }
                .modified { background-color: var(--vscode-gitDecoration-modifiedResourceForeground); }
                .untracked { background-color: var(--vscode-gitDecoration-untrackedResourceForeground); }
                .conflicted { background-color: var(--vscode-gitDecoration-conflictingResourceForeground); }
            </style>
        </head>
        <body>
            <h1>Git Status</h1>
            <div class="status-section">
                <h2>Branch: ${status.branch}</h2>
                <p>Ahead: ${status.ahead} | Behind: ${status.behind}</p>
            </div>
            
            ${this.renderFileSection('Staged Changes', status.staged, 'staged')}
            ${this.renderFileSection('Modified Files', status.modified, 'modified')}
            ${this.renderFileSection('Untracked Files', status.untracked, 'untracked')}
            ${this.renderFileSection('Conflicted Files', status.conflicted, 'conflicted')}
        </body>
        </html>`;
    }
    renderFileSection(title, files, className) {
        if (files.length === 0) {
            return '';
        }
        const fileItems = files.map(file => `<li class="file-item ${className}">${file}</li>`).join('');
        return `
        <div class="status-section">
            <h3>${title} (${files.length})</h3>
            <ul class="file-list">${fileItems}</ul>
        </div>`;
    }
    dispose() {
        this.outputChannel.dispose();
    }
}
exports.VSCodeGitIntegration = VSCodeGitIntegration;
//# sourceMappingURL=VSCodeGitIntegration.js.map