"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VSCodeExecutor = void 0;
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
const child_process_1 = require("child_process");
class VSCodeExecutor {
    constructor() {
        this.runningProcesses = new Map();
        this.executionCounter = 0;
        this.languageConfigs = {
            python: {
                command: 'python',
                args: ['-c'],
                fileExtension: '.py',
                requiresFile: false,
                supportsStdin: true
            },
            javascript: {
                command: 'node',
                args: ['-e'],
                fileExtension: '.js',
                requiresFile: false,
                supportsStdin: true
            },
            typescript: {
                command: 'ts-node',
                args: ['-e'],
                fileExtension: '.ts',
                requiresFile: false,
                supportsStdin: true
            },
            bash: {
                command: 'bash',
                args: ['-c'],
                fileExtension: '.sh',
                requiresFile: false,
                supportsStdin: true
            },
            powershell: {
                command: 'powershell',
                args: ['-Command'],
                fileExtension: '.ps1',
                requiresFile: false,
                supportsStdin: true
            },
            java: {
                command: 'java',
                args: [],
                fileExtension: '.java',
                requiresFile: true,
                supportsStdin: false
            },
            cpp: {
                command: 'g++',
                args: ['-o', 'temp_executable'],
                fileExtension: '.cpp',
                requiresFile: true,
                supportsStdin: false
            },
            c: {
                command: 'gcc',
                args: ['-o', 'temp_executable'],
                fileExtension: '.c',
                requiresFile: true,
                supportsStdin: false
            },
            go: {
                command: 'go',
                args: ['run'],
                fileExtension: '.go',
                requiresFile: true,
                supportsStdin: false
            },
            rust: {
                command: 'rustc',
                args: ['--edition', '2021', '-o', 'temp_executable'],
                fileExtension: '.rs',
                requiresFile: true,
                supportsStdin: false
            }
        };
        this.outputChannel = vscode.window.createOutputChannel('Agent Zero Code Executor');
    }
    async executeCode(request) {
        const startTime = Date.now();
        const executionId = ++this.executionCounter;
        try {
            this.outputChannel.appendLine(`[${executionId}] Executing ${request.language} code`);
            this.outputChannel.appendLine(`[${executionId}] Code:\n${request.code}`);
            const config = this.languageConfigs[request.language.toLowerCase()];
            if (!config) {
                throw new Error(`Unsupported language: ${request.language}`);
            }
            let result;
            if (config.requiresFile) {
                result = await this.executeWithFile(request, config, executionId);
            }
            else {
                result = await this.executeDirectly(request, config, executionId);
            }
            result.executionTime = Date.now() - startTime;
            this.outputChannel.appendLine(`[${executionId}] Execution completed in ${result.executionTime}ms`);
            return result;
        }
        catch (error) {
            const executionTime = Date.now() - startTime;
            this.outputChannel.appendLine(`[${executionId}] Execution failed: ${error}`);
            return {
                success: false,
                output: '',
                error: error.toString(),
                executionTime
            };
        }
    }
    async executeDirectly(request, config, executionId) {
        return new Promise((resolve) => {
            const workingDir = request.workingDirectory || this.getWorkspaceRoot();
            const timeout = request.timeout || 30000;
            const env = { ...process.env, ...request.environment };
            let args = [...config.args];
            if (config.supportsStdin) {
                args.push(request.code);
            }
            const process = (0, child_process_1.spawn)(config.command, args, {
                cwd: workingDir,
                env: env,
                stdio: ['pipe', 'pipe', 'pipe']
            });
            this.runningProcesses.set(executionId, process);
            let stdout = '';
            let stderr = '';
            process.stdout?.on('data', (data) => {
                stdout += data.toString();
            });
            process.stderr?.on('data', (data) => {
                stderr += data.toString();
            });
            const timeoutHandle = setTimeout(() => {
                process.kill('SIGTERM');
                resolve({
                    success: false,
                    output: stdout,
                    error: 'Execution timeout',
                    executionTime: timeout
                });
            }, timeout);
            process.on('close', (code) => {
                clearTimeout(timeoutHandle);
                this.runningProcesses.delete(executionId);
                resolve({
                    success: code === 0,
                    output: stdout,
                    error: code !== 0 ? stderr : undefined,
                    exitCode: code || 0,
                    executionTime: 0 // Will be set by caller
                });
            });
            process.on('error', (error) => {
                clearTimeout(timeoutHandle);
                this.runningProcesses.delete(executionId);
                resolve({
                    success: false,
                    output: stdout,
                    error: error.toString(),
                    executionTime: 0 // Will be set by caller
                });
            });
            // Send code via stdin if needed
            if (!config.supportsStdin && process.stdin) {
                process.stdin.write(request.code);
                process.stdin.end();
            }
        });
    }
    async executeWithFile(request, config, executionId) {
        const workingDir = request.workingDirectory || this.getWorkspaceRoot();
        const tempFileName = `temp_${executionId}${config.fileExtension}`;
        const tempFilePath = path.join(workingDir, tempFileName);
        try {
            // Write code to temporary file
            const uri = vscode.Uri.file(tempFilePath);
            const encoder = new TextEncoder();
            await vscode.workspace.fs.writeFile(uri, encoder.encode(request.code));
            // Compile if necessary
            if (this.needsCompilation(request.language)) {
                const compileResult = await this.compileFile(tempFilePath, config, executionId);
                if (!compileResult.success) {
                    return compileResult;
                }
            }
            // Execute the file
            const executeResult = await this.executeFile(tempFilePath, config, executionId, request);
            return executeResult;
        }
        finally {
            // Clean up temporary files
            await this.cleanupTempFiles(workingDir, executionId);
        }
    }
    async compileFile(filePath, config, executionId) {
        return new Promise((resolve) => {
            const workingDir = path.dirname(filePath);
            const args = [...config.args, filePath];
            const process = (0, child_process_1.spawn)(config.command, args, {
                cwd: workingDir,
                stdio: ['pipe', 'pipe', 'pipe']
            });
            let stdout = '';
            let stderr = '';
            process.stdout?.on('data', (data) => {
                stdout += data.toString();
            });
            process.stderr?.on('data', (data) => {
                stderr += data.toString();
            });
            process.on('close', (code) => {
                resolve({
                    success: code === 0,
                    output: stdout,
                    error: code !== 0 ? stderr : undefined,
                    exitCode: code || 0,
                    executionTime: 0
                });
            });
        });
    }
    async executeFile(filePath, config, executionId, request) {
        return new Promise((resolve) => {
            const workingDir = path.dirname(filePath);
            let command;
            let args;
            if (this.needsCompilation(request.language)) {
                // Execute compiled binary
                command = path.join(workingDir, 'temp_executable');
                args = request.args || [];
            }
            else {
                // Execute with interpreter
                command = config.command;
                args = [filePath, ...(request.args || [])];
            }
            const process = (0, child_process_1.spawn)(command, args, {
                cwd: workingDir,
                env: { ...process.env, ...request.environment },
                stdio: ['pipe', 'pipe', 'pipe']
            });
            this.runningProcesses.set(executionId, process);
            let stdout = '';
            let stderr = '';
            process.stdout?.on('data', (data) => {
                stdout += data.toString();
            });
            process.stderr?.on('data', (data) => {
                stderr += data.toString();
            });
            const timeout = request.timeout || 30000;
            const timeoutHandle = setTimeout(() => {
                process.kill('SIGTERM');
                resolve({
                    success: false,
                    output: stdout,
                    error: 'Execution timeout',
                    executionTime: timeout
                });
            }, timeout);
            process.on('close', (code) => {
                clearTimeout(timeoutHandle);
                this.runningProcesses.delete(executionId);
                resolve({
                    success: code === 0,
                    output: stdout,
                    error: code !== 0 ? stderr : undefined,
                    exitCode: code || 0,
                    executionTime: 0
                });
            });
            process.on('error', (error) => {
                clearTimeout(timeoutHandle);
                this.runningProcesses.delete(executionId);
                resolve({
                    success: false,
                    output: stdout,
                    error: error.toString(),
                    executionTime: 0
                });
            });
        });
    }
    needsCompilation(language) {
        return ['cpp', 'c', 'rust', 'java'].includes(language.toLowerCase());
    }
    async cleanupTempFiles(workingDir, executionId) {
        try {
            const filesToClean = [
                `temp_${executionId}.py`,
                `temp_${executionId}.js`,
                `temp_${executionId}.ts`,
                `temp_${executionId}.cpp`,
                `temp_${executionId}.c`,
                `temp_${executionId}.java`,
                `temp_${executionId}.go`,
                `temp_${executionId}.rs`,
                `temp_${executionId}.sh`,
                `temp_${executionId}.ps1`,
                'temp_executable',
                'temp_executable.exe'
            ];
            for (const fileName of filesToClean) {
                try {
                    const uri = vscode.Uri.file(path.join(workingDir, fileName));
                    await vscode.workspace.fs.delete(uri);
                }
                catch {
                    // File might not exist, ignore
                }
            }
        }
        catch (error) {
            this.outputChannel.appendLine(`Failed to cleanup temp files: ${error}`);
        }
    }
    getWorkspaceRoot() {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        return workspaceFolder ? workspaceFolder.uri.fsPath : process.cwd();
    }
    async killExecution(executionId) {
        const process = this.runningProcesses.get(executionId);
        if (process) {
            process.kill('SIGTERM');
            this.runningProcesses.delete(executionId);
            return true;
        }
        return false;
    }
    getRunningExecutions() {
        return Array.from(this.runningProcesses.keys());
    }
    dispose() {
        // Kill all running processes
        for (const [id, process] of this.runningProcesses) {
            process.kill('SIGTERM');
        }
        this.runningProcesses.clear();
        this.outputChannel.dispose();
    }
}
exports.VSCodeExecutor = VSCodeExecutor;
//# sourceMappingURL=VSCodeExecutor.js.map