"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentZeroService = void 0;
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
const child_process_1 = require("child_process");
class AgentZeroService {
    constructor(configManager, context) {
        this.configManager = configManager;
        this.context = context;
        this.process = null;
        this.status = { running: false };
        this.disposables = [];
        this.outputChannel = vscode.window.createOutputChannel('Agent Zero Service');
        this.config = this.getServiceConfig();
        // Listen for configuration changes
        this.disposables.push(vscode.workspace.onDidChangeConfiguration(e => {
            if (e.affectsConfiguration('agent-zero')) {
                this.config = this.getServiceConfig();
            }
        }));
    }
    getServiceConfig() {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        const agentZeroPath = workspaceFolder ?
            path.join(workspaceFolder.uri.fsPath) :
            path.join(__dirname, '..', '..', '..');
        return {
            pythonPath: this.configManager.get('service.pythonPath', 'python'),
            agentZeroPath: this.configManager.get('service.agentZeroPath', agentZeroPath),
            port: this.configManager.get('service.port', 50001),
            host: this.configManager.get('service.host', 'localhost'),
            autoStart: this.configManager.get('service.autoStart', true)
        };
    }
    async start() {
        if (this.status.running) {
            this.outputChannel.appendLine('Agent Zero service is already running');
            return true;
        }
        try {
            this.outputChannel.appendLine('Starting Agent Zero service...');
            this.outputChannel.show(true);
            // Check if Agent Zero files exist
            const runUiPath = path.join(this.config.agentZeroPath, 'run_ui.py');
            if (!await this.fileExists(runUiPath)) {
                throw new Error(`Agent Zero not found at: ${this.config.agentZeroPath}`);
            }
            // Prepare environment variables
            const env = { ...process.env };
            const agentConfig = this.configManager.getConfiguration();
            // Set model configurations as environment variables
            if (agentConfig.models.chat.provider === 'openai') {
                env.OPENAI_API_KEY = env.OPENAI_API_KEY || '';
            }
            if (agentConfig.models.chat.provider === 'anthropic') {
                env.ANTHROPIC_API_KEY = env.ANTHROPIC_API_KEY || '';
            }
            if (agentConfig.models.chat.provider === 'gemini') {
                env.GOOGLE_API_KEY = env.GOOGLE_API_KEY || '';
            }
            // Set Agent Zero specific environment variables
            env.WEB_UI_PORT = this.config.port.toString();
            env.WEB_UI_HOST = this.config.host;
            env.AGENT_PROMPTS_SUBDIR = agentConfig.agent.prompts;
            env.AGENT_MEMORY_SUBDIR = agentConfig.agent.memory;
            env.AGENT_KNOWLEDGE_SUBDIR = agentConfig.agent.knowledge;
            // Start the Python process
            this.process = (0, child_process_1.spawn)(this.config.pythonPath, ['run_ui.py'], {
                cwd: this.config.agentZeroPath,
                env: env,
                stdio: ['pipe', 'pipe', 'pipe']
            });
            // Handle process events
            this.setupProcessHandlers();
            // Wait for service to be ready
            const isReady = await this.waitForService();
            if (isReady) {
                this.status = {
                    running: true,
                    port: this.config.port,
                    pid: this.process.pid,
                    startTime: new Date()
                };
                this.outputChannel.appendLine(`Agent Zero service started successfully on port ${this.config.port}`);
                return true;
            }
            else {
                throw new Error('Service failed to start within timeout period');
            }
        }
        catch (error) {
            this.outputChannel.appendLine(`Failed to start Agent Zero service: ${error}`);
            this.status = { running: false, error: error.toString() };
            return false;
        }
    }
    async stop() {
        if (!this.status.running || !this.process) {
            this.outputChannel.appendLine('Agent Zero service is not running');
            return true;
        }
        try {
            this.outputChannel.appendLine('Stopping Agent Zero service...');
            // Gracefully terminate the process
            this.process.kill('SIGTERM');
            // Wait for process to exit
            await new Promise((resolve) => {
                const timeout = setTimeout(() => {
                    if (this.process) {
                        this.process.kill('SIGKILL');
                    }
                    resolve();
                }, 5000);
                this.process.on('exit', () => {
                    clearTimeout(timeout);
                    resolve();
                });
            });
            this.status = { running: false };
            this.process = null;
            this.outputChannel.appendLine('Agent Zero service stopped');
            return true;
        }
        catch (error) {
            this.outputChannel.appendLine(`Failed to stop Agent Zero service: ${error}`);
            return false;
        }
    }
    async restart() {
        this.outputChannel.appendLine('Restarting Agent Zero service...');
        await this.stop();
        await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
        return await this.start();
    }
    getStatus() {
        return { ...this.status };
    }
    getServiceUrl() {
        return `http://${this.config.host}:${this.config.port}`;
    }
    setupProcessHandlers() {
        if (!this.process)
            return;
        this.process.stdout?.on('data', (data) => {
            const output = data.toString();
            this.outputChannel.append(output);
            // Check for startup success indicators
            if (output.includes('Running on') || output.includes('Server started')) {
                this.status.running = true;
            }
        });
        this.process.stderr?.on('data', (data) => {
            const error = data.toString();
            this.outputChannel.append(`ERROR: ${error}`);
        });
        this.process.on('exit', (code, signal) => {
            this.outputChannel.appendLine(`Agent Zero service exited with code ${code}, signal ${signal}`);
            this.status = { running: false };
            this.process = null;
        });
        this.process.on('error', (error) => {
            this.outputChannel.appendLine(`Agent Zero service error: ${error}`);
            this.status = { running: false, error: error.toString() };
            this.process = null;
        });
    }
    async waitForService(timeout = 30000) {
        const startTime = Date.now();
        while (Date.now() - startTime < timeout) {
            try {
                // Try to connect to the service
                const response = await fetch(`${this.getServiceUrl()}/health`, {
                    method: 'GET',
                    signal: AbortSignal.timeout(1000)
                });
                if (response.ok) {
                    return true;
                }
            }
            catch (error) {
                // Service not ready yet, continue waiting
            }
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        return false;
    }
    async fileExists(filePath) {
        try {
            await vscode.workspace.fs.stat(vscode.Uri.file(filePath));
            return true;
        }
        catch {
            return false;
        }
    }
    async makeApiRequest(endpoint, method = 'GET', data) {
        if (!this.status.running) {
            throw new Error('Agent Zero service is not running');
        }
        try {
            const url = `${this.getServiceUrl()}${endpoint}`;
            const options = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                },
                signal: AbortSignal.timeout(30000) // 30 second timeout
            };
            if (data && (method === 'POST' || method === 'PUT')) {
                options.body = JSON.stringify(data);
            }
            const response = await fetch(url, options);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return await response.json();
            }
            else {
                return await response.text();
            }
        }
        catch (error) {
            this.outputChannel.appendLine(`API request failed: ${error}`);
            throw error;
        }
    }
    dispose() {
        this.stop();
        this.disposables.forEach(d => d.dispose());
        this.outputChannel.dispose();
    }
}
exports.AgentZeroService = AgentZeroService;
//# sourceMappingURL=AgentZeroService.js.map