import * as vscode from 'vscode';
import { AgentZeroService } from './AgentZeroService';
import { WebSocketService, MessageTypes, WebSocketMessage } from './WebSocketService';

export interface ChatMessage {
    id: string;
    content: string;
    sender: 'user' | 'agent' | 'system';
    timestamp: string;
    agentId?: string;
}

export interface AgentInfo {
    id: string;
    name: string;
    type: string;
    status: 'idle' | 'working' | 'error';
    created: string;
    lastActivity: string;
    parentId?: string;
    children: string[];
}

export interface CodeExecutionRequest {
    code: string;
    language: string;
    agentId?: string;
}

export interface CodeExecutionResult {
    success: boolean;
    output: string;
    error?: string;
    executionTime?: number;
}

export interface FileOperation {
    path: string;
    content?: string;
    operation: 'read' | 'write' | 'delete' | 'list';
}

export interface MemoryOperation {
    query?: string;
    content?: string;
    type?: string;
    tags?: string[];
    operation: 'save' | 'load' | 'search' | 'delete';
}

export class ApiService {
    private agentZeroService: AgentZeroService;
    private webSocketService: WebSocketService | null = null;
    private outputChannel: vscode.OutputChannel;
    private messageCallbacks = new Map<string, (response: any) => void>();

    constructor(agentZeroService: AgentZeroService) {
        this.agentZeroService = agentZeroService;
        this.outputChannel = vscode.window.createOutputChannel('Agent Zero API');
        this.initializeWebSocket();
    }

    private initializeWebSocket(): void {
        const serviceUrl = this.agentZeroService.getServiceUrl();
        const wsUrl = serviceUrl.replace('http://', 'ws://') + '/ws';
        
        this.webSocketService = new WebSocketService({
            url: wsUrl,
            reconnectInterval: 5000,
            maxReconnectAttempts: 10,
            heartbeatInterval: 30000
        });

        // Set up message handlers
        this.setupWebSocketHandlers();
    }

    private setupWebSocketHandlers(): void {
        if (!this.webSocketService) return;

        // Chat responses
        this.webSocketService.onMessage(MessageTypes.CHAT_RESPONSE, (data) => {
            this.handleChatResponse(data);
        });

        // Agent status updates
        this.webSocketService.onMessage(MessageTypes.AGENT_STATUS, (data) => {
            this.handleAgentStatusUpdate(data);
        });

        // Code execution results
        this.webSocketService.onMessage(MessageTypes.CODE_RESULT, (data) => {
            this.handleCodeExecutionResult(data);
        });

        // System errors
        this.webSocketService.onMessage(MessageTypes.SYSTEM_ERROR, (data) => {
            this.handleSystemError(data);
        });

        // System logs
        this.webSocketService.onMessage(MessageTypes.SYSTEM_LOG, (data) => {
            this.outputChannel.appendLine(`[SYSTEM] ${data.message}`);
        });
    }

    public async initialize(): Promise<boolean> {
        try {
            // Start Agent Zero service if not running
            const serviceStatus = this.agentZeroService.getStatus();
            if (!serviceStatus.running) {
                const started = await this.agentZeroService.start();
                if (!started) {
                    throw new Error('Failed to start Agent Zero service');
                }
            }

            // Connect WebSocket
            if (this.webSocketService) {
                await this.webSocketService.connect();
            }

            this.outputChannel.appendLine('API Service initialized successfully');
            return true;

        } catch (error) {
            this.outputChannel.appendLine(`Failed to initialize API Service: ${error}`);
            return false;
        }
    }

    public async sendChatMessage(message: string, agentId?: string): Promise<string> {
        try {
            // Try WebSocket first for real-time communication
            if (this.webSocketService && this.webSocketService.getConnectionStatus().connected) {
                return await this.sendChatMessageViaWebSocket(message, agentId);
            } else {
                // Fallback to HTTP API
                return await this.sendChatMessageViaHttp(message, agentId);
            }
        } catch (error) {
            this.outputChannel.appendLine(`Failed to send chat message: ${error}`);
            throw error;
        }
    }

    private async sendChatMessageViaWebSocket(message: string, agentId?: string): Promise<string> {
        return new Promise((resolve, reject) => {
            const messageId = this.generateMessageId();
            
            // Set up response handler
            this.messageCallbacks.set(messageId, (response) => {
                if (response.success) {
                    resolve(response.content);
                } else {
                    reject(new Error(response.error || 'Unknown error'));
                }
            });

            // Send message
            const wsMessage: WebSocketMessage = {
                type: MessageTypes.CHAT_MESSAGE,
                data: {
                    message,
                    agentId,
                    messageId
                },
                id: messageId
            };

            const sent = this.webSocketService!.send(wsMessage);
            if (!sent) {
                this.messageCallbacks.delete(messageId);
                reject(new Error('Failed to send message via WebSocket'));
            }

            // Set timeout
            setTimeout(() => {
                if (this.messageCallbacks.has(messageId)) {
                    this.messageCallbacks.delete(messageId);
                    reject(new Error('Message timeout'));
                }
            }, 30000);
        });
    }

    private async sendChatMessageViaHttp(message: string, agentId?: string): Promise<string> {
        const response = await this.agentZeroService.makeApiRequest('/api/chat', 'POST', {
            message,
            agentId
        });

        if (response.success) {
            return response.content;
        } else {
            throw new Error(response.error || 'Unknown error');
        }
    }

    public async executeCode(request: CodeExecutionRequest): Promise<CodeExecutionResult> {
        try {
            const response = await this.agentZeroService.makeApiRequest('/api/execute', 'POST', request);
            return response;
        } catch (error) {
            this.outputChannel.appendLine(`Code execution failed: ${error}`);
            throw error;
        }
    }

    public async performFileOperation(operation: FileOperation): Promise<any> {
        try {
            const response = await this.agentZeroService.makeApiRequest('/api/files', 'POST', operation);
            return response;
        } catch (error) {
            this.outputChannel.appendLine(`File operation failed: ${error}`);
            throw error;
        }
    }

    public async performMemoryOperation(operation: MemoryOperation): Promise<any> {
        try {
            const response = await this.agentZeroService.makeApiRequest('/api/memory', 'POST', operation);
            return response;
        } catch (error) {
            this.outputChannel.appendLine(`Memory operation failed: ${error}`);
            throw error;
        }
    }

    public async getAgents(): Promise<AgentInfo[]> {
        try {
            const response = await this.agentZeroService.makeApiRequest('/api/agents', 'GET');
            return response.agents || [];
        } catch (error) {
            this.outputChannel.appendLine(`Failed to get agents: ${error}`);
            throw error;
        }
    }

    public async createAgent(name: string, type: string, parentId?: string): Promise<AgentInfo> {
        try {
            const response = await this.agentZeroService.makeApiRequest('/api/agents', 'POST', {
                name,
                type,
                parentId
            });
            return response.agent;
        } catch (error) {
            this.outputChannel.appendLine(`Failed to create agent: ${error}`);
            throw error;
        }
    }

    public async deleteAgent(agentId: string): Promise<boolean> {
        try {
            const response = await this.agentZeroService.makeApiRequest(`/api/agents/${agentId}`, 'DELETE');
            return response.success;
        } catch (error) {
            this.outputChannel.appendLine(`Failed to delete agent: ${error}`);
            throw error;
        }
    }

    public async getAgentStatus(agentId: string): Promise<AgentInfo> {
        try {
            const response = await this.agentZeroService.makeApiRequest(`/api/agents/${agentId}`, 'GET');
            return response.agent;
        } catch (error) {
            this.outputChannel.appendLine(`Failed to get agent status: ${error}`);
            throw error;
        }
    }

    public async getTools(): Promise<any[]> {
        try {
            const response = await this.agentZeroService.makeApiRequest('/api/tools', 'GET');
            return response.tools || [];
        } catch (error) {
            this.outputChannel.appendLine(`Failed to get tools: ${error}`);
            throw error;
        }
    }

    public async executeTool(toolId: string, parameters: any): Promise<any> {
        try {
            const response = await this.agentZeroService.makeApiRequest('/api/tools/execute', 'POST', {
                toolId,
                parameters
            });
            return response;
        } catch (error) {
            this.outputChannel.appendLine(`Tool execution failed: ${error}`);
            throw error;
        }
    }

    public async getSystemStatus(): Promise<any> {
        try {
            const response = await this.agentZeroService.makeApiRequest('/api/status', 'GET');
            return response;
        } catch (error) {
            this.outputChannel.appendLine(`Failed to get system status: ${error}`);
            throw error;
        }
    }

    private handleChatResponse(data: any): void {
        const messageId = data.messageId;
        if (messageId && this.messageCallbacks.has(messageId)) {
            const callback = this.messageCallbacks.get(messageId)!;
            this.messageCallbacks.delete(messageId);
            callback(data);
        }
    }

    private handleAgentStatusUpdate(data: any): void {
        // Emit event for agent status updates
        vscode.commands.executeCommand('agent-zero.agentStatusUpdated', data);
    }

    private handleCodeExecutionResult(data: any): void {
        // Emit event for code execution results
        vscode.commands.executeCommand('agent-zero.codeExecutionResult', data);
    }

    private handleSystemError(data: any): void {
        this.outputChannel.appendLine(`[ERROR] ${data.message}`);
        vscode.window.showErrorMessage(`Agent Zero Error: ${data.message}`);
    }

    private generateMessageId(): string {
        return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    public getConnectionStatus(): {
        serviceRunning: boolean;
        webSocketConnected: boolean;
        serviceUrl: string;
    } {
        const serviceStatus = this.agentZeroService.getStatus();
        const wsStatus = this.webSocketService?.getConnectionStatus();

        return {
            serviceRunning: serviceStatus.running,
            webSocketConnected: wsStatus?.connected || false,
            serviceUrl: this.agentZeroService.getServiceUrl()
        };
    }

    public dispose(): void {
        if (this.webSocketService) {
            this.webSocketService.dispose();
        }
        this.messageCallbacks.clear();
        this.outputChannel.dispose();
    }
}
