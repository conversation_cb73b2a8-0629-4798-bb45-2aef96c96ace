/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */

export const pkceNotCreated = "pkce_not_created";
export const earJwkEmpty = "ear_jwk_empty";
export const earJweEmpty = "ear_jwe_empty";
export const cryptoNonExistent = "crypto_nonexistent";
export const emptyNavigateUri = "empty_navigate_uri";
export const hashEmptyError = "hash_empty_error";
export const noStateInHash = "no_state_in_hash";
export const hashDoesNotContainKnownProperties =
    "hash_does_not_contain_known_properties";
export const unableToParseState = "unable_to_parse_state";
export const stateInteractionTypeMismatch = "state_interaction_type_mismatch";
export const interactionInProgress = "interaction_in_progress";
export const popupWindowError = "popup_window_error";
export const emptyWindowError = "empty_window_error";
export const userCancelled = "user_cancelled";
export const monitorPopupTimeout = "monitor_popup_timeout";
export const monitorWindowTimeout = "monitor_window_timeout";
export const redirectInIframe = "redirect_in_iframe";
export const blockIframeReload = "block_iframe_reload";
export const blockNestedPopups = "block_nested_popups";
export const iframeClosedPrematurely = "iframe_closed_prematurely";
export const silentLogoutUnsupported = "silent_logout_unsupported";
export const noAccountError = "no_account_error";
export const silentPromptValueError = "silent_prompt_value_error";
export const noTokenRequestCacheError = "no_token_request_cache_error";
export const unableToParseTokenRequestCacheError =
    "unable_to_parse_token_request_cache_error";
export const authRequestNotSetError = "auth_request_not_set_error";
export const invalidCacheType = "invalid_cache_type";
export const nonBrowserEnvironment = "non_browser_environment";
export const databaseNotOpen = "database_not_open";
export const noNetworkConnectivity = "no_network_connectivity";
export const postRequestFailed = "post_request_failed";
export const getRequestFailed = "get_request_failed";
export const failedToParseResponse = "failed_to_parse_response";
export const unableToLoadToken = "unable_to_load_token";
export const cryptoKeyNotFound = "crypto_key_not_found";
export const authCodeRequired = "auth_code_required";
export const authCodeOrNativeAccountIdRequired =
    "auth_code_or_nativeAccountId_required";
export const spaCodeAndNativeAccountIdPresent =
    "spa_code_and_nativeAccountId_present";
export const databaseUnavailable = "database_unavailable";
export const unableToAcquireTokenFromNativePlatform =
    "unable_to_acquire_token_from_native_platform";
export const nativeHandshakeTimeout = "native_handshake_timeout";
export const nativeExtensionNotInstalled = "native_extension_not_installed";
export const nativeConnectionNotEstablished =
    "native_connection_not_established";
export const uninitializedPublicClientApplication =
    "uninitialized_public_client_application";
export const nativePromptNotSupported = "native_prompt_not_supported";
export const invalidBase64String = "invalid_base64_string";
export const invalidPopTokenRequest = "invalid_pop_token_request";
export const failedToBuildHeaders = "failed_to_build_headers";
export const failedToParseHeaders = "failed_to_parse_headers";
export const failedToDecryptEarResponse = "failed_to_decrypt_ear_response";
export const timedOut = "timed_out";
