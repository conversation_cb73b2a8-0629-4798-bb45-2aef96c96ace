"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolManager = void 0;
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
const VSCodeFileEditor_1 = require("../tools/VSCodeFileEditor");
const VSCodeExecutor_1 = require("../tools/VSCodeExecutor");
const VSCodeBrowser_1 = require("../tools/VSCodeBrowser");
const VSCodeSearchEngine_1 = require("../tools/VSCodeSearchEngine");
const VSCodeGitIntegration_1 = require("../tools/VSCodeGitIntegration");
const VSCodeTerminal_1 = require("../tools/VSCodeTerminal");
class ToolManager {
    constructor(context) {
        this.context = context;
        this._onDidChangeTreeData = new vscode.EventEmitter();
        this.onDidChangeTreeData = this._onDidChangeTreeData.event;
        this.tools = new Map();
        // Initialize enhanced tools
        this.fileEditor = new VSCodeFileEditor_1.VSCodeFileEditor();
        this.executor = new VSCodeExecutor_1.VSCodeExecutor();
        this.browser = new VSCodeBrowser_1.VSCodeBrowser();
        this.searchEngine = new VSCodeSearchEngine_1.VSCodeSearchEngine();
        this.gitIntegration = new VSCodeGitIntegration_1.VSCodeGitIntegration();
        this.terminal = new VSCodeTerminal_1.VSCodeTerminal();
        this.initializeTools();
    }
    initializeTools() {
        // Initialize built-in tools
        const builtInTools = [
            {
                id: 'file-editor',
                name: 'File Editor',
                description: 'Read, write, and modify files',
                category: 'File Operations',
                enabled: true
            },
            {
                id: 'code-execution',
                name: 'Code Execution',
                description: 'Execute code in various languages',
                category: 'Development',
                enabled: true
            },
            {
                id: 'terminal',
                name: 'Terminal',
                description: 'Execute terminal commands',
                category: 'System',
                enabled: true
            },
            {
                id: 'browser',
                name: 'Browser',
                description: 'Web browsing and scraping with VS Code integration',
                category: 'Web',
                enabled: true
            },
            {
                id: 'search-engine',
                name: 'Search Engine',
                description: 'Search the web and workspace for information',
                category: 'Web',
                enabled: true
            },
            {
                id: 'git-integration',
                name: 'Git Integration',
                description: 'Git version control operations',
                category: 'Development',
                enabled: true
            },
            {
                id: 'terminal',
                name: 'Terminal',
                description: 'Terminal and command execution',
                category: 'System',
                enabled: true
            },
            {
                id: 'document-query',
                name: 'Document Query',
                description: 'Query and analyze documents',
                category: 'Analysis',
                enabled: true
            },
            {
                id: 'memory-save',
                name: 'Memory Save',
                description: 'Save information to memory',
                category: 'Memory',
                enabled: true
            },
            {
                id: 'memory-load',
                name: 'Memory Load',
                description: 'Load information from memory',
                category: 'Memory',
                enabled: true
            },
            {
                id: 'vision-load',
                name: 'Vision Load',
                description: 'Analyze images and visual content',
                category: 'AI',
                enabled: true
            }
        ];
        builtInTools.forEach(tool => {
            this.tools.set(tool.id, tool);
        });
        this.loadCustomTools();
    }
    loadCustomTools() {
        // Load custom tools from the workspace
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (workspaceFolder) {
            const toolsPath = path.join(workspaceFolder.uri.fsPath, '.agent-zero', 'tools');
            if (fs.existsSync(toolsPath)) {
                // Load custom tools from the tools directory
                // Implementation would scan for tool definition files
            }
        }
    }
    async executeCode(code, language) {
        try {
            const result = await this.executor.executeCode({
                code,
                language,
                timeout: 30000
            });
            return {
                success: result.success,
                output: result.output,
                error: result.error,
                data: {
                    executionTime: result.executionTime,
                    exitCode: result.exitCode
                }
            };
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: `Failed to execute code: ${error}`
            };
        }
    }
    async readFile(filePath) {
        try {
            const result = await this.fileEditor.executeOperation({
                type: 'read',
                path: filePath
            });
            return {
                success: result.success,
                output: result.content || '',
                error: result.error,
                data: result.metadata
            };
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: `Failed to read file: ${error}`
            };
        }
    }
    async writeFile(filePath, content) {
        try {
            const result = await this.fileEditor.executeOperation({
                type: 'write',
                path: filePath,
                content
            });
            return {
                success: result.success,
                output: result.success ? `File written successfully: ${filePath}` : '',
                error: result.error,
                data: result.metadata
            };
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: `Failed to write file: ${error}`
            };
        }
    }
    async searchFiles(pattern, includePattern) {
        try {
            const files = await vscode.workspace.findFiles(includePattern || '**/*', '**/node_modules/**');
            const matchingFiles = files.filter(file => file.fsPath.toLowerCase().includes(pattern.toLowerCase()));
            return {
                success: true,
                output: `Found ${matchingFiles.length} matching files`,
                data: {
                    files: matchingFiles.map(f => f.fsPath),
                    count: matchingFiles.length
                }
            };
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: `Failed to search files: ${error}`
            };
        }
    }
    async executeTool(toolId, parameters) {
        const tool = this.tools.get(toolId);
        if (!tool) {
            return {
                success: false,
                output: '',
                error: `Tool not found: ${toolId}`
            };
        }
        if (!tool.enabled) {
            return {
                success: false,
                output: '',
                error: `Tool is disabled: ${toolId}`
            };
        }
        try {
            switch (toolId) {
                case 'file-editor':
                    return await this.handleFileEditor(parameters);
                case 'code-execution':
                    return await this.executeCode(parameters.code, parameters.language);
                case 'terminal':
                    return await this.handleTerminal(parameters);
                case 'browser':
                    return await this.handleBrowser(parameters);
                case 'search-engine':
                    return await this.handleSearch(parameters);
                case 'git-integration':
                    return await this.handleGit(parameters);
                default:
                    return {
                        success: false,
                        output: '',
                        error: `Tool implementation not found: ${toolId}`
                    };
            }
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: `Tool execution failed: ${error}`
            };
        }
    }
    async handleFileEditor(parameters) {
        const { action, path: filePath, content } = parameters;
        switch (action) {
            case 'read':
                return await this.readFile(filePath);
            case 'write':
                return await this.writeFile(filePath, content);
            default:
                return {
                    success: false,
                    output: '',
                    error: `Unknown file editor action: ${action}`
                };
        }
    }
    async handleTerminal(parameters) {
        try {
            const { action, command, terminalId, name } = parameters;
            switch (action) {
                case 'execute':
                    const result = await this.terminal.executeCommand({
                        command: command,
                        interactive: true
                    });
                    return {
                        success: result.success,
                        output: result.output || '',
                        error: result.error,
                        data: { terminalId: result.terminalId }
                    };
                case 'create':
                    const newTerminalId = await this.terminal.createTerminal(name);
                    return {
                        success: true,
                        output: `Terminal created: ${newTerminalId}`,
                        data: { terminalId: newTerminalId }
                    };
                case 'switch':
                    const switched = await this.terminal.switchToTerminal(terminalId);
                    return {
                        success: switched,
                        output: switched ? `Switched to terminal: ${terminalId}` : 'Failed to switch terminal'
                    };
                case 'close':
                    const closed = await this.terminal.closeTerminal(terminalId);
                    return {
                        success: closed,
                        output: closed ? `Terminal closed: ${terminalId}` : 'Failed to close terminal'
                    };
                default:
                    throw new Error(`Unknown terminal action: ${action}`);
            }
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: `Terminal operation failed: ${error}`
            };
        }
    }
    async handleBrowser(parameters) {
        try {
            const result = await this.browser.executeRequest(parameters);
            return {
                success: result.success,
                output: result.data ? JSON.stringify(result.data, null, 2) : '',
                error: result.error,
                data: result
            };
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: `Browser operation failed: ${error}`
            };
        }
    }
    async handleSearch(parameters) {
        try {
            const { query, engine, maxResults, searchType } = parameters;
            let result;
            if (searchType === 'workspace') {
                result = await this.searchEngine.searchInWorkspace(query, parameters.filePattern);
            }
            else {
                result = await this.searchEngine.search({
                    query,
                    engine,
                    maxResults
                });
            }
            return {
                success: result.success,
                output: JSON.stringify(result.results, null, 2),
                error: result.error,
                data: result
            };
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: `Search operation failed: ${error}`
            };
        }
    }
    async handleGit(parameters) {
        try {
            const result = await this.gitIntegration.executeGitOperation(parameters);
            return {
                success: result.success,
                output: result.output,
                error: result.error,
                data: result.data
            };
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: `Git operation failed: ${error}`
            };
        }
    }
    // TreeDataProvider implementation
    getTreeItem(element) {
        const item = new vscode.TreeItem(element.name, vscode.TreeItemCollapsibleState.None);
        item.description = element.description;
        item.tooltip = `${element.name}: ${element.description}`;
        item.contextValue = element.enabled ? 'enabledTool' : 'disabledTool';
        item.iconPath = new vscode.ThemeIcon(element.enabled ? 'tools' : 'circle-slash');
        return item;
    }
    getChildren(element) {
        if (!element) {
            return Promise.resolve(Array.from(this.tools.values()));
        }
        return Promise.resolve([]);
    }
    refresh() {
        this._onDidChangeTreeData.fire();
    }
    dispose() {
        this.tools.clear();
        // Dispose all enhanced tools
        this.fileEditor.dispose();
        this.executor.dispose();
        this.browser.dispose();
        this.searchEngine.dispose();
        this.gitIntegration.dispose();
        this.terminal.dispose();
    }
}
exports.ToolManager = ToolManager;
//# sourceMappingURL=ToolManager.js.map