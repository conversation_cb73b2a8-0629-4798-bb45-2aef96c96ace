import * as vscode from 'vscode';
import { AgentZeroProvider } from './providers/AgentZeroProvider';
import { ChatWebviewProvider } from './providers/ChatWebviewProvider';
import { MultiAgentProvider } from './providers/MultiAgentProvider';
import { AgentManager } from './core/AgentManager';
import { ModelManager } from './core/ModelManager';
import { ToolManager } from './core/ToolManager';
import { MemoryManager } from './core/MemoryManager';
import { EnhancedMemoryManager } from './core/EnhancedMemoryManager';
import { MultiAgentSystem } from './core/MultiAgentSystem';
import { KnowledgeSearchEngine } from './core/KnowledgeSearchEngine';
import { ConfigurationManager } from './core/ConfigurationManager';
import { AgentZeroService } from './services/AgentZeroService';
import { ApiService } from './services/ApiService';

let agentZeroProvider: AgentZeroProvider;
let chatWebviewProvider: ChatWebviewProvider;
let multiAgentProvider: MultiAgentProvider;
let agentManager: AgentManager;
let modelManager: ModelManager;
let toolManager: ToolManager;
let memoryManager: MemoryManager;
let enhancedMemoryManager: EnhancedMemoryManager;
let multiAgentSystem: MultiAgentSystem;
let knowledgeSearchEngine: KnowledgeSearchEngine;
let configurationManager: ConfigurationManager;
let agentZeroService: AgentZeroService;
let apiService: ApiService;

export function activate(context: vscode.ExtensionContext) {
    console.log('Agent Zero extension is now active!');

    // Initialize core managers
    configurationManager = new ConfigurationManager();
    modelManager = new ModelManager(configurationManager);
    memoryManager = new MemoryManager(context);
    toolManager = new ToolManager(context);

    // Initialize enhanced systems
    enhancedMemoryManager = new EnhancedMemoryManager(context, modelManager);
    knowledgeSearchEngine = new KnowledgeSearchEngine(enhancedMemoryManager, modelManager);
    multiAgentSystem = new MultiAgentSystem(context, modelManager, toolManager, enhancedMemoryManager);

    // Initialize services
    agentZeroService = new AgentZeroService(configurationManager, context);
    apiService = new ApiService(agentZeroService);

    // Initialize agent manager with API service
    agentManager = new AgentManager(modelManager, memoryManager, toolManager);

    // Initialize providers
    agentZeroProvider = new AgentZeroProvider(context, agentManager);
    chatWebviewProvider = new ChatWebviewProvider(context, agentManager);
    multiAgentProvider = new MultiAgentProvider(context, multiAgentSystem);

    // Register tree data providers
    vscode.window.registerTreeDataProvider('agent-zero-agents', agentZeroProvider);
    vscode.window.registerTreeDataProvider('agent-zero-memory', memoryManager);
    vscode.window.registerTreeDataProvider('agent-zero-enhanced-memory', enhancedMemoryManager);
    vscode.window.registerTreeDataProvider('agent-zero-multi-agents', multiAgentProvider);
    vscode.window.registerTreeDataProvider('agent-zero-tools', toolManager);

    // Register webview provider
    context.subscriptions.push(
        vscode.window.registerWebviewViewProvider('agent-zero-chat', chatWebviewProvider)
    );

    // Register commands
    registerCommands(context);

    // Set context for when extension is enabled
    vscode.commands.executeCommand('setContext', 'agent-zero:enabled', true);

    // Initialize Agent Zero backend
    initializeAgentZeroBackend();
}

function registerCommands(context: vscode.ExtensionContext) {
    // Open Chat command
    const openChatCommand = vscode.commands.registerCommand('agent-zero.openChat', () => {
        chatWebviewProvider.show();
    });

    // New Agent command
    const newAgentCommand = vscode.commands.registerCommand('agent-zero.newAgent', async () => {
        const agentName = await vscode.window.showInputBox({
            prompt: 'Enter agent name',
            placeHolder: 'My Agent'
        });
        
        if (agentName) {
            const agentType = await vscode.window.showQuickPick([
                'General Assistant',
                'Code Developer',
                'Researcher',
                'Data Analyst',
                'Custom'
            ], {
                placeHolder: 'Select agent type'
            });
            
            if (agentType) {
                agentManager.createAgent(agentName, agentType);
                agentZeroProvider.refresh();
                vscode.window.showInformationMessage(`Agent "${agentName}" created successfully!`);
            }
        }
    });

    // Show Settings command
    const showSettingsCommand = vscode.commands.registerCommand('agent-zero.showSettings', () => {
        vscode.commands.executeCommand('workbench.action.openSettings', 'agent-zero');
    });

    // Execute Code command
    const executeCodeCommand = vscode.commands.registerCommand('agent-zero.executeCode', async () => {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showErrorMessage('No active editor found');
            return;
        }

        const selection = editor.selection;
        const code = editor.document.getText(selection.isEmpty ? undefined : selection);
        
        if (!code.trim()) {
            vscode.window.showErrorMessage('No code selected or found');
            return;
        }

        const language = editor.document.languageId;
        await agentManager.executeCode(code, language);
    });

    // Analyze File command
    const analyzeFileCommand = vscode.commands.registerCommand('agent-zero.analyzeFile', async (uri?: vscode.Uri) => {
        let fileUri = uri;
        
        if (!fileUri) {
            const editor = vscode.window.activeTextEditor;
            if (editor) {
                fileUri = editor.document.uri;
            }
        }
        
        if (!fileUri) {
            vscode.window.showErrorMessage('No file selected');
            return;
        }

        await agentManager.analyzeFile(fileUri);
    });

    // Generate Code command
    const generateCodeCommand = vscode.commands.registerCommand('agent-zero.generateCode', async () => {
        const prompt = await vscode.window.showInputBox({
            prompt: 'Describe what code you want to generate',
            placeHolder: 'e.g., Create a function that sorts an array'
        });
        
        if (prompt) {
            const editor = vscode.window.activeTextEditor;
            const language = editor?.document.languageId || 'javascript';
            await agentManager.generateCode(prompt, language);
        }
    });

    // Enhanced memory commands
    const searchKnowledgeCommand = vscode.commands.registerCommand('agent-zero.searchKnowledge', async () => {
        await knowledgeSearchEngine.showSearchInterface();
    });

    const saveMemoryCommand = vscode.commands.registerCommand('agent-zero.saveMemory', async () => {
        const content = await vscode.window.showInputBox({
            prompt: 'Enter content to save to memory',
            placeHolder: 'Memory content...'
        });

        if (content) {
            const title = await vscode.window.showInputBox({
                prompt: 'Enter memory title (optional)',
                placeHolder: 'Memory title...'
            });

            await enhancedMemoryManager.saveMemory(content, 'fact', title);
            vscode.window.showInformationMessage('Memory saved successfully');
        }
    });

    // Multi-agent system commands
    const showAgentDashboardCommand = vscode.commands.registerCommand('agent-zero.showAgentDashboard', () => {
        vscode.commands.executeCommand('agent-zero.showAgentDashboard');
    });

    // Add all commands to subscriptions
    context.subscriptions.push(
        openChatCommand,
        newAgentCommand,
        showSettingsCommand,
        executeCodeCommand,
        analyzeFileCommand,
        generateCodeCommand,
        searchKnowledgeCommand,
        saveMemoryCommand,
        showAgentDashboardCommand
    );
}

async function initializeAgentZeroBackend() {
    try {
        // Initialize the Agent Zero backend service
        await agentManager.initialize(apiService);
        vscode.window.showInformationMessage('Agent Zero backend initialized successfully!');
    } catch (error) {
        vscode.window.showErrorMessage(`Failed to initialize Agent Zero backend: ${error}`);
    }
}

export function deactivate() {
    // Clean up resources
    if (agentManager) {
        agentManager.dispose();
    }
    if (modelManager) {
        modelManager.dispose();
    }
    if (toolManager) {
        toolManager.dispose();
    }
    if (memoryManager) {
        memoryManager.dispose();
    }
    if (enhancedMemoryManager) {
        enhancedMemoryManager.dispose();
    }
    if (multiAgentSystem) {
        multiAgentSystem.dispose();
    }
    if (knowledgeSearchEngine) {
        knowledgeSearchEngine.dispose();
    }
    if (apiService) {
        apiService.dispose();
    }
    if (agentZeroService) {
        agentZeroService.dispose();
    }
}
