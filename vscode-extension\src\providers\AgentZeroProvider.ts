import * as vscode from 'vscode';
import { Agent, AgentManager } from '../core/AgentManager';

export class AgentZeroProvider implements vscode.TreeDataProvider<Agent> {
    private _onDidChangeTreeData: vscode.EventEmitter<Agent | undefined | null | void> = new vscode.EventEmitter<Agent | undefined | null | void>();
    readonly onDidChangeTreeData: vscode.Event<Agent | undefined | null | void> = this._onDidChangeTreeData.event;

    constructor(
        private context: vscode.ExtensionContext,
        private agentManager: AgentManager
    ) {
        this.registerCommands();
    }

    private registerCommands(): void {
        // Register agent-specific commands
        const activateAgentCommand = vscode.commands.registerCommand('agent-zero.activateAgent', (agent: Agent) => {
            this.agentManager.setActiveAgent(agent.id);
            this.refresh();
            vscode.window.showInformationMessage(`Activated agent: ${agent.name}`);
        });

        const deleteAgentCommand = vscode.commands.registerCommand('agent-zero.deleteAgent', async (agent: Agent) => {
            const confirmation = await vscode.window.showWarningMessage(
                `Are you sure you want to delete agent "${agent.name}"?`,
                { modal: true },
                'Delete'
            );

            if (confirmation === 'Delete') {
                await this.agentManager.deleteAgent(agent.id);
                this.refresh();
                vscode.window.showInformationMessage(`Deleted agent: ${agent.name}`);
            }
        });

        const renameAgentCommand = vscode.commands.registerCommand('agent-zero.renameAgent', async (agent: Agent) => {
            const newName = await vscode.window.showInputBox({
                prompt: 'Enter new agent name',
                value: agent.name,
                validateInput: (value) => {
                    if (!value || value.trim().length === 0) {
                        return 'Agent name cannot be empty';
                    }
                    return null;
                }
            });

            if (newName && newName !== agent.name) {
                agent.name = newName;
                this.refresh();
                vscode.window.showInformationMessage(`Renamed agent to: ${newName}`);
            }
        });

        const createSubAgentCommand = vscode.commands.registerCommand('agent-zero.createSubAgent', async (parentAgent: Agent) => {
            const agentName = await vscode.window.showInputBox({
                prompt: 'Enter sub-agent name',
                placeHolder: 'Sub Agent'
            });
            
            if (agentName) {
                const agentType = await vscode.window.showQuickPick([
                    'Code Developer',
                    'Researcher',
                    'Data Analyst',
                    'File Manager',
                    'Web Scraper',
                    'Custom'
                ], {
                    placeHolder: 'Select sub-agent type'
                });
                
                if (agentType) {
                    this.agentManager.createAgent(agentName, agentType, parentAgent.id);
                    this.refresh();
                    vscode.window.showInformationMessage(`Sub-agent "${agentName}" created under "${parentAgent.name}"`);
                }
            }
        });

        const viewAgentDetailsCommand = vscode.commands.registerCommand('agent-zero.viewAgentDetails', (agent: Agent) => {
            this.showAgentDetails(agent);
        });

        // Add commands to context subscriptions
        this.context.subscriptions.push(
            activateAgentCommand,
            deleteAgentCommand,
            renameAgentCommand,
            createSubAgentCommand,
            viewAgentDetailsCommand
        );
    }

    private async showAgentDetails(agent: Agent): Promise<void> {
        const panel = vscode.window.createWebviewPanel(
            'agentDetails',
            `Agent Details: ${agent.name}`,
            vscode.ViewColumn.Two,
            {
                enableScripts: true,
                retainContextWhenHidden: true
            }
        );

        panel.webview.html = this.getAgentDetailsHtml(agent);

        // Handle messages from the webview
        panel.webview.onDidReceiveMessage(
            async (message) => {
                switch (message.command) {
                    case 'updateAgent':
                        // Handle agent updates
                        break;
                    case 'sendMessage':
                        try {
                            const response = await this.agentManager.sendMessage(message.text, agent.id);
                            panel.webview.postMessage({
                                command: 'messageResponse',
                                response: response
                            });
                        } catch (error) {
                            panel.webview.postMessage({
                                command: 'messageError',
                                error: String(error)
                            });
                        }
                        break;
                }
            },
            undefined,
            this.context.subscriptions
        );
    }

    private getAgentDetailsHtml(agent: Agent): string {
        const activeAgent = this.agentManager.getActiveAgent();
        const isActive = activeAgent?.id === agent.id;

        return `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Agent Details</title>
            <style>
                body {
                    font-family: var(--vscode-font-family);
                    color: var(--vscode-foreground);
                    background-color: var(--vscode-editor-background);
                    padding: 20px;
                    margin: 0;
                }
                .agent-header {
                    display: flex;
                    align-items: center;
                    margin-bottom: 20px;
                    padding-bottom: 10px;
                    border-bottom: 1px solid var(--vscode-panel-border);
                }
                .agent-status {
                    display: inline-block;
                    padding: 4px 8px;
                    border-radius: 4px;
                    font-size: 12px;
                    font-weight: bold;
                    margin-left: 10px;
                }
                .status-idle { background-color: var(--vscode-charts-green); }
                .status-working { background-color: var(--vscode-charts-yellow); }
                .status-error { background-color: var(--vscode-charts-red); }
                .active-badge {
                    background-color: var(--vscode-charts-blue);
                    color: white;
                    padding: 2px 6px;
                    border-radius: 3px;
                    font-size: 10px;
                    margin-left: 10px;
                }
                .info-grid {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 20px;
                    margin-bottom: 20px;
                }
                .info-card {
                    background-color: var(--vscode-editor-inactiveSelectionBackground);
                    padding: 15px;
                    border-radius: 5px;
                }
                .info-card h3 {
                    margin-top: 0;
                    color: var(--vscode-textLink-foreground);
                }
                .chat-container {
                    border: 1px solid var(--vscode-panel-border);
                    border-radius: 5px;
                    height: 300px;
                    display: flex;
                    flex-direction: column;
                }
                .chat-messages {
                    flex: 1;
                    overflow-y: auto;
                    padding: 10px;
                    background-color: var(--vscode-editor-background);
                }
                .chat-input {
                    display: flex;
                    padding: 10px;
                    border-top: 1px solid var(--vscode-panel-border);
                }
                .chat-input input {
                    flex: 1;
                    padding: 8px;
                    border: 1px solid var(--vscode-input-border);
                    background-color: var(--vscode-input-background);
                    color: var(--vscode-input-foreground);
                    border-radius: 3px;
                }
                .chat-input button {
                    margin-left: 10px;
                    padding: 8px 16px;
                    background-color: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                    border: none;
                    border-radius: 3px;
                    cursor: pointer;
                }
                .message {
                    margin-bottom: 10px;
                    padding: 8px;
                    border-radius: 5px;
                }
                .message.user {
                    background-color: var(--vscode-textBlockQuote-background);
                    text-align: right;
                }
                .message.agent {
                    background-color: var(--vscode-editor-inactiveSelectionBackground);
                }
            </style>
        </head>
        <body>
            <div class="agent-header">
                <h1>${agent.name}</h1>
                <span class="agent-status status-${agent.status}">${agent.status.toUpperCase()}</span>
                ${isActive ? '<span class="active-badge">ACTIVE</span>' : ''}
            </div>

            <div class="info-grid">
                <div class="info-card">
                    <h3>Basic Information</h3>
                    <p><strong>Type:</strong> ${agent.type}</p>
                    <p><strong>Created:</strong> ${agent.created.toLocaleString()}</p>
                    <p><strong>Last Activity:</strong> ${agent.lastActivity.toLocaleString()}</p>
                    <p><strong>Children:</strong> ${agent.children.length}</p>
                </div>
                <div class="info-card">
                    <h3>Hierarchy</h3>
                    <p><strong>Parent:</strong> ${agent.parentId || 'None (Root Agent)'}</p>
                    <p><strong>Sub-agents:</strong> ${agent.children.length}</p>
                    <p><strong>Level:</strong> ${agent.parentId ? 'Sub-agent' : 'Root Agent'}</p>
                </div>
            </div>

            <div class="info-card">
                <h3>Chat with Agent</h3>
                <div class="chat-container">
                    <div class="chat-messages" id="chatMessages">
                        <div class="message agent">
                            <strong>Agent:</strong> Hello! I'm ${agent.name}. How can I help you today?
                        </div>
                    </div>
                    <div class="chat-input">
                        <input type="text" id="messageInput" placeholder="Type your message..." />
                        <button onclick="sendMessage()">Send</button>
                    </div>
                </div>
            </div>

            <script>
                const vscode = acquireVsCodeApi();
                
                function sendMessage() {
                    const input = document.getElementById('messageInput');
                    const message = input.value.trim();
                    if (!message) return;
                    
                    // Add user message to chat
                    addMessage('user', message);
                    input.value = '';
                    
                    // Send to agent
                    vscode.postMessage({
                        command: 'sendMessage',
                        text: message
                    });
                }
                
                function addMessage(sender, text) {
                    const messagesContainer = document.getElementById('chatMessages');
                    const messageDiv = document.createElement('div');
                    messageDiv.className = \`message \${sender}\`;
                    messageDiv.innerHTML = \`<strong>\${sender === 'user' ? 'You' : 'Agent'}:</strong> \${text}\`;
                    messagesContainer.appendChild(messageDiv);
                    messagesContainer.scrollTop = messagesContainer.scrollHeight;
                }
                
                // Handle messages from extension
                window.addEventListener('message', event => {
                    const message = event.data;
                    switch (message.command) {
                        case 'messageResponse':
                            addMessage('agent', message.response);
                            break;
                        case 'messageError':
                            addMessage('agent', 'Error: ' + message.error);
                            break;
                    }
                });
                
                // Handle Enter key in input
                document.getElementById('messageInput').addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        sendMessage();
                    }
                });
            </script>
        </body>
        </html>`;
    }

    getTreeItem(element: Agent): vscode.TreeItem {
        const hasChildren = element.children.length > 0;
        const item = new vscode.TreeItem(
            element.name,
            hasChildren ? vscode.TreeItemCollapsibleState.Expanded : vscode.TreeItemCollapsibleState.None
        );

        const activeAgent = this.agentManager.getActiveAgent();
        const isActive = activeAgent?.id === element.id;

        item.description = `${element.type} - ${element.status}${isActive ? ' (Active)' : ''}`;
        item.tooltip = `${element.name}\nType: ${element.type}\nStatus: ${element.status}\nCreated: ${element.created.toLocaleString()}\nLast Activity: ${element.lastActivity.toLocaleString()}`;
        item.contextValue = isActive ? 'activeAgent' : 'agent';
        
        // Set icon based on status and type
        let iconName = 'robot';
        if (element.status === 'working') {
            iconName = 'loading~spin';
        } else if (element.status === 'error') {
            iconName = 'error';
        }
        
        item.iconPath = new vscode.ThemeIcon(iconName, isActive ? new vscode.ThemeColor('charts.blue') : undefined);

        return item;
    }

    getChildren(element?: Agent): Thenable<Agent[]> {
        if (!element) {
            // Return root agents (agents without parents)
            const rootAgents = this.agentManager.getAllAgents().filter(agent => !agent.parentId);
            return Promise.resolve(rootAgents);
        } else {
            // Return children of the given agent
            const children = element.children.map(childId => this.agentManager.getAgent(childId)).filter(Boolean) as Agent[];
            return Promise.resolve(children);
        }
    }

    refresh(): void {
        this._onDidChangeTreeData.fire();
    }
}
