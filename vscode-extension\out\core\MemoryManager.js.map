{"version": 3, "file": "MemoryManager.js", "sourceRoot": "", "sources": ["../../src/core/MemoryManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,2CAA6B;AAC7B,uCAAyB;AAuBzB,MAAa,aAAa;IAStB,YAAoB,OAAgC;QAAhC,YAAO,GAAP,OAAO,CAAyB;QAR5C,yBAAoB,GAA8E,IAAI,MAAM,CAAC,YAAY,EAAwD,CAAC;QACjL,wBAAmB,GAAuE,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;QAE3H,aAAQ,GAA4B,IAAI,GAAG,EAAE,CAAC;QAC9C,cAAS,GAA+B,IAAI,GAAG,EAAE,CAAC;QAClD,kBAAa,GAAG,CAAC,CAAC;QAClB,qBAAgB,GAAG,CAAC,CAAC;QAGzB,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,CAAC,0BAA0B,EAAE,CAAC;IACtC,CAAC;IAEM,KAAK,CAAC,UAAU,CAAC,OAAe,EAAE,IAAwB,EAAE,OAAiB,EAAE,EAAE,aAAqB,CAAC;QAC1G,MAAM,MAAM,GAAe;YACvB,EAAE,EAAE,UAAU,EAAE,IAAI,CAAC,aAAa,EAAE;YACpC,IAAI;YACJ,OAAO;YACP,IAAI;YACJ,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,UAAU;YACV,QAAQ,EAAE,EAAE;SACf,CAAC;QAEF,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;QACrC,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACjC,IAAI,CAAC,OAAO,EAAE,CAAC;QAEf,OAAO,MAAM,CAAC,EAAE,CAAC;IACrB,CAAC;IAEM,KAAK,CAAC,UAAU,CAAC,KAAa,EAAE,IAAyB;QAC5D,MAAM,OAAO,GAAiB,EAAE,CAAC;QAEjC,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE;YACzC,IAAI,IAAI,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,EAAE;gBAC9B,SAAS;aACZ;YAED,yEAAyE;YACzE,IAAI,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;gBAC1D,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE;gBAC1E,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aACxB;SACJ;QAED,iCAAiC;QACjC,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YACzB,MAAM,cAAc,GAAG,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC;YACnD,IAAI,cAAc,KAAK,CAAC;gBAAE,OAAO,cAAc,CAAC;YAChD,OAAO,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QACzD,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,KAAK,CAAC,YAAY,CAAC,EAAU;QAChC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACzC,IAAI,OAAO,EAAE;YACT,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACjC,IAAI,CAAC,OAAO,EAAE,CAAC;SAClB;QACD,OAAO,OAAO,CAAC;IACnB,CAAC;IAEM,KAAK,CAAC,YAAY,CAAC,EAAU,EAAE,OAA4B;QAC9D,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACrC,IAAI,CAAC,MAAM,EAAE;YACT,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAC/B,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACjC,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,KAAK,CAAC,YAAY,CAAC,KAAa,EAAE,OAAe,EAAE,QAAgB,EAAE,MAAc;QACtF,MAAM,SAAS,GAAkB;YAC7B,EAAE,EAAE,aAAa,EAAE,IAAI,CAAC,gBAAgB,EAAE;YAC1C,KAAK;YACL,OAAO;YACP,QAAQ;YACR,MAAM;YACN,WAAW,EAAE,IAAI,IAAI,EAAE;SAC1B,CAAC;QAEF,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;QAC5C,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;QACtC,IAAI,CAAC,OAAO,EAAE,CAAC;QAEf,OAAO,SAAS,CAAC,EAAE,CAAC;IACxB,CAAC;IAEM,KAAK,CAAC,eAAe,CAAC,KAAa,EAAE,QAAiB;QACzD,MAAM,OAAO,GAAoB,EAAE,CAAC;QAEpC,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE;YACxC,IAAI,QAAQ,IAAI,IAAI,CAAC,QAAQ,KAAK,QAAQ,EAAE;gBACxC,SAAS;aACZ;YAED,qBAAqB;YACrB,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;gBACtD,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,EAAE;gBAC1D,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACtB;SACJ;QAED,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC;IACrF,CAAC;IAEM,cAAc;QACjB,MAAM,KAAK,GAAG;YACV,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI;YACzB,MAAM,EAAE,EAA4B;YACpC,YAAY,EAAE,EAA4B;SAC7C,CAAC;QAEF,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE;YACzC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACjE,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,cAAc;YAC7E,MAAM,GAAG,GAAG,GAAG,eAAe,IAAI,eAAe,GAAG,CAAC,EAAE,CAAC;YACxD,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;SAChE;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,iBAAiB;QACpB,MAAM,KAAK,GAAG;YACV,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI;YAC1B,UAAU,EAAE,EAA4B;SAC3C,CAAC;QAEF,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE;YACxC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;SAChF;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEO,KAAK,CAAC,qBAAqB;QAC/B,IAAI;YACA,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAA6B,qBAAqB,EAAE,EAAE,CAAC,CAAC;YACvG,KAAK,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;gBACnD,wCAAwC;gBACxC,MAAM,CAAC,SAAS,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBAC9C,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;aACjC;YACD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;SAC3C;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;SAC/D;IACL,CAAC;IAEO,KAAK,CAAC,mBAAmB;QAC7B,IAAI;YACA,MAAM,UAAU,GAA+B,EAAE,CAAC;YAClD,KAAK,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE;gBAChD,UAAU,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC;aAC3B;YACD,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,qBAAqB,EAAE,UAAU,CAAC,CAAC;SAC5E;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;SAC7D;IACL,CAAC;IAEO,0BAA0B;QAC9B,MAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/D,IAAI,CAAC,eAAe,EAAE;YAClB,OAAO;SACV;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;QACxF,IAAI,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE;YAC9B,IAAI;gBACA,MAAM,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;gBAC5C,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;oBACtB,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;wBACxB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;wBAChD,MAAM,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;wBAClD,MAAM,aAAa,GAAkB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;wBACzD,aAAa,CAAC,WAAW,GAAG,IAAI,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;wBAChE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,EAAE,aAAa,CAAC,CAAC;qBACvD;iBACJ;gBACD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;aAC/C;YAAC,OAAO,KAAK,EAAE;gBACZ,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;aACpE;SACJ;IACL,CAAC;IAEO,KAAK,CAAC,wBAAwB;QAClC,MAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/D,IAAI,CAAC,eAAe,EAAE;YAClB,OAAO;SACV;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;QAExF,IAAI;YACA,0BAA0B;YAC1B,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE;gBAC/B,EAAE,CAAC,SAAS,CAAC,aAAa,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;aACpD;YAED,8CAA8C;YAC9C,KAAK,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE;gBAC/C,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;gBACxD,EAAE,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;aAC7D;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;SAClE;IACL,CAAC;IAED,kCAAkC;IAClC,WAAW,CAAC,OAAmC;QAC3C,MAAM,QAAQ,GAAG,MAAM,IAAI,OAAO,CAAC;QACnC,MAAM,IAAI,GAAG,IAAI,MAAM,CAAC,QAAQ,CAC5B,QAAQ,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,IAAI,KAAK,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,EACpF,MAAM,CAAC,wBAAwB,CAAC,IAAI,CACvC,CAAC;QAEF,IAAI,QAAQ,EAAE;YACV,IAAI,CAAC,WAAW,GAAG,eAAe,OAAO,CAAC,UAAU,EAAE,CAAC;YACvD,IAAI,CAAC,OAAO,GAAG,GAAG,OAAO,CAAC,OAAO,WAAW,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,OAAO,CAAC,SAAS,CAAC,cAAc,EAAE,EAAE,CAAC;YACtH,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;YACjC,IAAI,CAAC,QAAQ,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;SACjD;aAAM;YACH,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,QAAQ,CAAC;YACpC,IAAI,CAAC,OAAO,GAAG,GAAG,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,gBAAgB,OAAO,CAAC,MAAM,cAAc,OAAO,CAAC,WAAW,CAAC,cAAc,EAAE,EAAE,CAAC;YACtI,IAAI,CAAC,YAAY,GAAG,eAAe,CAAC;YACpC,IAAI,CAAC,QAAQ,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;SAChD;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,WAAW,CAAC,OAAoC;QAC5C,IAAI,CAAC,OAAO,EAAE;YACV,2CAA2C;YAC3C,MAAM,KAAK,GAAmC;gBAC1C,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;gBACrC,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;aACzC,CAAC;YACF,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SACjC;QACD,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC/B,CAAC;IAED,OAAO;QACH,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC;IACrC,CAAC;IAEM,OAAO;QACV,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QACtB,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;IAC3B,CAAC;CACJ;AApQD,sCAoQC"}