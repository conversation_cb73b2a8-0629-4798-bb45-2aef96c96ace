{"version": 3, "file": "VSCodeTerminal.js", "sourceRoot": "", "sources": ["../../src/tools/VSCodeTerminal.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AA8BjC,MAAa,cAAc;IAMvB;QAJQ,cAAS,GAAG,IAAI,GAAG,EAA2B,CAAC;QAC/C,oBAAe,GAAG,CAAC,CAAC;QACpB,qBAAgB,GAAkB,IAAI,CAAC;QAG3C,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,qBAAqB,CAAC,CAAC;QAC9E,IAAI,CAAC,0BAA0B,EAAE,CAAC;IACtC,CAAC;IAEO,0BAA0B;QAC9B,mCAAmC;QACnC,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,QAAQ,EAAE,EAAE;YAC1C,KAAK,MAAM,CAAC,EAAE,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE;gBACxC,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE;oBAC/B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;oBAC1B,IAAI,IAAI,CAAC,gBAAgB,KAAK,EAAE,EAAE;wBAC9B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;qBAChC;oBACD,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,oBAAoB,EAAE,SAAS,CAAC,CAAC;oBAC/D,MAAM;iBACT;aACJ;QACL,CAAC,CAAC,CAAC;QAEH,qCAAqC;QACrC,MAAM,CAAC,MAAM,CAAC,yBAAyB,CAAC,CAAC,QAAQ,EAAE,EAAE;YACjD,IAAI,QAAQ,EAAE;gBACV,KAAK,MAAM,CAAC,EAAE,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE;oBACxC,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE;wBAC/B,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;wBAC3B,OAAO,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;wBAC9B,MAAM;qBACT;iBACJ;aACJ;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,KAAK,CAAC,cAAc,CAAC,OAAwB;QAChD,IAAI;YACA,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,+BAA+B,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;YAEhF,IAAI,OAAO,CAAC,WAAW,EAAE;gBACrB,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC;aACxD;iBAAM;gBACH,OAAO,MAAM,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC,CAAC;aAC3D;SAEJ;QAAC,OAAO,KAAK,EAAE;YACZ,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,4BAA4B,KAAK,EAAE,CAAC,CAAC;YACnE,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE;aAC1B,CAAC;SACL;IACL,CAAC;IAEO,KAAK,CAAC,yBAAyB,CAAC,OAAwB;QAC5D,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAC3D,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAE/C,IAAI,CAAC,OAAO,EAAE;YACV,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;SACxD;QAED,4BAA4B;QAC5B,IAAI,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC;QAClC,IAAI,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;YACzC,WAAW,IAAI,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SAC/C;QAED,2BAA2B;QAC3B,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QACvC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;QACxB,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACnC,OAAO,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;QAE9B,OAAO;YACH,OAAO,EAAE,IAAI;YACb,UAAU,EAAE,UAAU;YACtB,MAAM,EAAE,6BAA6B,WAAW,EAAE;SACrD,CAAC;IACN,CAAC;IAEO,KAAK,CAAC,4BAA4B,CAAC,OAAwB;QAC/D,kEAAkE;QAClE,iDAAiD;QAEjD,MAAM,eAAe,GAA2B;YAC5C,IAAI,EAAE,mBAAmB,EAAE,IAAI,CAAC,eAAe,EAAE;YACjD,GAAG,EAAE,OAAO,CAAC,GAAG;YAChB,GAAG,EAAE,OAAO,CAAC,GAAG;YAChB,SAAS,EAAE,OAAO,CAAC,KAAK;SAC3B,CAAC;QAEF,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;QAE/D,4BAA4B;QAC5B,IAAI,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC;QAClC,IAAI,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;YACzC,WAAW,IAAI,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SAC/C;QAED,eAAe;QACf,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QAC/B,QAAQ,CAAC,IAAI,EAAE,CAAC;QAEhB,iEAAiE;QACjE,0CAA0C;QAC1C,OAAO;YACH,OAAO,EAAE,IAAI;YACb,MAAM,EAAE,iCAAiC,WAAW,EAAE;YACtD,UAAU,EAAE,QAAQ,IAAI,CAAC,eAAe,EAAE;SAC7C,CAAC;IACN,CAAC;IAEM,KAAK,CAAC,cAAc,CAAC,IAAa,EAAE,OAAkC;QACzE,MAAM,UAAU,GAAG,YAAY,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC;QACxD,MAAM,YAAY,GAAG,IAAI,IAAI,cAAc,IAAI,CAAC,eAAe,EAAE,CAAC;QAElE,MAAM,eAAe,GAA2B;YAC5C,IAAI,EAAE,YAAY;YAClB,GAAG,EAAE,OAAO,EAAE,GAAG;YACjB,GAAG,EAAE,OAAO,EAAE,GAAG;YACjB,SAAS,EAAE,OAAO,EAAE,KAAK;SAC5B,CAAC;QAEF,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;QAE/D,MAAM,OAAO,GAAoB;YAC7B,EAAE,EAAE,UAAU;YACd,IAAI,EAAE,YAAY;YAClB,QAAQ,EAAE,QAAQ;YAClB,QAAQ,EAAE,IAAI;YACd,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,QAAQ,EAAE,IAAI,IAAI,EAAE;YACpB,QAAQ,EAAE,EAAE;SACf,CAAC;QAEF,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QACxC,IAAI,CAAC,gBAAgB,GAAG,UAAU,CAAC;QAEnC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,6BAA6B,UAAU,EAAE,CAAC,CAAC;QACzE,OAAO,UAAU,CAAC;IACtB,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,OAAwB;QACtD,6DAA6D;QAC7D,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzE,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC1D,IAAI,OAAO,IAAI,OAAO,CAAC,QAAQ,EAAE;gBAC7B,OAAO,IAAI,CAAC,gBAAgB,CAAC;aAChC;SACJ;QAED,sBAAsB;QACtB,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IACzD,CAAC;IAEM,KAAK,CAAC,gBAAgB,CAAC,UAAkB;QAC5C,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC/C,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;QACxB,IAAI,CAAC,gBAAgB,GAAG,UAAU,CAAC;QACnC,OAAO,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;QAE9B,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,yBAAyB,UAAU,EAAE,CAAC,CAAC;QACrE,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,KAAK,CAAC,aAAa,CAAC,UAAkB;QACzC,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC/C,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;QAC3B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAElC,IAAI,IAAI,CAAC,gBAAgB,KAAK,UAAU,EAAE;YACtC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;SAChC;QAED,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,oBAAoB,UAAU,EAAE,CAAC,CAAC;QAChE,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,KAAK,CAAC,kBAAkB,CAAC,UAAkB,EAAE,IAAY;QAC5D,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC/C,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAChC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5B,OAAO,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;QAE9B,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,yBAAyB,UAAU,KAAK,IAAI,EAAE,CAAC,CAAC;QAC9E,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,mBAAmB;QACtB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;IAC/C,CAAC;IAEM,iBAAiB;QACpB,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,OAAO,IAAI,CAAC;SACf;QACD,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,IAAI,CAAC;IAC7D,CAAC;IAEM,KAAK,CAAC,SAAS,CAAC,UAAkB,EAAE,IAAe,EAAE,UAAmB;QAC3E,MAAM,gBAAgB,GAAG,UAAU,IAAI,IAAI,CAAC,gBAAgB,CAAC;QAE7D,IAAI,CAAC,gBAAgB,EAAE;YACnB,2CAA2C;YAC3C,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC;YACpE,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;SAChE;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QACrD,IAAI,CAAC,OAAO,EAAE;YACV,MAAM,IAAI,KAAK,CAAC,oBAAoB,gBAAgB,YAAY,CAAC,CAAC;SACrE;QAED,6DAA6D;QAC7D,MAAM,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,WAAW,EAAE,CAAC;QAC7D,IAAI,OAAe,CAAC;QAEpB,QAAQ,SAAS,EAAE;YACf,KAAK,IAAI;gBACL,OAAO,GAAG,WAAW,UAAU,GAAG,CAAC;gBACnC,MAAM;YACV,KAAK,IAAI;gBACL,OAAO,GAAG,SAAS,UAAU,GAAG,CAAC;gBACjC,MAAM;YACV,KAAK,IAAI;gBACL,OAAO,GAAG,YAAY,UAAU,GAAG,CAAC;gBACpC,MAAM;YACV,KAAK,IAAI;gBACL,OAAO,GAAG,SAAS,UAAU,GAAG,CAAC;gBACjC,MAAM;YACV,KAAK,KAAK;gBACN,OAAO,GAAG,qBAAqB,UAAU,GAAG,CAAC;gBAC7C,MAAM;YACV,KAAK,KAAK;gBACN,OAAO,GAAG,IAAI,UAAU,GAAG,CAAC;gBAC5B,MAAM;YACV;gBACI,OAAO,GAAG,IAAI,UAAU,GAAG,CAAC;SACnC;QAED,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;YACzB,OAAO,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SACnC;QAED,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QACnC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;QACxB,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC/B,OAAO,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;QAE9B,OAAO;YACH,OAAO,EAAE,IAAI;YACb,UAAU,EAAE,gBAAgB;YAC5B,MAAM,EAAE,oBAAoB,OAAO,EAAE;SACxC,CAAC;IACN,CAAC;IAEM,KAAK,CAAC,aAAa,CAAC,UAAmB;QAC1C,MAAM,gBAAgB,GAAG,UAAU,IAAI,IAAI,CAAC,gBAAgB,CAAC;QAE7D,IAAI,CAAC,gBAAgB,EAAE;YACnB,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QACrD,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,4CAA4C;QAC5C,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QACnC,OAAO,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;QAE9B,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,UAAmB;QACxC,MAAM,gBAAgB,GAAG,UAAU,IAAI,IAAI,CAAC,gBAAgB,CAAC;QAE7D,IAAI,CAAC,gBAAgB,EAAE;YACnB,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QACrD,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,2CAA2C;QAC3C,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;QAC5C,OAAO,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;QAE9B,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,kBAAkB,CAAC,UAAkB;QACxC,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC/C,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAChD,CAAC;IAEM,KAAK,CAAC,mBAAmB;QAC5B,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC1C,iBAAiB,EACjB,kBAAkB,EAClB,MAAM,CAAC,UAAU,CAAC,GAAG,EACrB;YACI,aAAa,EAAE,IAAI;YACnB,uBAAuB,EAAE,IAAI;SAChC,CACJ,CAAC;QAEF,KAAK,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAEnD,0BAA0B;QAC1B,KAAK,CAAC,OAAO,CAAC,mBAAmB,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YAChD,QAAQ,OAAO,CAAC,OAAO,EAAE;gBACrB,KAAK,gBAAgB;oBACjB,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;oBAChD,MAAM;gBACV,KAAK,eAAe;oBAChB,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;oBAC7C,KAAK,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAC,UAAU;oBAC9D,MAAM;gBACV,KAAK,gBAAgB;oBACjB,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBACxC,KAAK,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAC,UAAU;oBAC9D,MAAM;gBACV,KAAK,eAAe;oBAChB,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;oBAC7C,MAAM;aACb;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,sBAAsB;QAC1B,MAAM,QAAQ,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE5C,MAAM,WAAW,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;yBAC3B,OAAO,CAAC,EAAE,KAAK,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;sBACvD,OAAO,CAAC,IAAI;sBACZ,OAAO,CAAC,EAAE;sBACV,OAAO,CAAC,SAAS,CAAC,cAAc,EAAE;sBAClC,OAAO,CAAC,QAAQ,CAAC,cAAc,EAAE;sBACjC,OAAO,CAAC,QAAQ,CAAC,MAAM;;uDAEU,OAAO,CAAC,EAAE;sDACX,OAAO,CAAC,EAAE;sDACV,OAAO,CAAC,EAAE;;;SAGvD,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAEZ,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sBAmCO,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;gBAyBjB,CAAC;IACb,CAAC;IAEM,OAAO;QACV,8BAA8B;QAC9B,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE;YAC3C,OAAO,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;SAC9B;QACD,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QACvB,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;IACjC,CAAC;CACJ;AAxbD,wCAwbC"}