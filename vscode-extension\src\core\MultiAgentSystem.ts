import * as vscode from 'vscode';
import { Agent } from './AgentManager';
import { ModelManager } from './ModelManager';
import { ToolManager } from './ToolManager';
import { EnhancedMemoryManager } from './EnhancedMemoryManager';

export interface AgentTemplate {
    id: string;
    name: string;
    description: string;
    type: AgentType;
    systemPrompt: string;
    capabilities: string[];
    tools: string[];
    modelConfig: {
        provider: string;
        model: string;
        temperature: number;
        maxTokens: number;
    };
    memoryConfig: {
        enabled: boolean;
        retentionDays: number;
        importance: number;
    };
    icon: string;
    color: string;
}

export enum AgentType {
    GENERAL = 'general',
    CODE_DEVELOPER = 'code-developer',
    CODE_REVIEWER = 'code-reviewer',
    TESTER = 'tester',
    DOCUMENTATION = 'documentation',
    RESEARCHER = 'researcher',
    PROJECT_MANAGER = 'project-manager',
    SECURITY_AUDITOR = 'security-auditor',
    PERFORMANCE_OPTIMIZER = 'performance-optimizer',
    UI_UX_DESIGNER = 'ui-ux-designer',
    DATA_ANALYST = 'data-analyst',
    DEVOPS_ENGINEER = 'devops-engineer',
    CUSTOM = 'custom'
}

export interface AgentTask {
    id: string;
    agentId: string;
    title: string;
    description: string;
    status: 'pending' | 'in-progress' | 'completed' | 'failed' | 'cancelled';
    priority: 'low' | 'medium' | 'high' | 'urgent';
    createdAt: Date;
    updatedAt: Date;
    completedAt?: Date;
    result?: string;
    dependencies: string[];
    assignedBy: string;
    estimatedDuration?: number;
    actualDuration?: number;
}

export interface AgentCommunication {
    id: string;
    fromAgentId: string;
    toAgentId: string;
    message: string;
    type: 'request' | 'response' | 'notification' | 'delegation';
    timestamp: Date;
    metadata?: Record<string, any>;
}

export class MultiAgentSystem implements vscode.TreeDataProvider<Agent> {
    private _onDidChangeTreeData: vscode.EventEmitter<Agent | undefined | null | void> = new vscode.EventEmitter<Agent | undefined | null | void>();
    readonly onDidChangeTreeData: vscode.Event<Agent | undefined | null | void> = this._onDidChangeTreeData.event;

    private agents: Map<string, Agent> = new Map();
    private agentTemplates: Map<string, AgentTemplate> = new Map();
    private tasks: Map<string, AgentTask> = new Map();
    private communications: AgentCommunication[] = [];
    private agentCounter = 0;
    private taskCounter = 0;
    private outputChannel: vscode.OutputChannel;

    constructor(
        private context: vscode.ExtensionContext,
        private modelManager: ModelManager,
        private toolManager: ToolManager,
        private memoryManager: EnhancedMemoryManager
    ) {
        this.outputChannel = vscode.window.createOutputChannel('Agent Zero Multi-Agent System');
        this.initializeAgentTemplates();
        this.loadSystemState();
    }

    private initializeAgentTemplates(): void {
        const templates: AgentTemplate[] = [
            {
                id: 'general-assistant',
                name: 'General Assistant',
                description: 'A versatile AI assistant for general tasks',
                type: AgentType.GENERAL,
                systemPrompt: 'You are a helpful AI assistant. Provide clear, accurate, and helpful responses to user queries.',
                capabilities: ['conversation', 'analysis', 'problem-solving', 'research'],
                tools: ['search-engine', 'file-editor', 'browser'],
                modelConfig: {
                    provider: 'openai',
                    model: 'gpt-4',
                    temperature: 0.7,
                    maxTokens: 4000
                },
                memoryConfig: {
                    enabled: true,
                    retentionDays: 30,
                    importance: 5
                },
                icon: 'robot',
                color: '#007ACC'
            },
            {
                id: 'code-developer',
                name: 'Code Developer',
                description: 'Specialized in writing, debugging, and optimizing code',
                type: AgentType.CODE_DEVELOPER,
                systemPrompt: 'You are an expert software developer. Write clean, efficient, and well-documented code. Follow best practices and coding standards.',
                capabilities: ['code-generation', 'debugging', 'refactoring', 'architecture-design'],
                tools: ['code-execution', 'file-editor', 'git-integration', 'terminal'],
                modelConfig: {
                    provider: 'openai',
                    model: 'gpt-4',
                    temperature: 0.3,
                    maxTokens: 6000
                },
                memoryConfig: {
                    enabled: true,
                    retentionDays: 60,
                    importance: 8
                },
                icon: 'code',
                color: '#00D4AA'
            },
            {
                id: 'code-reviewer',
                name: 'Code Reviewer',
                description: 'Reviews code for quality, security, and best practices',
                type: AgentType.CODE_REVIEWER,
                systemPrompt: 'You are a senior code reviewer. Analyze code for bugs, security issues, performance problems, and adherence to best practices. Provide constructive feedback.',
                capabilities: ['code-review', 'security-analysis', 'performance-analysis', 'best-practices'],
                tools: ['file-editor', 'git-integration', 'search-engine'],
                modelConfig: {
                    provider: 'anthropic',
                    model: 'claude-3-sonnet-20240229',
                    temperature: 0.2,
                    maxTokens: 8000
                },
                memoryConfig: {
                    enabled: true,
                    retentionDays: 90,
                    importance: 9
                },
                icon: 'search',
                color: '#FF6B6B'
            },
            {
                id: 'tester',
                name: 'Test Engineer',
                description: 'Creates and executes comprehensive test suites',
                type: AgentType.TESTER,
                systemPrompt: 'You are a QA engineer specializing in test automation. Create comprehensive test cases, write automated tests, and ensure software quality.',
                capabilities: ['test-creation', 'test-automation', 'bug-detection', 'quality-assurance'],
                tools: ['code-execution', 'file-editor', 'terminal'],
                modelConfig: {
                    provider: 'openai',
                    model: 'gpt-4',
                    temperature: 0.4,
                    maxTokens: 5000
                },
                memoryConfig: {
                    enabled: true,
                    retentionDays: 45,
                    importance: 7
                },
                icon: 'beaker',
                color: '#4ECDC4'
            },
            {
                id: 'documentation',
                name: 'Documentation Writer',
                description: 'Creates clear and comprehensive documentation',
                type: AgentType.DOCUMENTATION,
                systemPrompt: 'You are a technical writer. Create clear, comprehensive, and user-friendly documentation. Focus on clarity and completeness.',
                capabilities: ['documentation', 'technical-writing', 'api-documentation', 'user-guides'],
                tools: ['file-editor', 'search-engine', 'browser'],
                modelConfig: {
                    provider: 'anthropic',
                    model: 'claude-3-sonnet-20240229',
                    temperature: 0.5,
                    maxTokens: 6000
                },
                memoryConfig: {
                    enabled: true,
                    retentionDays: 120,
                    importance: 6
                },
                icon: 'book',
                color: '#95E1D3'
            },
            {
                id: 'researcher',
                name: 'Research Assistant',
                description: 'Conducts thorough research and analysis',
                type: AgentType.RESEARCHER,
                systemPrompt: 'You are a research assistant. Conduct thorough research, analyze information, and provide comprehensive insights on various topics.',
                capabilities: ['research', 'analysis', 'data-gathering', 'report-writing'],
                tools: ['search-engine', 'browser', 'file-editor'],
                modelConfig: {
                    provider: 'openai',
                    model: 'gpt-4',
                    temperature: 0.6,
                    maxTokens: 8000
                },
                memoryConfig: {
                    enabled: true,
                    retentionDays: 180,
                    importance: 8
                },
                icon: 'search',
                color: '#F38BA8'
            },
            {
                id: 'security-auditor',
                name: 'Security Auditor',
                description: 'Identifies and addresses security vulnerabilities',
                type: AgentType.SECURITY_AUDITOR,
                systemPrompt: 'You are a cybersecurity expert. Identify security vulnerabilities, assess risks, and recommend security improvements.',
                capabilities: ['security-analysis', 'vulnerability-assessment', 'penetration-testing', 'compliance'],
                tools: ['file-editor', 'search-engine', 'terminal'],
                modelConfig: {
                    provider: 'anthropic',
                    model: 'claude-3-opus-20240229',
                    temperature: 0.1,
                    maxTokens: 6000
                },
                memoryConfig: {
                    enabled: true,
                    retentionDays: 365,
                    importance: 10
                },
                icon: 'shield',
                color: '#FF4757'
            }
        ];

        templates.forEach(template => {
            this.agentTemplates.set(template.id, template);
        });
    }

    public async createAgentFromTemplate(templateId: string, customName?: string, parentId?: string): Promise<Agent> {
        const template = this.agentTemplates.get(templateId);
        if (!template) {
            throw new Error(`Template not found: ${templateId}`);
        }

        const agent: Agent = {
            id: `agent-${++this.agentCounter}`,
            name: customName || template.name,
            type: template.type,
            status: 'idle',
            created: new Date(),
            lastActivity: new Date(),
            parentId,
            children: [],
            context: {
                template: template,
                systemPrompt: template.systemPrompt,
                capabilities: template.capabilities,
                tools: template.tools,
                modelConfig: template.modelConfig,
                memoryConfig: template.memoryConfig,
                taskQueue: [],
                communicationHistory: []
            }
        };

        this.agents.set(agent.id, agent);

        // Add to parent's children if parent exists
        if (parentId && this.agents.has(parentId)) {
            const parent = this.agents.get(parentId)!;
            parent.children.push(agent.id);
        }

        await this.saveSystemState();
        this.refresh();

        this.outputChannel.appendLine(`Created agent: ${agent.name} (${agent.id}) from template: ${templateId}`);
        return agent;
    }

    public async createCustomAgent(config: {
        name: string;
        description: string;
        systemPrompt: string;
        capabilities: string[];
        tools: string[];
        modelConfig: AgentTemplate['modelConfig'];
        parentId?: string;
    }): Promise<Agent> {
        const agent: Agent = {
            id: `agent-${++this.agentCounter}`,
            name: config.name,
            type: AgentType.CUSTOM,
            status: 'idle',
            created: new Date(),
            lastActivity: new Date(),
            parentId: config.parentId,
            children: [],
            context: {
                description: config.description,
                systemPrompt: config.systemPrompt,
                capabilities: config.capabilities,
                tools: config.tools,
                modelConfig: config.modelConfig,
                memoryConfig: {
                    enabled: true,
                    retentionDays: 30,
                    importance: 5
                },
                taskQueue: [],
                communicationHistory: []
            }
        };

        this.agents.set(agent.id, agent);

        // Add to parent's children if parent exists
        if (config.parentId && this.agents.has(config.parentId)) {
            const parent = this.agents.get(config.parentId)!;
            parent.children.push(agent.id);
        }

        await this.saveSystemState();
        this.refresh();

        this.outputChannel.appendLine(`Created custom agent: ${agent.name} (${agent.id})`);
        return agent;
    }

    public async assignTask(agentId: string, task: Omit<AgentTask, 'id' | 'agentId' | 'createdAt' | 'updatedAt'>): Promise<string> {
        const agent = this.agents.get(agentId);
        if (!agent) {
            throw new Error(`Agent not found: ${agentId}`);
        }

        const agentTask: AgentTask = {
            id: `task-${++this.taskCounter}`,
            agentId,
            createdAt: new Date(),
            updatedAt: new Date(),
            ...task
        };

        this.tasks.set(agentTask.id, agentTask);
        agent.context.taskQueue = agent.context.taskQueue || [];
        agent.context.taskQueue.push(agentTask.id);

        await this.saveSystemState();
        this.outputChannel.appendLine(`Assigned task "${agentTask.title}" to agent ${agent.name}`);

        // Start processing the task if agent is idle
        if (agent.status === 'idle') {
            await this.processNextTask(agentId);
        }

        return agentTask.id;
    }

    public async processNextTask(agentId: string): Promise<void> {
        const agent = this.agents.get(agentId);
        if (!agent || agent.status !== 'idle') {
            return;
        }

        const taskQueue = agent.context.taskQueue || [];
        if (taskQueue.length === 0) {
            return;
        }

        const taskId = taskQueue[0];
        const task = this.tasks.get(taskId);
        if (!task) {
            // Remove invalid task from queue
            agent.context.taskQueue = taskQueue.slice(1);
            return;
        }

        // Check dependencies
        const dependenciesCompleted = task.dependencies.every(depId => {
            const depTask = this.tasks.get(depId);
            return depTask && depTask.status === 'completed';
        });

        if (!dependenciesCompleted) {
            this.outputChannel.appendLine(`Task "${task.title}" dependencies not met, skipping for now`);
            return;
        }

        // Start processing the task
        agent.status = 'working';
        task.status = 'in-progress';
        task.updatedAt = new Date();

        this.outputChannel.appendLine(`Agent ${agent.name} started processing task: ${task.title}`);

        try {
            const result = await this.executeTask(agent, task);
            
            // Task completed successfully
            task.status = 'completed';
            task.result = result;
            task.completedAt = new Date();
            task.actualDuration = task.completedAt.getTime() - task.updatedAt.getTime();

            // Remove from queue
            agent.context.taskQueue = taskQueue.slice(1);
            agent.status = 'idle';
            agent.lastActivity = new Date();

            this.outputChannel.appendLine(`Agent ${agent.name} completed task: ${task.title}`);

            // Save memory of the task completion
            if (agent.context.memoryConfig?.enabled) {
                await this.memoryManager.saveMemory(
                    `Completed task: ${task.title}\nDescription: ${task.description}\nResult: ${result}`,
                    'solution',
                    `Task: ${task.title}`,
                    ['task', 'completion', agent.type],
                    agent.context.memoryConfig.importance,
                    {
                        agentId: agent.id
                    }
                );
            }

            // Process next task if available
            await this.processNextTask(agentId);

        } catch (error) {
            // Task failed
            task.status = 'failed';
            task.result = `Error: ${error}`;
            task.updatedAt = new Date();
            agent.status = 'error';

            this.outputChannel.appendLine(`Agent ${agent.name} failed to complete task: ${task.title} - ${error}`);
        }

        await this.saveSystemState();
        this.refresh();
    }

    private async executeTask(agent: Agent, task: AgentTask): Promise<string> {
        // Build context for the agent
        const context = {
            systemPrompt: agent.context.systemPrompt,
            capabilities: agent.context.capabilities,
            tools: agent.context.tools,
            taskDescription: task.description,
            agentMemory: await this.getAgentMemory(agent.id)
        };

        // Use the model manager to process the task
        const prompt = this.buildTaskPrompt(task, context);
        const response = await this.modelManager.processMessage(prompt, context);

        // If the task requires tool usage, execute tools
        if (this.requiresToolExecution(response)) {
            const toolResults = await this.executeTools(response, agent);
            return `${response}\n\nTool Results:\n${toolResults}`;
        }

        return response;
    }

    private buildTaskPrompt(task: AgentTask, context: any): string {
        return `
${context.systemPrompt}

You have the following capabilities: ${context.capabilities.join(', ')}
Available tools: ${context.tools.join(', ')}

Task: ${task.title}
Description: ${task.description}
Priority: ${task.priority}

${context.agentMemory ? `Relevant memory:\n${context.agentMemory}` : ''}

Please complete this task step by step. If you need to use tools, clearly indicate which tools you want to use and how.
        `.trim();
    }

    private requiresToolExecution(response: string): boolean {
        const toolKeywords = ['execute', 'run', 'search', 'file', 'git', 'terminal', 'browser'];
        return toolKeywords.some(keyword => response.toLowerCase().includes(keyword));
    }

    private async executeTools(response: string, agent: Agent): Promise<string> {
        // Parse tool requests from response and execute them
        // This is a simplified implementation
        const toolResults: string[] = [];

        for (const toolId of agent.context.tools) {
            if (response.toLowerCase().includes(toolId)) {
                try {
                    const result = await this.toolManager.executeTool(toolId, {
                        request: response,
                        agentId: agent.id
                    });
                    toolResults.push(`${toolId}: ${result.output}`);
                } catch (error) {
                    toolResults.push(`${toolId}: Error - ${error}`);
                }
            }
        }

        return toolResults.join('\n');
    }

    private async getAgentMemory(agentId: string): Promise<string> {
        try {
            const memories = await this.memoryManager.loadMemory(`agentId:${agentId}`, undefined, 5);
            return memories.map(m => `${m.title}: ${m.content.substring(0, 200)}`).join('\n');
        } catch (error) {
            return '';
        }
    }

    public async sendMessage(fromAgentId: string, toAgentId: string, message: string, type: AgentCommunication['type'] = 'request'): Promise<void> {
        const communication: AgentCommunication = {
            id: `comm-${Date.now()}`,
            fromAgentId,
            toAgentId,
            message,
            type,
            timestamp: new Date()
        };

        this.communications.push(communication);

        // Add to both agents' communication history
        const fromAgent = this.agents.get(fromAgentId);
        const toAgent = this.agents.get(toAgentId);

        if (fromAgent) {
            fromAgent.context.communicationHistory = fromAgent.context.communicationHistory || [];
            fromAgent.context.communicationHistory.push(communication.id);
        }

        if (toAgent) {
            toAgent.context.communicationHistory = toAgent.context.communicationHistory || [];
            toAgent.context.communicationHistory.push(communication.id);
        }

        this.outputChannel.appendLine(`Message sent from ${fromAgent?.name} to ${toAgent?.name}: ${message.substring(0, 100)}`);

        await this.saveSystemState();
    }

    public getAgentTemplates(): AgentTemplate[] {
        return Array.from(this.agentTemplates.values());
    }

    public getAgentTasks(agentId: string): AgentTask[] {
        return Array.from(this.tasks.values()).filter(task => task.agentId === agentId);
    }

    public getAgentCommunications(agentId: string): AgentCommunication[] {
        return this.communications.filter(comm => 
            comm.fromAgentId === agentId || comm.toAgentId === agentId
        );
    }

    public async deleteAgent(agentId: string): Promise<boolean> {
        const agent = this.agents.get(agentId);
        if (!agent) {
            return false;
        }

        // Cancel all pending tasks
        const agentTasks = this.getAgentTasks(agentId);
        for (const task of agentTasks) {
            if (task.status === 'pending' || task.status === 'in-progress') {
                task.status = 'cancelled';
                task.updatedAt = new Date();
            }
        }

        // Remove from parent's children
        if (agent.parentId && this.agents.has(agent.parentId)) {
            const parent = this.agents.get(agent.parentId)!;
            parent.children = parent.children.filter(childId => childId !== agentId);
        }

        // Delete all children recursively
        for (const childId of agent.children) {
            await this.deleteAgent(childId);
        }

        // Remove the agent
        this.agents.delete(agentId);

        await this.saveSystemState();
        this.refresh();

        this.outputChannel.appendLine(`Deleted agent: ${agent.name} (${agentId})`);
        return true;
    }

    private async loadSystemState(): Promise<void> {
        try {
            const agentsData = this.context.workspaceState.get<Record<string, Agent>>('multi-agent.agents', {});
            for (const [id, agent] of Object.entries(agentsData)) {
                agent.created = new Date(agent.created);
                agent.lastActivity = new Date(agent.lastActivity);
                this.agents.set(id, agent);
            }

            const tasksData = this.context.workspaceState.get<Record<string, AgentTask>>('multi-agent.tasks', {});
            for (const [id, task] of Object.entries(tasksData)) {
                task.createdAt = new Date(task.createdAt);
                task.updatedAt = new Date(task.updatedAt);
                if (task.completedAt) {
                    task.completedAt = new Date(task.completedAt);
                }
                this.tasks.set(id, task);
            }

            const commsData = this.context.workspaceState.get<AgentCommunication[]>('multi-agent.communications', []);
            this.communications = commsData.map(comm => ({
                ...comm,
                timestamp: new Date(comm.timestamp)
            }));

            this.agentCounter = this.agents.size;
            this.taskCounter = this.tasks.size;

        } catch (error) {
            this.outputChannel.appendLine(`Failed to load system state: ${error}`);
        }
    }

    private async saveSystemState(): Promise<void> {
        try {
            const agentsData: Record<string, Agent> = {};
            for (const [id, agent] of this.agents.entries()) {
                agentsData[id] = agent;
            }

            const tasksData: Record<string, AgentTask> = {};
            for (const [id, task] of this.tasks.entries()) {
                tasksData[id] = task;
            }

            await this.context.workspaceState.update('multi-agent.agents', agentsData);
            await this.context.workspaceState.update('multi-agent.tasks', tasksData);
            await this.context.workspaceState.update('multi-agent.communications', this.communications);

        } catch (error) {
            this.outputChannel.appendLine(`Failed to save system state: ${error}`);
        }
    }

    // TreeDataProvider implementation
    getTreeItem(element: Agent): vscode.TreeItem {
        const hasChildren = element.children.length > 0;
        const item = new vscode.TreeItem(
            element.name,
            hasChildren ? vscode.TreeItemCollapsibleState.Expanded : vscode.TreeItemCollapsibleState.None
        );

        const taskCount = this.getAgentTasks(element.id).filter(t => t.status === 'pending' || t.status === 'in-progress').length;
        
        item.description = `${element.type} - ${element.status}${taskCount > 0 ? ` (${taskCount} tasks)` : ''}`;
        item.tooltip = `${element.name}\nType: ${element.type}\nStatus: ${element.status}\nCreated: ${element.created.toLocaleString()}\nLast Activity: ${element.lastActivity.toLocaleString()}`;
        item.contextValue = 'multiAgent';
        
        // Set icon based on agent type and status
        const template = element.context.template as AgentTemplate;
        const iconName = template?.icon || 'robot';
        const iconColor = element.status === 'working' ? 'charts.yellow' : 
                         element.status === 'error' ? 'charts.red' : 
                         template?.color ? undefined : 'charts.blue';
        
        item.iconPath = new vscode.ThemeIcon(iconName, iconColor ? new vscode.ThemeColor(iconColor) : undefined);

        return item;
    }

    getChildren(element?: Agent): Thenable<Agent[]> {
        if (!element) {
            // Return root agents (agents without parents)
            const rootAgents = Array.from(this.agents.values()).filter(agent => !agent.parentId);
            return Promise.resolve(rootAgents);
        } else {
            // Return children of the given agent
            const children = element.children.map(childId => this.agents.get(childId)).filter(Boolean) as Agent[];
            return Promise.resolve(children);
        }
    }

    refresh(): void {
        this._onDidChangeTreeData.fire();
    }

    public dispose(): void {
        this.agents.clear();
        this.tasks.clear();
        this.communications = [];
        this.outputChannel.dispose();
    }
}
