{"version": 3, "file": "VSCodeSearchEngine.js", "sourceRoot": "", "sources": ["../../src/tools/VSCodeSearchEngine.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AA8BjC,MAAa,kBAAkB;IAI3B;QAFQ,kBAAa,GAAoB,EAAE,CAAC;QAGxC,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,0BAA0B,CAAC,CAAC;IACvF,CAAC;IAEM,KAAK,CAAC,MAAM,CAAC,OAAsB;QACtC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI;YACA,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,mBAAmB,OAAO,CAAC,KAAK,WAAW,OAAO,CAAC,MAAM,IAAI,YAAY,EAAE,CAAC,CAAC;YAE3G,wBAAwB;YACxB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACjC,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,GAAG,EAAE;gBACjC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC,CAAC,8BAA8B;aAC7D;YAED,IAAI,OAAuB,CAAC;YAE5B,QAAQ,OAAO,CAAC,MAAM,IAAI,YAAY,EAAE;gBACpC,KAAK,YAAY;oBACb,OAAO,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;oBAC/C,MAAM;gBACV,KAAK,QAAQ;oBACT,OAAO,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;oBAC3C,MAAM;gBACV,KAAK,MAAM;oBACP,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;oBACzC,MAAM;gBACV,KAAK,eAAe;oBAChB,OAAO,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;oBAClD,MAAM;gBACV,KAAK,QAAQ;oBACT,OAAO,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;oBAC3C,MAAM;gBACV;oBACI,MAAM,IAAI,KAAK,CAAC,8BAA8B,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;aACvE;YAED,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC1C,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,uBAAuB,UAAU,aAAa,OAAO,CAAC,MAAM,UAAU,CAAC,CAAC;YAEtG,OAAO;gBACH,OAAO,EAAE,IAAI;gBACb,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;gBACnD,YAAY,EAAE,OAAO,CAAC,MAAM;gBAC5B,UAAU;aACb,CAAC;SAEL;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC1C,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,kBAAkB,KAAK,EAAE,CAAC,CAAC;YAEzD,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,OAAO,EAAE,EAAE;gBACX,UAAU;gBACV,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE;aAC1B,CAAC;SACL;IACL,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,OAAsB;QACjD,IAAI;YACA,oCAAoC;YACpC,MAAM,GAAG,GAAG,iCAAiC,kBAAkB,CAAC,OAAO,CAAC,KAAK,CAAC,wCAAwC,CAAC;YAEvH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC;YAClC,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YAEnC,MAAM,OAAO,GAAmB,EAAE,CAAC;YAEnC,kCAAkC;YAClC,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACf,OAAO,CAAC,IAAI,CAAC;oBACT,KAAK,EAAE,IAAI,CAAC,OAAO,IAAI,2BAA2B;oBAClD,GAAG,EAAE,IAAI,CAAC,WAAW,IAAI,wBAAwB;oBACjD,OAAO,EAAE,IAAI,CAAC,QAAQ;oBACtB,MAAM,EAAE,YAAY;oBACpB,IAAI,EAAE,CAAC;iBACV,CAAC,CAAC;aACN;YAED,qBAAqB;YACrB,IAAI,IAAI,CAAC,aAAa,EAAE;gBACpB,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,KAAU,EAAE,KAAa,EAAE,EAAE;oBACrD,IAAI,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,QAAQ,EAAE;wBAC9B,OAAO,CAAC,IAAI,CAAC;4BACT,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,eAAe;4BACpD,GAAG,EAAE,KAAK,CAAC,QAAQ;4BACnB,OAAO,EAAE,KAAK,CAAC,IAAI;4BACnB,MAAM,EAAE,YAAY;4BACpB,IAAI,EAAE,KAAK,GAAG,CAAC;yBAClB,CAAC,CAAC;qBACN;gBACL,CAAC,CAAC,CAAC;aACN;YAED,8CAA8C;YAC9C,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;gBACtB,OAAO,CAAC,IAAI,CAAC;oBACT,KAAK,EAAE,uBAAuB,OAAO,CAAC,KAAK,GAAG;oBAC9C,GAAG,EAAE,6BAA6B,kBAAkB,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;oBACrE,OAAO,EAAE,4CAA4C;oBACrD,MAAM,EAAE,YAAY;oBACpB,IAAI,EAAE,CAAC;iBACV,CAAC,CAAC;aACN;YAED,OAAO,OAAO,CAAC;SAElB;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,IAAI,KAAK,CAAC,6BAA6B,KAAK,EAAE,CAAC,CAAC;SACzD;IACL,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,OAAsB;QAC7C,wDAAwD;QACxD,+BAA+B;QAC/B,OAAO,CAAC;gBACJ,KAAK,EAAE,sBAAsB,OAAO,CAAC,KAAK,GAAG;gBAC7C,GAAG,EAAE,mCAAmC,kBAAkB,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBAC3E,OAAO,EAAE,wCAAwC;gBACjD,MAAM,EAAE,QAAQ;gBAChB,IAAI,EAAE,CAAC;aACV,CAAC,CAAC;IACP,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,OAAsB;QAC3C,+CAA+C;QAC/C,+BAA+B;QAC/B,OAAO,CAAC;gBACJ,KAAK,EAAE,oBAAoB,OAAO,CAAC,KAAK,GAAG;gBAC3C,GAAG,EAAE,iCAAiC,kBAAkB,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACzE,OAAO,EAAE,sCAAsC;gBAC/C,MAAM,EAAE,MAAM;gBACd,IAAI,EAAE,CAAC;aACV,CAAC,CAAC;IACP,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,OAAsB;QACpD,IAAI;YACA,yBAAyB;YACzB,MAAM,GAAG,GAAG,8EAA8E,kBAAkB,CAAC,OAAO,CAAC,KAAK,CAAC,qBAAqB,CAAC;YAEjJ,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC;YAClC,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YAEnC,MAAM,OAAO,GAAmB,EAAE,CAAC;YAEnC,IAAI,IAAI,CAAC,KAAK,EAAE;gBACZ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAS,EAAE,KAAa,EAAE,EAAE;oBAC5C,OAAO,CAAC,IAAI,CAAC;wBACT,KAAK,EAAE,IAAI,CAAC,KAAK;wBACjB,GAAG,EAAE,IAAI,CAAC,IAAI;wBACd,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,KAAK,CAAC;wBACzD,MAAM,EAAE,gBAAgB;wBACxB,aAAa,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,CAAC,WAAW,EAAE;wBAChE,IAAI,EAAE,KAAK,GAAG,CAAC;qBAClB,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC;aACN;YAED,OAAO,OAAO,CAAC;SAElB;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,IAAI,KAAK,CAAC,iCAAiC,KAAK,EAAE,CAAC,CAAC;SAC7D;IACL,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,OAAsB;QAC7C,IAAI;YACA,wBAAwB;YACxB,MAAM,GAAG,GAAG,gDAAgD,kBAAkB,CAAC,OAAO,CAAC,KAAK,CAAC,wBAAwB,CAAC;YAEtH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC;YAClC,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YAEnC,MAAM,OAAO,GAAmB,EAAE,CAAC;YAEnC,IAAI,IAAI,CAAC,KAAK,EAAE;gBACZ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAS,EAAE,KAAa,EAAE,EAAE;oBAC5C,OAAO,CAAC,IAAI,CAAC;wBACT,KAAK,EAAE,IAAI,CAAC,SAAS;wBACrB,GAAG,EAAE,IAAI,CAAC,QAAQ;wBAClB,OAAO,EAAE,IAAI,CAAC,WAAW,IAAI,0BAA0B;wBACvD,MAAM,EAAE,QAAQ;wBAChB,aAAa,EAAE,IAAI,CAAC,UAAU;wBAC9B,IAAI,EAAE,KAAK,GAAG,CAAC;qBAClB,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC;aACN;YAED,OAAO,OAAO,CAAC;SAElB;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,IAAI,KAAK,CAAC,yBAAyB,KAAK,EAAE,CAAC,CAAC;SACrD;IACL,CAAC;IAEM,KAAK,CAAC,iBAAiB,CAAC,KAAa,EAAE,WAAoB;QAC9D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI;YACA,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,gCAAgC,KAAK,GAAG,CAAC,CAAC;YAExE,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,eAAe,CACxD,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,EAClC,EAAE,OAAO,EAAE,WAAW,IAAI,MAAM,EAAE,OAAO,EAAE,oBAAoB,EAAE,CACpE,CAAC;YAEF,MAAM,OAAO,GAAmB,EAAE,CAAC;YACnC,IAAI,IAAI,GAAG,CAAC,CAAC;YAEb,KAAK,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,IAAI,aAAa,EAAE;gBACxC,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE;oBACzB,OAAO,CAAC,IAAI,CAAC;wBACT,KAAK,EAAE,GAAG,GAAG,CAAC,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,EAAE;wBACpD,GAAG,EAAE,GAAG,CAAC,QAAQ,EAAE;wBACnB,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,IAAI;wBAC3B,MAAM,EAAE,WAAW;wBACnB,IAAI,EAAE,IAAI,EAAE;qBACf,CAAC,CAAC;iBACN;aACJ;YAED,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE1C,OAAO;gBACH,OAAO,EAAE,IAAI;gBACb,KAAK;gBACL,OAAO;gBACP,YAAY,EAAE,OAAO,CAAC,MAAM;gBAC5B,UAAU;aACb,CAAC;SAEL;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC1C,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,KAAK;gBACL,OAAO,EAAE,EAAE;gBACX,UAAU;gBACV,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE;aAC1B,CAAC;SACL;IACL,CAAC;IAEM,gBAAgB;QACnB,OAAO,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC;IACnC,CAAC;IAEM,kBAAkB;QACrB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,wBAAwB,CAAC,CAAC;IAC5D,CAAC;IAEM,KAAK,CAAC,gBAAgB,CAAC,MAAoB;QAC9C,IAAI;YACA,IAAI,MAAM,CAAC,MAAM,KAAK,WAAW,EAAE;gBAC/B,sBAAsB;gBACtB,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBACzC,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;gBAC9D,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;aAClD;iBAAM;gBACH,oBAAoB;gBACpB,MAAM,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;aAC/D;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,iCAAiC,KAAK,EAAE,CAAC,CAAC;YACxE,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,iCAAiC,KAAK,EAAE,CAAC,CAAC;SAC5E;IACL,CAAC;IAEM,KAAK,CAAC,iBAAiB,CAAC,QAAwB;QACnD,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;YACnB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,kBAAkB,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC;YACnE,OAAO;SACV;QAED,6CAA6C;QAC7C,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC1C,eAAe,EACf,mBAAmB,QAAQ,CAAC,KAAK,EAAE,EACnC,MAAM,CAAC,UAAU,CAAC,GAAG,EACrB;YACI,aAAa,EAAE,IAAI;YACnB,uBAAuB,EAAE,IAAI;SAChC,CACJ,CAAC;QAEF,KAAK,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QAEzD,0BAA0B;QAC1B,KAAK,CAAC,OAAO,CAAC,mBAAmB,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YAChD,IAAI,OAAO,CAAC,OAAO,KAAK,YAAY,EAAE;gBAClC,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBAC/C,IAAI,MAAM,EAAE;oBACR,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;iBACvC;aACJ;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,oBAAoB,CAAC,QAAwB;QACjD,MAAM,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;6DACP,KAAK;2CACvB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC;wCAChC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC;4CACvB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC;;kDAEzB,MAAM,CAAC,MAAM;sBACzC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,6BAA6B,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,kBAAkB,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE;;;SAGlI,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAEZ,OAAO;;;;;;;;;;;;;;;;;0CAiB2B,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC;2BAC9C,QAAQ,CAAC,YAAY,eAAe,QAAQ,CAAC,UAAU;;cAEpE,WAAW;;;;;;;;gBAQT,CAAC;IACb,CAAC;IAEO,SAAS,CAAC,IAAY;QAC1B,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;IAClE,CAAC;IAEO,UAAU,CAAC,IAAY;QAC3B,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC1C,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC;QACvB,OAAO,GAAG,CAAC,SAAS,CAAC;IACzB,CAAC;IAEM,OAAO;QACV,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;IACjC,CAAC;CACJ;AA9WD,gDA8WC"}