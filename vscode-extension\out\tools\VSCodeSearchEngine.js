"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VSCodeSearchEngine = void 0;
const vscode = __importStar(require("vscode"));
class VSCodeSearchEngine {
    constructor() {
        this.searchHistory = [];
        this.outputChannel = vscode.window.createOutputChannel('Agent Zero Search Engine');
    }
    async search(request) {
        const startTime = Date.now();
        try {
            this.outputChannel.appendLine(`Searching for: "${request.query}" using ${request.engine || 'duckduckgo'}`);
            // Add to search history
            this.searchHistory.push(request);
            if (this.searchHistory.length > 100) {
                this.searchHistory.shift(); // Keep only last 100 searches
            }
            let results;
            switch (request.engine || 'duckduckgo') {
                case 'duckduckgo':
                    results = await this.searchDuckDuckGo(request);
                    break;
                case 'google':
                    results = await this.searchGoogle(request);
                    break;
                case 'bing':
                    results = await this.searchBing(request);
                    break;
                case 'stackoverflow':
                    results = await this.searchStackOverflow(request);
                    break;
                case 'github':
                    results = await this.searchGitHub(request);
                    break;
                default:
                    throw new Error(`Unsupported search engine: ${request.engine}`);
            }
            const searchTime = Date.now() - startTime;
            this.outputChannel.appendLine(`Search completed in ${searchTime}ms, found ${results.length} results`);
            return {
                success: true,
                query: request.query,
                results: results.slice(0, request.maxResults || 10),
                totalResults: results.length,
                searchTime
            };
        }
        catch (error) {
            const searchTime = Date.now() - startTime;
            this.outputChannel.appendLine(`Search failed: ${error}`);
            return {
                success: false,
                query: request.query,
                results: [],
                searchTime,
                error: error.toString()
            };
        }
    }
    async searchDuckDuckGo(request) {
        try {
            // Use DuckDuckGo Instant Answer API
            const url = `https://api.duckduckgo.com/?q=${encodeURIComponent(request.query)}&format=json&no_html=1&skip_disambig=1`;
            const response = await fetch(url);
            const data = await response.json();
            const results = [];
            // Add instant answer if available
            if (data.Abstract) {
                results.push({
                    title: data.Heading || 'DuckDuckGo Instant Answer',
                    url: data.AbstractURL || 'https://duckduckgo.com',
                    snippet: data.Abstract,
                    source: 'DuckDuckGo',
                    rank: 1
                });
            }
            // Add related topics
            if (data.RelatedTopics) {
                data.RelatedTopics.forEach((topic, index) => {
                    if (topic.Text && topic.FirstURL) {
                        results.push({
                            title: topic.Text.split(' - ')[0] || 'Related Topic',
                            url: topic.FirstURL,
                            snippet: topic.Text,
                            source: 'DuckDuckGo',
                            rank: index + 2
                        });
                    }
                });
            }
            // If no results from API, create a search URL
            if (results.length === 0) {
                results.push({
                    title: `Search results for "${request.query}"`,
                    url: `https://duckduckgo.com/?q=${encodeURIComponent(request.query)}`,
                    snippet: 'Click to view search results on DuckDuckGo',
                    source: 'DuckDuckGo',
                    rank: 1
                });
            }
            return results;
        }
        catch (error) {
            throw new Error(`DuckDuckGo search failed: ${error}`);
        }
    }
    async searchGoogle(request) {
        // Note: This would require Google Custom Search API key
        // For now, return a search URL
        return [{
                title: `Google search for "${request.query}"`,
                url: `https://www.google.com/search?q=${encodeURIComponent(request.query)}`,
                snippet: 'Click to view search results on Google',
                source: 'Google',
                rank: 1
            }];
    }
    async searchBing(request) {
        // Note: This would require Bing Search API key
        // For now, return a search URL
        return [{
                title: `Bing search for "${request.query}"`,
                url: `https://www.bing.com/search?q=${encodeURIComponent(request.query)}`,
                snippet: 'Click to view search results on Bing',
                source: 'Bing',
                rank: 1
            }];
    }
    async searchStackOverflow(request) {
        try {
            // Use Stack Exchange API
            const url = `https://api.stackexchange.com/2.3/search?order=desc&sort=relevance&intitle=${encodeURIComponent(request.query)}&site=stackoverflow`;
            const response = await fetch(url);
            const data = await response.json();
            const results = [];
            if (data.items) {
                data.items.forEach((item, index) => {
                    results.push({
                        title: item.title,
                        url: item.link,
                        snippet: this.stripHtml(item.body_markdown || item.title),
                        source: 'Stack Overflow',
                        publishedDate: new Date(item.creation_date * 1000).toISOString(),
                        rank: index + 1
                    });
                });
            }
            return results;
        }
        catch (error) {
            throw new Error(`Stack Overflow search failed: ${error}`);
        }
    }
    async searchGitHub(request) {
        try {
            // Use GitHub Search API
            const url = `https://api.github.com/search/repositories?q=${encodeURIComponent(request.query)}&sort=stars&order=desc`;
            const response = await fetch(url);
            const data = await response.json();
            const results = [];
            if (data.items) {
                data.items.forEach((item, index) => {
                    results.push({
                        title: item.full_name,
                        url: item.html_url,
                        snippet: item.description || 'No description available',
                        source: 'GitHub',
                        publishedDate: item.created_at,
                        rank: index + 1
                    });
                });
            }
            return results;
        }
        catch (error) {
            throw new Error(`GitHub search failed: ${error}`);
        }
    }
    async searchInWorkspace(query, filePattern) {
        const startTime = Date.now();
        try {
            this.outputChannel.appendLine(`Searching in workspace for: "${query}"`);
            const searchResults = await vscode.workspace.findTextInFiles({ pattern: query, isRegex: false }, { include: filePattern || '**/*', exclude: '**/node_modules/**' });
            const results = [];
            let rank = 1;
            for (const [uri, matches] of searchResults) {
                for (const match of matches) {
                    results.push({
                        title: `${uri.fsPath}:${match.range.start.line + 1}`,
                        url: uri.toString(),
                        snippet: match.preview.text,
                        source: 'Workspace',
                        rank: rank++
                    });
                }
            }
            const searchTime = Date.now() - startTime;
            return {
                success: true,
                query,
                results,
                totalResults: results.length,
                searchTime
            };
        }
        catch (error) {
            const searchTime = Date.now() - startTime;
            return {
                success: false,
                query,
                results: [],
                searchTime,
                error: error.toString()
            };
        }
    }
    getSearchHistory() {
        return [...this.searchHistory];
    }
    clearSearchHistory() {
        this.searchHistory = [];
        this.outputChannel.appendLine('Search history cleared');
    }
    async openSearchResult(result) {
        try {
            if (result.source === 'Workspace') {
                // Open workspace file
                const uri = vscode.Uri.parse(result.url);
                const document = await vscode.workspace.openTextDocument(uri);
                await vscode.window.showTextDocument(document);
            }
            else {
                // Open external URL
                await vscode.env.openExternal(vscode.Uri.parse(result.url));
            }
        }
        catch (error) {
            this.outputChannel.appendLine(`Failed to open search result: ${error}`);
            vscode.window.showErrorMessage(`Failed to open search result: ${error}`);
        }
    }
    async showSearchResults(response) {
        if (!response.success) {
            vscode.window.showErrorMessage(`Search failed: ${response.error}`);
            return;
        }
        // Create a webview to display search results
        const panel = vscode.window.createWebviewPanel('searchResults', `Search Results: ${response.query}`, vscode.ViewColumn.Two, {
            enableScripts: true,
            retainContextWhenHidden: true
        });
        panel.webview.html = this.getSearchResultsHtml(response);
        // Handle webview messages
        panel.webview.onDidReceiveMessage(async (message) => {
            if (message.command === 'openResult') {
                const result = response.results[message.index];
                if (result) {
                    await this.openSearchResult(result);
                }
            }
        });
    }
    getSearchResultsHtml(response) {
        const resultsHtml = response.results.map((result, index) => `
            <div class="search-result" onclick="openResult(${index})">
                <h3 class="result-title">${this.escapeHtml(result.title)}</h3>
                <p class="result-url">${this.escapeHtml(result.url)}</p>
                <p class="result-snippet">${this.escapeHtml(result.snippet)}</p>
                <div class="result-meta">
                    <span class="result-source">${result.source}</span>
                    ${result.publishedDate ? `<span class="result-date">${new Date(result.publishedDate).toLocaleDateString()}</span>` : ''}
                </div>
            </div>
        `).join('');
        return `
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body { font-family: var(--vscode-font-family); padding: 20px; }
                .search-header { margin-bottom: 20px; }
                .search-result { margin-bottom: 20px; padding: 15px; border: 1px solid var(--vscode-panel-border); border-radius: 5px; cursor: pointer; }
                .search-result:hover { background-color: var(--vscode-list-hoverBackground); }
                .result-title { margin: 0 0 5px 0; color: var(--vscode-textLink-foreground); }
                .result-url { margin: 0 0 10px 0; color: var(--vscode-descriptionForeground); font-size: 0.9em; }
                .result-snippet { margin: 0 0 10px 0; }
                .result-meta { display: flex; gap: 15px; font-size: 0.8em; color: var(--vscode-descriptionForeground); }
            </style>
        </head>
        <body>
            <div class="search-header">
                <h1>Search Results for "${this.escapeHtml(response.query)}"</h1>
                <p>Found ${response.totalResults} results in ${response.searchTime}ms</p>
            </div>
            ${resultsHtml}
            <script>
                const vscode = acquireVsCodeApi();
                function openResult(index) {
                    vscode.postMessage({ command: 'openResult', index: index });
                }
            </script>
        </body>
        </html>`;
    }
    stripHtml(html) {
        return html.replace(/<[^>]*>/g, '').substring(0, 200) + '...';
    }
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    dispose() {
        this.outputChannel.dispose();
    }
}
exports.VSCodeSearchEngine = VSCodeSearchEngine;
//# sourceMappingURL=VSCodeSearchEngine.js.map