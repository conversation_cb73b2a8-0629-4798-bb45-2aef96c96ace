{"version": 3, "file": "ToolManager.js", "sourceRoot": "", "sources": ["../../src/core/ToolManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,2CAA6B;AAC7B,uCAAyB;AACzB,gEAA6D;AAC7D,4DAAyD;AACzD,0DAAuD;AACvD,oEAAiE;AACjE,wEAAqE;AACrE,4DAAyD;AAkBzD,MAAa,WAAW;IAYpB,YAAoB,OAAgC;QAAhC,YAAO,GAAP,OAAO,CAAyB;QAX5C,yBAAoB,GAAwD,IAAI,MAAM,CAAC,YAAY,EAAkC,CAAC;QACrI,wBAAmB,GAAiD,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;QAErG,UAAK,GAAsB,IAAI,GAAG,EAAE,CAAC;QASzC,4BAA4B;QAC5B,IAAI,CAAC,UAAU,GAAG,IAAI,mCAAgB,EAAE,CAAC;QACzC,IAAI,CAAC,QAAQ,GAAG,IAAI,+BAAc,EAAE,CAAC;QACrC,IAAI,CAAC,OAAO,GAAG,IAAI,6BAAa,EAAE,CAAC;QACnC,IAAI,CAAC,YAAY,GAAG,IAAI,uCAAkB,EAAE,CAAC;QAC7C,IAAI,CAAC,cAAc,GAAG,IAAI,2CAAoB,EAAE,CAAC;QACjD,IAAI,CAAC,QAAQ,GAAG,IAAI,+BAAc,EAAE,CAAC;QAErC,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAEO,eAAe;QACnB,4BAA4B;QAC5B,MAAM,YAAY,GAAW;YACzB;gBACI,EAAE,EAAE,aAAa;gBACjB,IAAI,EAAE,aAAa;gBACnB,WAAW,EAAE,+BAA+B;gBAC5C,QAAQ,EAAE,iBAAiB;gBAC3B,OAAO,EAAE,IAAI;aAChB;YACD;gBACI,EAAE,EAAE,gBAAgB;gBACpB,IAAI,EAAE,gBAAgB;gBACtB,WAAW,EAAE,mCAAmC;gBAChD,QAAQ,EAAE,aAAa;gBACvB,OAAO,EAAE,IAAI;aAChB;YACD;gBACI,EAAE,EAAE,UAAU;gBACd,IAAI,EAAE,UAAU;gBAChB,WAAW,EAAE,2BAA2B;gBACxC,QAAQ,EAAE,QAAQ;gBAClB,OAAO,EAAE,IAAI;aAChB;YACD;gBACI,EAAE,EAAE,SAAS;gBACb,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,oDAAoD;gBACjE,QAAQ,EAAE,KAAK;gBACf,OAAO,EAAE,IAAI;aAChB;YACD;gBACI,EAAE,EAAE,eAAe;gBACnB,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,8CAA8C;gBAC3D,QAAQ,EAAE,KAAK;gBACf,OAAO,EAAE,IAAI;aAChB;YACD;gBACI,EAAE,EAAE,iBAAiB;gBACrB,IAAI,EAAE,iBAAiB;gBACvB,WAAW,EAAE,gCAAgC;gBAC7C,QAAQ,EAAE,aAAa;gBACvB,OAAO,EAAE,IAAI;aAChB;YACD;gBACI,EAAE,EAAE,UAAU;gBACd,IAAI,EAAE,UAAU;gBAChB,WAAW,EAAE,gCAAgC;gBAC7C,QAAQ,EAAE,QAAQ;gBAClB,OAAO,EAAE,IAAI;aAChB;YACD;gBACI,EAAE,EAAE,gBAAgB;gBACpB,IAAI,EAAE,gBAAgB;gBACtB,WAAW,EAAE,6BAA6B;gBAC1C,QAAQ,EAAE,UAAU;gBACpB,OAAO,EAAE,IAAI;aAChB;YACD;gBACI,EAAE,EAAE,aAAa;gBACjB,IAAI,EAAE,aAAa;gBACnB,WAAW,EAAE,4BAA4B;gBACzC,QAAQ,EAAE,QAAQ;gBAClB,OAAO,EAAE,IAAI;aAChB;YACD;gBACI,EAAE,EAAE,aAAa;gBACjB,IAAI,EAAE,aAAa;gBACnB,WAAW,EAAE,8BAA8B;gBAC3C,QAAQ,EAAE,QAAQ;gBAClB,OAAO,EAAE,IAAI;aAChB;YACD;gBACI,EAAE,EAAE,aAAa;gBACjB,IAAI,EAAE,aAAa;gBACnB,WAAW,EAAE,mCAAmC;gBAChD,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,IAAI;aAChB;SACJ,CAAC;QAEF,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACxB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAEO,eAAe;QACnB,uCAAuC;QACvC,MAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/D,IAAI,eAAe,EAAE;YACjB,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;YAChF,IAAI,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;gBAC1B,6CAA6C;gBAC7C,sDAAsD;aACzD;SACJ;IACL,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,IAAY,EAAE,QAAgB;QACnD,IAAI;YACA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;gBAC3C,IAAI;gBACJ,QAAQ;gBACR,OAAO,EAAE,KAAK;aACjB,CAAC,CAAC;YAEH,OAAO;gBACH,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,IAAI,EAAE;oBACF,aAAa,EAAE,MAAM,CAAC,aAAa;oBACnC,QAAQ,EAAE,MAAM,CAAC,QAAQ;iBAC5B;aACJ,CAAC;SACL;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,2BAA2B,KAAK,EAAE;aAC5C,CAAC;SACL;IACL,CAAC;IAEM,KAAK,CAAC,QAAQ,CAAC,QAAgB;QAClC,IAAI;YACA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC;gBAClD,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,QAAQ;aACjB,CAAC,CAAC;YAEH,OAAO;gBACH,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,MAAM,EAAE,MAAM,CAAC,OAAO,IAAI,EAAE;gBAC5B,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,IAAI,EAAE,MAAM,CAAC,QAAQ;aACxB,CAAC;SACL;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,wBAAwB,KAAK,EAAE;aACzC,CAAC;SACL;IACL,CAAC;IAEM,KAAK,CAAC,SAAS,CAAC,QAAgB,EAAE,OAAe;QACpD,IAAI;YACA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC;gBAClD,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,QAAQ;gBACd,OAAO;aACV,CAAC,CAAC;YAEH,OAAO;gBACH,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,MAAM,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,8BAA8B,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE;gBACtE,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,IAAI,EAAE,MAAM,CAAC,QAAQ;aACxB,CAAC;SACL;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,yBAAyB,KAAK,EAAE;aAC1C,CAAC;SACL;IACL,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,OAAe,EAAE,cAAuB;QAC7D,IAAI;YACA,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAC1C,cAAc,IAAI,MAAM,EACxB,oBAAoB,CACvB,CAAC;YAEF,MAAM,aAAa,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CACtC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAC5D,CAAC;YAEF,OAAO;gBACH,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,SAAS,aAAa,CAAC,MAAM,iBAAiB;gBACtD,IAAI,EAAE;oBACF,KAAK,EAAE,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC;oBACvC,KAAK,EAAE,aAAa,CAAC,MAAM;iBAC9B;aACJ,CAAC;SACL;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,2BAA2B,KAAK,EAAE;aAC5C,CAAC;SACL;IACL,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,MAAc,EAAE,UAA+B;QACpE,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACpC,IAAI,CAAC,IAAI,EAAE;YACP,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,mBAAmB,MAAM,EAAE;aACrC,CAAC;SACL;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,qBAAqB,MAAM,EAAE;aACvC,CAAC;SACL;QAED,IAAI;YACA,QAAQ,MAAM,EAAE;gBACZ,KAAK,aAAa;oBACd,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;gBACnD,KAAK,gBAAgB;oBACjB,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAC;gBACxE,KAAK,UAAU;oBACX,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;gBACjD,KAAK,SAAS;oBACV,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;gBAChD,KAAK,eAAe;oBAChB,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;gBAC/C,KAAK,iBAAiB;oBAClB,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;gBAC5C;oBACI,OAAO;wBACH,OAAO,EAAE,KAAK;wBACd,MAAM,EAAE,EAAE;wBACV,KAAK,EAAE,kCAAkC,MAAM,EAAE;qBACpD,CAAC;aACT;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,0BAA0B,KAAK,EAAE;aAC3C,CAAC;SACL;IACL,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,UAA+B;QAC1D,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,UAAU,CAAC;QAEvD,QAAQ,MAAM,EAAE;YACZ,KAAK,MAAM;gBACP,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACzC,KAAK,OAAO;gBACR,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YACnD;gBACI,OAAO;oBACH,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,EAAE;oBACV,KAAK,EAAE,+BAA+B,MAAM,EAAE;iBACjD,CAAC;SACT;IACL,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,UAA+B;QACxD,IAAI;YACA,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,UAAU,CAAC;YAEzD,QAAQ,MAAM,EAAE;gBACZ,KAAK,SAAS;oBACV,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC;wBAC9C,OAAO,EAAE,OAAO;wBAChB,WAAW,EAAE,IAAI;qBACpB,CAAC,CAAC;oBACH,OAAO;wBACH,OAAO,EAAE,MAAM,CAAC,OAAO;wBACvB,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,EAAE;wBAC3B,KAAK,EAAE,MAAM,CAAC,KAAK;wBACnB,IAAI,EAAE,EAAE,UAAU,EAAE,MAAM,CAAC,UAAU,EAAE;qBAC1C,CAAC;gBAEN,KAAK,QAAQ;oBACT,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;oBAC/D,OAAO;wBACH,OAAO,EAAE,IAAI;wBACb,MAAM,EAAE,qBAAqB,aAAa,EAAE;wBAC5C,IAAI,EAAE,EAAE,UAAU,EAAE,aAAa,EAAE;qBACtC,CAAC;gBAEN,KAAK,QAAQ;oBACT,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;oBAClE,OAAO;wBACH,OAAO,EAAE,QAAQ;wBACjB,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,yBAAyB,UAAU,EAAE,CAAC,CAAC,CAAC,2BAA2B;qBACzF,CAAC;gBAEN,KAAK,OAAO;oBACR,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;oBAC7D,OAAO;wBACH,OAAO,EAAE,MAAM;wBACf,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,oBAAoB,UAAU,EAAE,CAAC,CAAC,CAAC,0BAA0B;qBACjF,CAAC;gBAEN;oBACI,MAAM,IAAI,KAAK,CAAC,4BAA4B,MAAM,EAAE,CAAC,CAAC;aAC7D;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,8BAA8B,KAAK,EAAE;aAC/C,CAAC;SACL;IACL,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,UAA+B;QACvD,IAAI;YACA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YAC7D,OAAO;gBACH,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBAC/D,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,IAAI,EAAE,MAAM;aACf,CAAC;SACL;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,6BAA6B,KAAK,EAAE;aAC9C,CAAC;SACL;IACL,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,UAA+B;QACtD,IAAI;YACA,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,UAAU,CAAC;YAE7D,IAAI,MAAM,CAAC;YACX,IAAI,UAAU,KAAK,WAAW,EAAE;gBAC5B,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,CAAC,WAAW,CAAC,CAAC;aACrF;iBAAM;gBACH,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;oBACpC,KAAK;oBACL,MAAM;oBACN,UAAU;iBACb,CAAC,CAAC;aACN;YAED,OAAO;gBACH,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;gBAC/C,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,IAAI,EAAE,MAAM;aACf,CAAC;SACL;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,4BAA4B,KAAK,EAAE;aAC7C,CAAC;SACL;IACL,CAAC;IAEO,KAAK,CAAC,SAAS,CAAC,UAA+B;QACnD,IAAI;YACA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;YACzE,OAAO;gBACH,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,IAAI,EAAE,MAAM,CAAC,IAAI;aACpB,CAAC;SACL;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,yBAAyB,KAAK,EAAE;aAC1C,CAAC;SACL;IACL,CAAC;IAED,kCAAkC;IAClC,WAAW,CAAC,OAAa;QACrB,MAAM,IAAI,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;QACrF,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACvC,IAAI,CAAC,OAAO,GAAG,GAAG,OAAO,CAAC,IAAI,KAAK,OAAO,CAAC,WAAW,EAAE,CAAC;QACzD,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,cAAc,CAAC;QACrE,IAAI,CAAC,QAAQ,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC;QACjF,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,WAAW,CAAC,OAAc;QACtB,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;SAC3D;QACD,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC/B,CAAC;IAED,OAAO;QACH,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC;IACrC,CAAC;IAEM,OAAO;QACV,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;QAEnB,6BAA6B;QAC7B,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QAC1B,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;QACxB,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QACvB,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;QAC5B,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;QAC9B,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;IAC5B,CAAC;CACJ;AAtbD,kCAsbC"}