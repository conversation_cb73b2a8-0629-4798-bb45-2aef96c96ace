.vscode/**
.vscode-test/**
src/**
.gitignore
.yarnrc
vsc-extension-quickstart.md
**/tsconfig.json
**/.eslintrc.json
**/*.map
**/*.ts
**/node_modules/**
**/.DS_Store
**/*.pyc
**/__pycache__/**
.pytest_cache/**
*.log
.env
.env.local
.env.development
.env.test
.env.production
coverage/**
.nyc_output/**
test/**
tests/**
**/*.test.js
**/*.test.ts
**/*.spec.js
**/*.spec.ts
.github/**
docs/**
examples/**
scripts/**
*.md
!README.md
!CHANGELOG.md
LICENSE
.gitattributes
.editorconfig
.prettierrc
.prettierignore
webpack.config.js
rollup.config.js
babel.config.js
jest.config.js
.babelrc
.eslintignore
tsconfig.*.json
*.tsbuildinfo
.cache/**
dist/**
build/**
tmp/**
temp/**
.tmp/**
.temp/**
*.orig
*.rej
*~
.#*
#*#
.*.swp
.*.swo
*.bak
*.backup
.vscode-test-web/**
out/test/**
src/test/**
