/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */

import { AccountInfo } from "@azure/msal-common/browser";

/**
 * ClearCacheRequest
 * - correlationId          - Unique GUID set per request to trace a request end-to-end for telemetry purposes.
 * - account                - Account object that will be logged out of. All tokens tied to this account will be cleared.
 */
export type ClearCacheRequest = {
    correlationId?: string;
    account?: AccountInfo | null;
};
