"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigurationManager = void 0;
const vscode = __importStar(require("vscode"));
class ConfigurationManager {
    constructor() {
        this.disposables = [];
        this.config = vscode.workspace.getConfiguration('agent-zero');
        // Listen for configuration changes
        this.disposables.push(vscode.workspace.onDidChangeConfiguration(e => {
            if (e.affectsConfiguration('agent-zero')) {
                this.config = vscode.workspace.getConfiguration('agent-zero');
                this.onConfigurationChanged();
            }
        }));
    }
    getConfiguration() {
        return {
            models: {
                chat: {
                    provider: this.config.get('models.chat.provider', 'openai'),
                    name: this.config.get('models.chat.name', 'gpt-4'),
                    apiBase: this.config.get('models.chat.apiBase'),
                    ctxLength: this.config.get('models.chat.ctxLength'),
                    limitRequests: this.config.get('models.chat.limitRequests'),
                    limitInput: this.config.get('models.chat.limitInput'),
                    limitOutput: this.config.get('models.chat.limitOutput'),
                    vision: this.config.get('models.chat.vision', false),
                    kwargs: this.config.get('models.chat.kwargs', {})
                },
                utility: {
                    provider: this.config.get('models.utility.provider', 'openai'),
                    name: this.config.get('models.utility.name', 'gpt-3.5-turbo'),
                    apiBase: this.config.get('models.utility.apiBase'),
                    ctxLength: this.config.get('models.utility.ctxLength'),
                    limitRequests: this.config.get('models.utility.limitRequests'),
                    limitInput: this.config.get('models.utility.limitInput'),
                    limitOutput: this.config.get('models.utility.limitOutput'),
                    vision: this.config.get('models.utility.vision', false),
                    kwargs: this.config.get('models.utility.kwargs', {})
                },
                embeddings: {
                    provider: this.config.get('models.embeddings.provider', 'openai'),
                    name: this.config.get('models.embeddings.name', 'text-embedding-ada-002'),
                    apiBase: this.config.get('models.embeddings.apiBase'),
                    ctxLength: this.config.get('models.embeddings.ctxLength'),
                    limitRequests: this.config.get('models.embeddings.limitRequests'),
                    limitInput: this.config.get('models.embeddings.limitInput'),
                    limitOutput: this.config.get('models.embeddings.limitOutput'),
                    vision: this.config.get('models.embeddings.vision', false),
                    kwargs: this.config.get('models.embeddings.kwargs', {})
                },
                browser: {
                    provider: this.config.get('models.browser.provider', 'openai'),
                    name: this.config.get('models.browser.name', 'gpt-4'),
                    apiBase: this.config.get('models.browser.apiBase'),
                    ctxLength: this.config.get('models.browser.ctxLength'),
                    limitRequests: this.config.get('models.browser.limitRequests'),
                    limitInput: this.config.get('models.browser.limitInput'),
                    limitOutput: this.config.get('models.browser.limitOutput'),
                    vision: this.config.get('models.browser.vision', true),
                    kwargs: this.config.get('models.browser.kwargs', {})
                }
            },
            agent: {
                prompts: this.config.get('agent.prompts', 'agent0'),
                memory: this.config.get('agent.memory', 'default'),
                knowledge: this.config.get('agent.knowledge', 'custom')
            },
            execution: {
                enableDocker: this.config.get('execution.enableDocker', false),
                enableSSH: this.config.get('execution.enableSSH', false),
                dockerName: this.config.get('execution.dockerName', 'A0-dev'),
                dockerImage: this.config.get('execution.dockerImage', 'frdel/agent-zero-run:development'),
                dockerPorts: this.config.get('execution.dockerPorts', { "22/tcp": 55022, "80/tcp": 55080 })
            },
            ui: {
                theme: this.config.get('ui.theme', 'auto'),
                language: this.config.get('ui.language', 'en')
            },
            mcp: {
                servers: this.config.get('mcp.servers', '')
            }
        };
    }
    async updateConfiguration(key, value, target) {
        await this.config.update(key, value, target || vscode.ConfigurationTarget.Workspace);
    }
    get(key, defaultValue) {
        return this.config.get(key, defaultValue);
    }
    onConfigurationChanged() {
        // Emit configuration change event
        vscode.commands.executeCommand('agent-zero.configurationChanged', this.getConfiguration());
    }
    dispose() {
        this.disposables.forEach(d => d.dispose());
    }
}
exports.ConfigurationManager = ConfigurationManager;
//# sourceMappingURL=ConfigurationManager.js.map