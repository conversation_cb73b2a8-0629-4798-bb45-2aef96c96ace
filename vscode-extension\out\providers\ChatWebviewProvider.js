"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatWebviewProvider = void 0;
const vscode = __importStar(require("vscode"));
class ChatWebviewProvider {
    constructor(context, agentManager) {
        this.context = context;
        this.agentManager = agentManager;
    }
    resolveWebviewView(webviewView, context, _token) {
        this._view = webviewView;
        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [
                this.context.extensionUri
            ]
        };
        webviewView.webview.html = this.getHtmlForWebview(webviewView.webview);
        // Handle messages from the webview
        webviewView.webview.onDidReceiveMessage(async (data) => {
            switch (data.type) {
                case 'sendMessage':
                    await this.handleSendMessage(data.message);
                    break;
                case 'clearChat':
                    this.clearChat();
                    break;
                case 'exportChat':
                    await this.exportChat();
                    break;
                case 'importChat':
                    await this.importChat();
                    break;
                case 'changeAgent':
                    this.changeActiveAgent(data.agentId);
                    break;
                case 'showProjectSummary':
                    await this.handleShowProjectSummary();
                    break;
                case 'askAboutProject':
                    await this.handleAskAboutProject(data.question);
                    break;
            }
        }, undefined, this.context.subscriptions);
    }
    async handleSendMessage(message) {
        if (!this._view) {
            return;
        }
        try {
            // Show user message immediately
            this._view.webview.postMessage({
                type: 'addMessage',
                message: {
                    id: Date.now().toString(),
                    content: message,
                    sender: 'user',
                    timestamp: new Date().toISOString()
                }
            });
            // Show typing indicator
            this._view.webview.postMessage({
                type: 'setTyping',
                isTyping: true
            });
            // Send message to agent
            const response = await this.agentManager.sendMessage(message);
            // Hide typing indicator
            this._view.webview.postMessage({
                type: 'setTyping',
                isTyping: false
            });
            // Show agent response
            this._view.webview.postMessage({
                type: 'addMessage',
                message: {
                    id: (Date.now() + 1).toString(),
                    content: response,
                    sender: 'agent',
                    timestamp: new Date().toISOString()
                }
            });
        }
        catch (error) {
            // Hide typing indicator
            this._view.webview.postMessage({
                type: 'setTyping',
                isTyping: false
            });
            // Show error message
            this._view.webview.postMessage({
                type: 'addMessage',
                message: {
                    id: (Date.now() + 1).toString(),
                    content: `Error: ${error}`,
                    sender: 'system',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    clearChat() {
        if (this._view) {
            this._view.webview.postMessage({
                type: 'clearMessages'
            });
        }
    }
    async exportChat() {
        // Implementation for exporting chat history
        vscode.window.showInformationMessage('Chat export functionality coming soon!');
    }
    async importChat() {
        // Implementation for importing chat history
        vscode.window.showInformationMessage('Chat import functionality coming soon!');
    }
    changeActiveAgent(agentId) {
        if (this.agentManager.setActiveAgent(agentId)) {
            const agent = this.agentManager.getAgent(agentId);
            if (this._view && agent) {
                this._view.webview.postMessage({
                    type: 'agentChanged',
                    agent: {
                        id: agent.id,
                        name: agent.name,
                        type: agent.type,
                        status: agent.status
                    }
                });
            }
        }
    }
    async handleShowProjectSummary() {
        try {
            const summary = await this.agentManager.getProjectSummary();
            // Add project summary as a system message
            this.addMessage('system', 'Project Summary', summary);
            this.updateWebview();
        }
        catch (error) {
            this.addMessage('system', 'Error', `Failed to get project summary: ${error}`);
            this.updateWebview();
        }
    }
    async handleAskAboutProject(question) {
        if (!question)
            return;
        try {
            // Add user question to chat
            this.addMessage('user', 'You', question);
            this.updateWebview();
            // Get answer from agent
            const answer = await this.agentManager.askAboutProject(question);
            // Add agent response to chat
            this.addMessage('assistant', 'Agent Zero', answer);
            this.updateWebview();
        }
        catch (error) {
            this.addMessage('system', 'Error', `Failed to get project information: ${error}`);
            this.updateWebview();
        }
    }
    show() {
        if (this._view) {
            this._view.show?.(true);
        }
    }
    getHtmlForWebview(webview) {
        // Get the local path to main script run in the webview
        const scriptPathOnDisk = vscode.Uri.joinPath(this.context.extensionUri, 'media', 'main.js');
        const scriptUri = webview.asWebviewUri(scriptPathOnDisk);
        // Get the local path to CSS file
        const stylePathOnDisk = vscode.Uri.joinPath(this.context.extensionUri, 'media', 'main.css');
        const styleUri = webview.asWebviewUri(stylePathOnDisk);
        // Use a nonce to only allow specific scripts to be run
        const nonce = getNonce();
        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource}; script-src 'nonce-${nonce}';">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <link href="${styleUri}" rel="stylesheet">
            <title>Agent Zero Chat</title>
        </head>
        <body>
            <div class="chat-container">
                <div class="chat-header">
                    <div class="agent-info">
                        <span class="agent-name" id="agentName">Agent Zero</span>
                        <span class="agent-status" id="agentStatus">idle</span>
                    </div>
                    <div class="chat-controls">
                        <button class="control-btn" id="projectSummaryBtn" title="Show Project Summary">📋</button>
                        <button class="control-btn" id="askProjectBtn" title="Ask About Project">❓</button>
                        <button class="control-btn" id="clearBtn" title="Clear Chat">🗑️</button>
                        <button class="control-btn" id="exportBtn" title="Export Chat">📤</button>
                        <button class="control-btn" id="importBtn" title="Import Chat">📥</button>
                    </div>
                </div>
                
                <div class="messages-container" id="messagesContainer">
                    <div class="welcome-message">
                        <h3>Welcome to Agent Zero!</h3>
                        <p>I'm your AI assistant. How can I help you today?</p>
                    </div>
                </div>
                
                <div class="typing-indicator" id="typingIndicator" style="display: none;">
                    <div class="typing-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                    <span>Agent is typing...</span>
                </div>
                
                <div class="input-container">
                    <textarea 
                        id="messageInput" 
                        placeholder="Type your message here..." 
                        rows="1"
                        maxlength="4000"
                    ></textarea>
                    <button id="sendBtn" class="send-btn" title="Send Message">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                            <path d="M15.854 7.146a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708-.708L14.293 8 8.146 1.854a.5.5 0 1 1 .708-.708l7 7z"/>
                            <path d="M8.5 8a.5.5 0 0 1-.5-.5V1a.5.5 0 0 1 1 0v6.5a.5.5 0 0 1-.5.5z"/>
                        </svg>
                    </button>
                </div>
                
                <div class="status-bar">
                    <span class="char-count" id="charCount">0/4000</span>
                </div>
            </div>

            <script nonce="${nonce}" src="${scriptUri}"></script>
            <script nonce="${nonce}">
                // Project context buttons
                document.getElementById('projectSummaryBtn').addEventListener('click', () => {
                    vscode.postMessage({
                        command: 'showProjectSummary'
                    });
                });

                document.getElementById('askProjectBtn').addEventListener('click', () => {
                    const question = prompt('What would you like to know about this project?');
                    if (question) {
                        vscode.postMessage({
                            command: 'askAboutProject',
                            question: question
                        });
                    }
                });
            </script>
        </body>
        </html>`;
    }
}
exports.ChatWebviewProvider = ChatWebviewProvider;
ChatWebviewProvider.viewType = 'agent-zero-chat';
function getNonce() {
    let text = '';
    const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    for (let i = 0; i < 32; i++) {
        text += possible.charAt(Math.floor(Math.random() * possible.length));
    }
    return text;
}
//# sourceMappingURL=ChatWebviewProvider.js.map