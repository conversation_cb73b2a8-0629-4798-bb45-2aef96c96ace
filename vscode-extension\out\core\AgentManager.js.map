{"version": 3, "file": "AgentManager.js", "sourceRoot": "", "sources": ["../../src/core/AgentManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAmBjC,MAAa,YAAY;IAMrB,YACY,YAA0B,EAC1B,aAA4B,EAC5B,WAAwB,EACxB,qBAA6C;QAH7C,iBAAY,GAAZ,YAAY,CAAc;QAC1B,kBAAa,GAAb,aAAa,CAAe;QAC5B,gBAAW,GAAX,WAAW,CAAa;QACxB,0BAAqB,GAArB,qBAAqB,CAAwB;QATjD,WAAM,GAAuB,IAAI,GAAG,EAAE,CAAC;QACvC,gBAAW,GAAkB,IAAI,CAAC;QAClC,iBAAY,GAAG,CAAC,CAAC;QACjB,eAAU,GAAsB,IAAI,CAAC;IAO1C,CAAC;IAEG,KAAK,CAAC,UAAU,CAAC,UAAsB;QAC1C,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAE7B,yBAAyB;QACzB,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;QAEnC,sCAAsC;QACtC,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAEnC,iDAAiD;QACjD,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,EAAE;YACxB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,YAAY,EAAE,mBAAmB,CAAC,CAAC;YACtF,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC,EAAE,CAAC;SACnC;aAAM;YACH,gCAAgC;YAChC,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACvD,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC,EAAE,CAAC;SACpC;QAED,6CAA6C;QAC7C,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;IACrC,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,IAAY,EAAE,IAAY,EAAE,QAAiB;QAClE,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,kCAAkC;YAClC,OAAO,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;SACjE;aAAM;YACH,6BAA6B;YAC7B,MAAM,KAAK,GAAU;gBACjB,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,YAAY,EAAE;gBAClC,IAAI;gBACJ,IAAI;gBACJ,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE,IAAI,IAAI,EAAE;gBACnB,YAAY,EAAE,IAAI,IAAI,EAAE;gBACxB,QAAQ;gBACR,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,EAAE;aACd,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;YAEjC,4CAA4C;YAC5C,IAAI,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;gBACvC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC;gBAC1C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;aAClC;YAED,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,OAAO,KAAK,CAAC;SAChB;IACL,CAAC;IAEM,QAAQ,CAAC,EAAU;QACtB,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAC/B,CAAC;IAEM,YAAY;QACf,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;IAC5C,CAAC;IAEM,cAAc;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;IAC/E,CAAC;IAEM,cAAc,CAAC,EAAU;QAC5B,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;YACrB,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC;SACf;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,EAAU;QAC/B,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAClC,IAAI,CAAC,KAAK,EAAE;YACR,OAAO,KAAK,CAAC;SAChB;QAED,gCAAgC;QAChC,IAAI,KAAK,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;YACnD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAE,CAAC;YAChD,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,KAAK,EAAE,CAAC,CAAC;SACvE;QAED,kCAAkC;QAClC,KAAK,MAAM,OAAO,IAAI,KAAK,CAAC,QAAQ,EAAE;YAClC,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;SACnC;QAED,mBAAmB;QACnB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QAEvB,8CAA8C;QAC9C,IAAI,IAAI,CAAC,WAAW,KAAK,EAAE,EAAE;YACzB,MAAM,eAAe,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;YAC5C,IAAI,CAAC,WAAW,GAAG,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;SAChF;QAED,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,IAAY,EAAE,QAAgB;QACnD,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAC1C,IAAI,CAAC,WAAW,EAAE;YACd,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,CAAC;YACxD,OAAO;SACV;QAED,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,6BAA6B,CAAC,CAAC;YAC9D,OAAO;SACV;QAED,IAAI;YACA,WAAW,CAAC,MAAM,GAAG,SAAS,CAAC;YAC/B,WAAW,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;YAEtC,+BAA+B;YAC/B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC;gBAC7C,IAAI;gBACJ,QAAQ;gBACR,OAAO,EAAE,WAAW,CAAC,EAAE;aAC1B,CAAC,CAAC;YAEH,IAAI,MAAM,CAAC,OAAO,EAAE;gBAChB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,4BAA4B,CAAC,CAAC;gBACnE,IAAI,MAAM,CAAC,MAAM,EAAE;oBACf,kDAAkD;oBAClD,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,wBAAwB,CAAC,CAAC;oBAClF,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;oBACxC,aAAa,CAAC,IAAI,EAAE,CAAC;iBACxB;aACJ;iBAAM;gBACH,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,IAAI,uBAAuB,CAAC,CAAC;aAC5D;YAED,WAAW,CAAC,MAAM,GAAG,MAAM,CAAC;SAC/B;QAAC,OAAO,KAAK,EAAE;YACZ,WAAW,CAAC,MAAM,GAAG,OAAO,CAAC;YAC7B,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,0BAA0B,KAAK,EAAE,CAAC,CAAC;SACrE;IACL,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,GAAe;QACpC,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAC1C,IAAI,CAAC,WAAW,EAAE;YACd,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,CAAC;YACxD,OAAO;SACV;QAED,IAAI;YACA,WAAW,CAAC,MAAM,GAAG,SAAS,CAAC;YAC/B,WAAW,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;YAEtC,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;YAC9D,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;YAEnC,4CAA4C;YAC5C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,OAAO,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC;YAEnF,uBAAuB;YACvB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,yBAAyB,CAAC,CAAC;YAEhE,WAAW,CAAC,MAAM,GAAG,MAAM,CAAC;SAC/B;QAAC,OAAO,KAAK,EAAE;YACZ,WAAW,CAAC,MAAM,GAAG,OAAO,CAAC;YAC7B,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,yBAAyB,KAAK,EAAE,CAAC,CAAC;SACpE;IACL,CAAC;IAEM,KAAK,CAAC,YAAY,CAAC,MAAc,EAAE,QAAgB;QACtD,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAC1C,IAAI,CAAC,WAAW,EAAE;YACd,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,CAAC;YACxD,OAAO;SACV;QAED,IAAI;YACA,WAAW,CAAC,MAAM,GAAG,SAAS,CAAC;YAC/B,WAAW,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;YAEtC,yCAAyC;YACzC,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YAE7E,2CAA2C;YAC3C,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;YAC9C,IAAI,MAAM,EAAE;gBACR,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;oBAC5B,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;gBAC/D,CAAC,CAAC,CAAC;aACN;YAED,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,6BAA6B,CAAC,CAAC;YAEpE,WAAW,CAAC,MAAM,GAAG,MAAM,CAAC;SAC/B;QAAC,OAAO,KAAK,EAAE;YACZ,WAAW,CAAC,MAAM,GAAG,OAAO,CAAC;YAC7B,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,2BAA2B,KAAK,EAAE,CAAC,CAAC;SACtE;IACL,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,OAAe,EAAE,OAAgB;QACtD,MAAM,WAAW,GAAG,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;QAC7E,IAAI,CAAC,WAAW,EAAE;YACd,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;SAC5C;QAED,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;SAClD;QAED,IAAI;YACA,WAAW,CAAC,MAAM,GAAG,SAAS,CAAC;YAC/B,WAAW,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;YAEtC,+CAA+C;YAC/C,IAAI,eAAe,GAAG,WAAW,CAAC,OAAO,CAAC;YAC1C,IAAI,IAAI,CAAC,qBAAqB,EAAE;gBAC5B,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,iBAAiB,EAAE,CAAC;gBAC5E,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;gBAErF,eAAe,GAAG;oBACd,GAAG,WAAW,CAAC,OAAO;oBACtB,cAAc;oBACd,eAAe;oBACf,aAAa,EAAE,IAAI,CAAC,4BAA4B,CAAC,cAAc,EAAE,WAAW,CAAC,OAAO,CAAC,aAAa,CAAC;iBACtG,CAAC;aACL;YAED,qDAAqD;YACrD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,OAAO,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC;YAEhF,uBAAuB;YACvB,WAAW,CAAC,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC;YAC1C,WAAW,CAAC,OAAO,CAAC,YAAY,GAAG,QAAQ,CAAC;YAE5C,8BAA8B;YAC9B,IAAI,IAAI,CAAC,qBAAqB,EAAE;gBAC5B,MAAM,IAAI,CAAC,qBAAqB,CAAC,UAAU,CACvC,SAAS,OAAO,YAAY,QAAQ,EAAE,EACtC,cAAc,EACd,qBAAqB,WAAW,CAAC,IAAI,EAAE,EACvC,CAAC,cAAc,EAAE,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC,EAC1C,CAAC,EACD;oBACI,OAAO,EAAE,WAAW,CAAC,EAAE;oBACvB,SAAS,EAAE,WAAW,CAAC,IAAI;oBAC3B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACtC,CACJ,CAAC;aACL;YAED,WAAW,CAAC,MAAM,GAAG,MAAM,CAAC;YAC5B,OAAO,QAAQ,CAAC;SACnB;QAAC,OAAO,KAAK,EAAE;YACZ,WAAW,CAAC,MAAM,GAAG,OAAO,CAAC;YAC7B,MAAM,KAAK,CAAC;SACf;IACL,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,IAAY,EAAE,IAAY,EAAE,QAAiB;QAC7E,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;SAClD;QAED,IAAI;YACA,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;YAE1E,MAAM,KAAK,GAAU;gBACjB,EAAE,EAAE,SAAS,CAAC,EAAE;gBAChB,IAAI,EAAE,SAAS,CAAC,IAAI;gBACpB,IAAI,EAAE,SAAS,CAAC,IAAI;gBACpB,MAAM,EAAE,SAAS,CAAC,MAAM;gBACxB,OAAO,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;gBACpC,YAAY,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC;gBAC9C,QAAQ,EAAE,SAAS,CAAC,QAAQ;gBAC5B,QAAQ,EAAE,SAAS,CAAC,QAAQ;gBAC5B,OAAO,EAAE,EAAE;aACd,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;YACjC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,OAAO,KAAK,CAAC;SAChB;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,IAAI,KAAK,CAAC,uCAAuC,KAAK,EAAE,CAAC,CAAC;SACnE;IACL,CAAC;IAEO,KAAK,CAAC,qBAAqB;QAC/B,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,OAAO;SACV;QAED,IAAI;YACA,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;YAErD,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE;gBAChC,MAAM,KAAK,GAAU;oBACjB,EAAE,EAAE,SAAS,CAAC,EAAE;oBAChB,IAAI,EAAE,SAAS,CAAC,IAAI;oBACpB,IAAI,EAAE,SAAS,CAAC,IAAI;oBACpB,MAAM,EAAE,SAAS,CAAC,MAAM;oBACxB,OAAO,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;oBACpC,YAAY,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC;oBAC9C,QAAQ,EAAE,SAAS,CAAC,QAAQ;oBAC5B,QAAQ,EAAE,SAAS,CAAC,QAAQ;oBAC5B,OAAO,EAAE,EAAE;iBACd,CAAC;gBAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;aACpC;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;SAC/D;IACL,CAAC;IAEO,KAAK,CAAC,mBAAmB;QAC7B,6DAA6D;QAC7D,6CAA6C;IACjD,CAAC;IAEO,iBAAiB;QACrB,2DAA2D;QAC3D,6CAA6C;IACjD,CAAC;IAEO,4BAA4B,CAAC,cAAsB,EAAE,qBAA8B;QACvF,IAAI,CAAC,cAAc,EAAE;YACjB,OAAO,qBAAqB,IAAI,iCAAiC,CAAC;SACrE;QAED,MAAM,iBAAiB,GAAG;EAChC,qBAAqB,IAAI,iCAAiC;;;EAG1D,cAAc;;;;;;;;;;;SAWP,CAAC,IAAI,EAAE,CAAC;QAET,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IAEM,KAAK,CAAC,iBAAiB;QAC1B,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;YAC7B,OAAO,uCAAuC,CAAC;SAClD;QAED,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,iBAAiB,EAAE,CAAC;QAC5E,IAAI,CAAC,cAAc,EAAE;YACjB,OAAO,kEAAkE,CAAC;SAC7E;QAED,OAAO,cAAc,CAAC;IAC1B,CAAC;IAEM,KAAK,CAAC,eAAe,CAAC,QAAgB;QACzC,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;YAC7B,OAAO,uCAAuC,CAAC;SAClD;QAED,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,iBAAiB,EAAE,CAAC;QAC5E,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QAEtF,MAAM,kBAAkB,GAAG;qEACkC,QAAQ;;;EAG3E,cAAc;;;EAGd,eAAe;SACR,CAAC,IAAI,EAAE,CAAC;QAET,iDAAiD;QACjD,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAC1C,IAAI,WAAW,EAAE;YACb,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC;SACrE;aAAM;YACH,6BAA6B;YAC7B,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,kBAAkB,EAAE;gBAC9D,aAAa,EAAE,8JAA8J;aAChL,CAAC,CAAC;SACN;IACL,CAAC;IAEM,OAAO;QACV,qBAAqB;QACrB,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;QACpB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAC5B,CAAC;CACJ;AA/ZD,oCA+ZC"}