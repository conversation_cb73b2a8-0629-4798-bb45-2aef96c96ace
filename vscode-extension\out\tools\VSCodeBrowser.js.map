{"version": 3, "file": "VSCodeBrowser.js", "sourceRoot": "", "sources": ["../../src/tools/VSCodeBrowser.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAyBjC,MAAa,aAAa;IAItB;QAFQ,iBAAY,GAA+B,IAAI,CAAC;QAGpD,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,CAAC;IACjF,CAAC;IAEM,KAAK,CAAC,cAAc,CAAC,OAAuB;QAC/C,IAAI;YACA,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,6BAA6B,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;YAE7E,QAAQ,OAAO,CAAC,MAAM,EAAE;gBACpB,KAAK,MAAM;oBACP,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC;gBACjD,KAAK,UAAU;oBACX,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC;gBACvD,KAAK,QAAQ;oBACT,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;gBACrD,KAAK,SAAS;oBACV,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBACvD,KAAK,YAAY;oBACb,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBACtD,KAAK,OAAO;oBACR,OAAO,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;gBACrC;oBACI,MAAM,IAAI,KAAK,CAAC,+BAA+B,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;aACxE;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,0BAA0B,KAAK,EAAE,CAAC,CAAC;YACjE,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE;aAC1B,CAAC;SACL;IACL,CAAC;IAEO,KAAK,CAAC,OAAO,CAAC,GAAW;QAC7B,IAAI;YACA,eAAe;YACf,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;gBAC/B,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;aAC3C;YAED,oCAAoC;YACpC,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAChD,kBAAkB,EAClB,oBAAoB,EACpB,MAAM,CAAC,UAAU,CAAC,GAAG,EACrB;gBACI,aAAa,EAAE,IAAI;gBACnB,uBAAuB,EAAE,IAAI;gBAC7B,gBAAgB,EAAE,IAAI;aACzB,CACJ,CAAC;YAEF,yBAAyB;YACzB,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;YAE1D,0BAA0B;YAC1B,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,mBAAmB,CACzC,CAAC,OAAO,EAAE,EAAE;gBACR,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YACvC,CAAC,CACJ,CAAC;YAEF,qDAAqD;YACrD,IAAI,GAAG,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;gBACzD,MAAM,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;aACxD;YAED,OAAO;gBACH,OAAO,EAAE,IAAI;gBACb,GAAG,EAAE,GAAG;gBACR,IAAI,EAAE,EAAE,OAAO,EAAE,6BAA6B,EAAE;aACnD,CAAC;SAEL;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,uBAAuB,KAAK,EAAE;aACxC,CAAC;SACL;IACL,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,GAAW;QACnC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;SAClC;QAED,IAAI;YACA,yBAAyB;YACzB,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;YAE1D,mCAAmC;YACnC,IAAI,GAAG,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;gBACzD,MAAM,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;aACxD;YAED,OAAO;gBACH,OAAO,EAAE,IAAI;gBACb,GAAG,EAAE,GAAG;gBACR,IAAI,EAAE,EAAE,OAAO,EAAE,uBAAuB,EAAE;aAC7C,CAAC;SAEL;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,uBAAuB,KAAK,EAAE;aACxC,CAAC;SACL;IACL,CAAC;IAEO,KAAK,CAAC,SAAS,CAAC,KAAa;QACjC,IAAI;YACA,0CAA0C;YAC1C,MAAM,SAAS,GAAG,6BAA6B,kBAAkB,CAAC,KAAK,CAAC,EAAE,CAAC;YAE3E,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAE7C,IAAI,MAAM,CAAC,OAAO,EAAE;gBAChB,MAAM,CAAC,IAAI,GAAG;oBACV,GAAG,MAAM,CAAC,IAAI;oBACd,KAAK,EAAE,KAAK;oBACZ,YAAY,EAAE,YAAY;iBAC7B,CAAC;aACL;YAED,OAAO,MAAM,CAAC;SAEjB;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,qBAAqB,KAAK,EAAE;aACtC,CAAC;SACL;IACL,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,QAAiB;QAC1C,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,2BAA2B;aACrC,CAAC;SACL;QAED,IAAI;YACA,6CAA6C;YAC7C,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,WAAW,CAAC;gBAClC,OAAO,EAAE,gBAAgB;gBACzB,QAAQ,EAAE,QAAQ;aACrB,CAAC,CAAC;YAEH,2DAA2D;YAC3D,gEAAgE;YAChE,8DAA8D;YAE9D,OAAO;gBACH,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,0DAA0D;gBACnE,IAAI,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE;aAC/B,CAAC;SAEL;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,8BAA8B,KAAK,EAAE;aAC/C,CAAC;SACL;IACL,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,OAAa;QACtC,oEAAoE;QACpE,yEAAyE;QAEzE,OAAO;YACH,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,gEAAgE;SAC1E,CAAC;IACN,CAAC;IAEO,KAAK,CAAC,YAAY;QACtB,IAAI;YACA,IAAI,IAAI,CAAC,YAAY,EAAE;gBACnB,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;gBAC5B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;aAC5B;YAED,OAAO;gBACH,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,EAAE,OAAO,EAAE,6BAA6B,EAAE;aACnD,CAAC;SAEL;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,4BAA4B,KAAK,EAAE;aAC7C,CAAC;SACL;IACL,CAAC;IAEO,cAAc,CAAC,GAAW;QAC9B,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wEA8DyD,GAAG;;;;;;;0CAOjC,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBA+D7B,CAAC;IACb,CAAC;IAEO,oBAAoB,CAAC,OAAY;QACrC,QAAQ,OAAO,CAAC,OAAO,EAAE;YACrB,KAAK,UAAU;gBACX,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;gBAChC,MAAM;YACV,KAAK,cAAc;gBACf,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;gBACvD,MAAM;YACV,KAAK,kBAAkB;gBACnB,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,sBAAsB,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;gBACvE,MAAM;YACV;gBACI,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,4BAA4B,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;SACpF;IACL,CAAC;IAEO,UAAU,CAAC,GAAW;QAC1B,IAAI;YACA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;YACb,OAAO,IAAI,CAAC;SACf;QAAC,MAAM;YACJ,OAAO,KAAK,CAAC;SAChB;IACL,CAAC;IAEM,KAAK,CAAC,wBAAwB,CAAC,GAAW;QAC7C,IAAI;YACA,MAAM,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;YACrD,OAAO;gBACH,OAAO,EAAE,IAAI;gBACb,GAAG,EAAE,GAAG;gBACR,IAAI,EAAE,EAAE,OAAO,EAAE,gCAAgC,EAAE;aACtD,CAAC;SACL;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,oCAAoC,KAAK,EAAE;aACrD,CAAC;SACL;IACL,CAAC;IAEM,OAAO;QACV,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;YAC5B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;SAC5B;QACD,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;IACjC,CAAC;CACJ;AAjYD,sCAiYC"}