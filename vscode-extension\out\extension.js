"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = __importStar(require("vscode"));
const AgentZeroProvider_1 = require("./providers/AgentZeroProvider");
const ChatWebviewProvider_1 = require("./providers/ChatWebviewProvider");
const MultiAgentProvider_1 = require("./providers/MultiAgentProvider");
const AgentManager_1 = require("./core/AgentManager");
const ModelManager_1 = require("./core/ModelManager");
const ToolManager_1 = require("./core/ToolManager");
const MemoryManager_1 = require("./core/MemoryManager");
const EnhancedMemoryManager_1 = require("./core/EnhancedMemoryManager");
const MultiAgentSystem_1 = require("./core/MultiAgentSystem");
const KnowledgeSearchEngine_1 = require("./core/KnowledgeSearchEngine");
const ConfigurationManager_1 = require("./core/ConfigurationManager");
const AgentZeroService_1 = require("./services/AgentZeroService");
const ApiService_1 = require("./services/ApiService");
let agentZeroProvider;
let chatWebviewProvider;
let multiAgentProvider;
let agentManager;
let modelManager;
let toolManager;
let memoryManager;
let enhancedMemoryManager;
let multiAgentSystem;
let knowledgeSearchEngine;
let configurationManager;
let agentZeroService;
let apiService;
function activate(context) {
    console.log('Agent Zero extension is now active!');
    // Initialize core managers
    configurationManager = new ConfigurationManager_1.ConfigurationManager();
    modelManager = new ModelManager_1.ModelManager(configurationManager);
    memoryManager = new MemoryManager_1.MemoryManager(context);
    toolManager = new ToolManager_1.ToolManager(context);
    // Initialize enhanced systems
    enhancedMemoryManager = new EnhancedMemoryManager_1.EnhancedMemoryManager(context, modelManager);
    knowledgeSearchEngine = new KnowledgeSearchEngine_1.KnowledgeSearchEngine(enhancedMemoryManager, modelManager);
    multiAgentSystem = new MultiAgentSystem_1.MultiAgentSystem(context, modelManager, toolManager, enhancedMemoryManager);
    // Initialize services
    agentZeroService = new AgentZeroService_1.AgentZeroService(configurationManager, context);
    apiService = new ApiService_1.ApiService(agentZeroService);
    // Initialize agent manager with enhanced memory
    agentManager = new AgentManager_1.AgentManager(modelManager, memoryManager, toolManager, enhancedMemoryManager);
    // Initialize providers
    agentZeroProvider = new AgentZeroProvider_1.AgentZeroProvider(context, agentManager);
    chatWebviewProvider = new ChatWebviewProvider_1.ChatWebviewProvider(context, agentManager);
    multiAgentProvider = new MultiAgentProvider_1.MultiAgentProvider(context, multiAgentSystem);
    // Register tree data providers
    vscode.window.registerTreeDataProvider('agent-zero-agents', agentZeroProvider);
    vscode.window.registerTreeDataProvider('agent-zero-memory', memoryManager);
    vscode.window.registerTreeDataProvider('agent-zero-enhanced-memory', enhancedMemoryManager);
    vscode.window.registerTreeDataProvider('agent-zero-multi-agents', multiAgentProvider);
    vscode.window.registerTreeDataProvider('agent-zero-tools', toolManager);
    // Register webview provider
    context.subscriptions.push(vscode.window.registerWebviewViewProvider('agent-zero-chat', chatWebviewProvider));
    // Register commands
    registerCommands(context);
    // Set context for when extension is enabled
    vscode.commands.executeCommand('setContext', 'agent-zero:enabled', true);
    // Initialize Agent Zero backend
    initializeAgentZeroBackend();
}
exports.activate = activate;
function registerCommands(context) {
    // Open Chat command
    const openChatCommand = vscode.commands.registerCommand('agent-zero.openChat', () => {
        chatWebviewProvider.show();
    });
    // New Agent command
    const newAgentCommand = vscode.commands.registerCommand('agent-zero.newAgent', async () => {
        const agentName = await vscode.window.showInputBox({
            prompt: 'Enter agent name',
            placeHolder: 'My Agent'
        });
        if (agentName) {
            const agentType = await vscode.window.showQuickPick([
                'General Assistant',
                'Code Developer',
                'Researcher',
                'Data Analyst',
                'Custom'
            ], {
                placeHolder: 'Select agent type'
            });
            if (agentType) {
                agentManager.createAgent(agentName, agentType);
                agentZeroProvider.refresh();
                vscode.window.showInformationMessage(`Agent "${agentName}" created successfully!`);
            }
        }
    });
    // Show Settings command
    const showSettingsCommand = vscode.commands.registerCommand('agent-zero.showSettings', () => {
        vscode.commands.executeCommand('workbench.action.openSettings', 'agent-zero');
    });
    // Execute Code command
    const executeCodeCommand = vscode.commands.registerCommand('agent-zero.executeCode', async () => {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showErrorMessage('No active editor found');
            return;
        }
        const selection = editor.selection;
        const code = editor.document.getText(selection.isEmpty ? undefined : selection);
        if (!code.trim()) {
            vscode.window.showErrorMessage('No code selected or found');
            return;
        }
        const language = editor.document.languageId;
        await agentManager.executeCode(code, language);
    });
    // Analyze File command
    const analyzeFileCommand = vscode.commands.registerCommand('agent-zero.analyzeFile', async (uri) => {
        let fileUri = uri;
        if (!fileUri) {
            const editor = vscode.window.activeTextEditor;
            if (editor) {
                fileUri = editor.document.uri;
            }
        }
        if (!fileUri) {
            vscode.window.showErrorMessage('No file selected');
            return;
        }
        await agentManager.analyzeFile(fileUri);
    });
    // Generate Code command
    const generateCodeCommand = vscode.commands.registerCommand('agent-zero.generateCode', async () => {
        const prompt = await vscode.window.showInputBox({
            prompt: 'Describe what code you want to generate',
            placeHolder: 'e.g., Create a function that sorts an array'
        });
        if (prompt) {
            const editor = vscode.window.activeTextEditor;
            const language = editor?.document.languageId || 'javascript';
            await agentManager.generateCode(prompt, language);
        }
    });
    // Enhanced memory commands
    const searchKnowledgeCommand = vscode.commands.registerCommand('agent-zero.searchKnowledge', async () => {
        await knowledgeSearchEngine.showSearchInterface();
    });
    const saveMemoryCommand = vscode.commands.registerCommand('agent-zero.saveMemory', async () => {
        const content = await vscode.window.showInputBox({
            prompt: 'Enter content to save to memory',
            placeHolder: 'Memory content...'
        });
        if (content) {
            const title = await vscode.window.showInputBox({
                prompt: 'Enter memory title (optional)',
                placeHolder: 'Memory title...'
            });
            await enhancedMemoryManager.saveMemory(content, 'fact', title);
            vscode.window.showInformationMessage('Memory saved successfully');
        }
    });
    // Multi-agent system commands
    const showAgentDashboardCommand = vscode.commands.registerCommand('agent-zero.showAgentDashboard', () => {
        vscode.commands.executeCommand('agent-zero.showAgentDashboard');
    });
    // Project context commands
    const showProjectSummaryCommand = vscode.commands.registerCommand('agent-zero.showProjectSummary', async () => {
        try {
            const summary = await agentManager.getProjectSummary();
            // Show in a new document
            const doc = await vscode.workspace.openTextDocument({
                content: summary,
                language: 'markdown'
            });
            await vscode.window.showTextDocument(doc);
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to get project summary: ${error}`);
        }
    });
    const askAboutProjectCommand = vscode.commands.registerCommand('agent-zero.askAboutProject', async () => {
        const question = await vscode.window.showInputBox({
            prompt: 'What would you like to know about this project?',
            placeHolder: 'e.g., What is the main purpose of this project? How is authentication implemented?'
        });
        if (!question)
            return;
        try {
            const answer = await agentManager.askAboutProject(question);
            // Show in a new document
            const doc = await vscode.workspace.openTextDocument({
                content: `Question: ${question}\n\nAnswer:\n${answer}`,
                language: 'markdown'
            });
            await vscode.window.showTextDocument(doc);
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to get project information: ${error}`);
        }
    });
    const refreshProjectContextCommand = vscode.commands.registerCommand('agent-zero.refreshProjectContext', async () => {
        try {
            await enhancedMemoryManager.indexCurrentWorkspace();
            vscode.window.showInformationMessage('Project context refreshed successfully!');
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to refresh project context: ${error}`);
        }
    });
    // Add all commands to subscriptions
    context.subscriptions.push(openChatCommand, newAgentCommand, showSettingsCommand, executeCodeCommand, analyzeFileCommand, generateCodeCommand, searchKnowledgeCommand, saveMemoryCommand, showAgentDashboardCommand, showProjectSummaryCommand, askAboutProjectCommand, refreshProjectContextCommand);
}
async function initializeAgentZeroBackend() {
    try {
        // Initialize the Agent Zero backend service
        await agentManager.initialize(apiService);
        vscode.window.showInformationMessage('Agent Zero backend initialized successfully!');
    }
    catch (error) {
        vscode.window.showErrorMessage(`Failed to initialize Agent Zero backend: ${error}`);
    }
}
function deactivate() {
    // Clean up resources
    if (agentManager) {
        agentManager.dispose();
    }
    if (modelManager) {
        modelManager.dispose();
    }
    if (toolManager) {
        toolManager.dispose();
    }
    if (memoryManager) {
        memoryManager.dispose();
    }
    if (enhancedMemoryManager) {
        enhancedMemoryManager.dispose();
    }
    if (multiAgentSystem) {
        multiAgentSystem.dispose();
    }
    if (knowledgeSearchEngine) {
        knowledgeSearchEngine.dispose();
    }
    if (apiService) {
        apiService.dispose();
    }
    if (agentZeroService) {
        agentZeroService.dispose();
    }
}
exports.deactivate = deactivate;
//# sourceMappingURL=extension.js.map