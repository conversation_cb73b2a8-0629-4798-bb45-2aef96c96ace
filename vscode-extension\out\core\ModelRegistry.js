"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ModelRegistry = void 0;
class ModelRegistry {
    constructor() {
        this.providers = new Map();
        this.models = new Map();
        this.initializeRegistry();
    }
    initializeRegistry() {
        // OpenAI
        this.registerProvider({
            id: 'openai',
            name: 'OpenAI',
            description: 'Leading AI research company with GPT models',
            website: 'https://openai.com',
            apiKeyRequired: true,
            localModel: false,
            supportedTypes: ['chat', 'embedding', 'vision'],
            models: [
                {
                    id: 'gpt-4',
                    name: 'GPT-4',
                    provider: 'openai',
                    type: 'chat',
                    description: 'Most capable GPT model for complex tasks',
                    contextLength: 8192,
                    maxTokens: 4096,
                    supportsVision: false,
                    supportsStreaming: true,
                    costPer1kTokens: { input: 0.03, output: 0.06 },
                    capabilities: ['reasoning', 'coding', 'analysis', 'writing'],
                    limitations: ['knowledge cutoff', 'no real-time data']
                },
                {
                    id: 'gpt-4-turbo',
                    name: 'GPT-4 Turbo',
                    provider: 'openai',
                    type: 'chat',
                    description: 'Faster and more efficient GPT-4',
                    contextLength: 128000,
                    maxTokens: 4096,
                    supportsVision: true,
                    supportsStreaming: true,
                    costPer1kTokens: { input: 0.01, output: 0.03 },
                    capabilities: ['reasoning', 'coding', 'analysis', 'writing', 'vision'],
                    limitations: ['knowledge cutoff']
                },
                {
                    id: 'gpt-3.5-turbo',
                    name: 'GPT-3.5 Turbo',
                    provider: 'openai',
                    type: 'chat',
                    description: 'Fast and efficient for most tasks',
                    contextLength: 16385,
                    maxTokens: 4096,
                    supportsVision: false,
                    supportsStreaming: true,
                    costPer1kTokens: { input: 0.0015, output: 0.002 },
                    capabilities: ['conversation', 'coding', 'writing'],
                    limitations: ['less capable than GPT-4']
                },
                {
                    id: 'text-embedding-ada-002',
                    name: 'Text Embedding Ada 002',
                    provider: 'openai',
                    type: 'embedding',
                    description: 'High-quality text embeddings',
                    contextLength: 8191,
                    maxTokens: 0,
                    supportsVision: false,
                    supportsStreaming: false,
                    costPer1kTokens: { input: 0.0001, output: 0 },
                    capabilities: ['text similarity', 'search', 'clustering'],
                    limitations: ['text only']
                }
            ]
        });
        // Anthropic
        this.registerProvider({
            id: 'anthropic',
            name: 'Anthropic',
            description: 'AI safety company with Claude models',
            website: 'https://anthropic.com',
            apiKeyRequired: true,
            localModel: false,
            supportedTypes: ['chat', 'vision'],
            models: [
                {
                    id: 'claude-3-opus-20240229',
                    name: 'Claude 3 Opus',
                    provider: 'anthropic',
                    type: 'chat',
                    description: 'Most powerful Claude model',
                    contextLength: 200000,
                    maxTokens: 4096,
                    supportsVision: true,
                    supportsStreaming: true,
                    costPer1kTokens: { input: 0.015, output: 0.075 },
                    capabilities: ['reasoning', 'coding', 'analysis', 'writing', 'vision'],
                    limitations: ['high cost']
                },
                {
                    id: 'claude-3-sonnet-20240229',
                    name: 'Claude 3 Sonnet',
                    provider: 'anthropic',
                    type: 'chat',
                    description: 'Balanced performance and speed',
                    contextLength: 200000,
                    maxTokens: 4096,
                    supportsVision: true,
                    supportsStreaming: true,
                    costPer1kTokens: { input: 0.003, output: 0.015 },
                    capabilities: ['reasoning', 'coding', 'analysis', 'writing', 'vision'],
                    limitations: []
                },
                {
                    id: 'claude-3-haiku-20240307',
                    name: 'Claude 3 Haiku',
                    provider: 'anthropic',
                    type: 'chat',
                    description: 'Fastest Claude model',
                    contextLength: 200000,
                    maxTokens: 4096,
                    supportsVision: true,
                    supportsStreaming: true,
                    costPer1kTokens: { input: 0.00025, output: 0.00125 },
                    capabilities: ['conversation', 'coding', 'writing', 'vision'],
                    limitations: ['less capable than Opus/Sonnet']
                }
            ]
        });
        // Google Gemini
        this.registerProvider({
            id: 'gemini',
            name: 'Google Gemini',
            description: 'Google\'s multimodal AI models',
            website: 'https://ai.google.dev',
            apiKeyRequired: true,
            localModel: false,
            supportedTypes: ['chat', 'vision', 'embedding'],
            models: [
                {
                    id: 'gemini-pro',
                    name: 'Gemini Pro',
                    provider: 'gemini',
                    type: 'chat',
                    description: 'Google\'s most capable model',
                    contextLength: 32768,
                    maxTokens: 8192,
                    supportsVision: false,
                    supportsStreaming: true,
                    costPer1kTokens: { input: 0.0005, output: 0.0015 },
                    capabilities: ['reasoning', 'coding', 'analysis', 'writing'],
                    limitations: []
                },
                {
                    id: 'gemini-pro-vision',
                    name: 'Gemini Pro Vision',
                    provider: 'gemini',
                    type: 'vision',
                    description: 'Multimodal model with vision capabilities',
                    contextLength: 16384,
                    maxTokens: 2048,
                    supportsVision: true,
                    supportsStreaming: true,
                    costPer1kTokens: { input: 0.00025, output: 0.0005 },
                    capabilities: ['vision', 'reasoning', 'analysis'],
                    limitations: ['shorter context']
                }
            ]
        });
        // Groq
        this.registerProvider({
            id: 'groq',
            name: 'Groq',
            description: 'Ultra-fast inference with LPU technology',
            website: 'https://groq.com',
            apiKeyRequired: true,
            localModel: false,
            supportedTypes: ['chat'],
            models: [
                {
                    id: 'llama2-70b-4096',
                    name: 'Llama 2 70B',
                    provider: 'groq',
                    type: 'chat',
                    description: 'Meta\'s Llama 2 70B on Groq',
                    contextLength: 4096,
                    maxTokens: 4096,
                    supportsVision: false,
                    supportsStreaming: true,
                    costPer1kTokens: { input: 0.0007, output: 0.0008 },
                    capabilities: ['conversation', 'coding', 'reasoning'],
                    limitations: ['shorter context']
                },
                {
                    id: 'mixtral-8x7b-32768',
                    name: 'Mixtral 8x7B',
                    provider: 'groq',
                    type: 'chat',
                    description: 'Mistral\'s Mixtral model on Groq',
                    contextLength: 32768,
                    maxTokens: 32768,
                    supportsVision: false,
                    supportsStreaming: true,
                    costPer1kTokens: { input: 0.00027, output: 0.00027 },
                    capabilities: ['conversation', 'coding', 'reasoning'],
                    limitations: []
                }
            ]
        });
        // Ollama
        this.registerProvider({
            id: 'ollama',
            name: 'Ollama',
            description: 'Run large language models locally',
            website: 'https://ollama.ai',
            apiKeyRequired: false,
            localModel: true,
            supportedTypes: ['chat', 'embedding'],
            models: [
                {
                    id: 'llama2',
                    name: 'Llama 2',
                    provider: 'ollama',
                    type: 'chat',
                    description: 'Meta\'s Llama 2 model',
                    contextLength: 4096,
                    maxTokens: 4096,
                    supportsVision: false,
                    supportsStreaming: true,
                    costPer1kTokens: { input: 0, output: 0 },
                    capabilities: ['conversation', 'coding', 'reasoning'],
                    limitations: ['requires local installation']
                },
                {
                    id: 'codellama',
                    name: 'Code Llama',
                    provider: 'ollama',
                    type: 'code',
                    description: 'Specialized for code generation',
                    contextLength: 16384,
                    maxTokens: 4096,
                    supportsVision: false,
                    supportsStreaming: true,
                    costPer1kTokens: { input: 0, output: 0 },
                    capabilities: ['code generation', 'code completion', 'debugging'],
                    limitations: ['requires local installation']
                }
            ]
        });
        // Add more providers...
        this.addMistralProvider();
        this.addDeepSeekProvider();
        this.addLocalProviders();
    }
    addMistralProvider() {
        this.registerProvider({
            id: 'mistral',
            name: 'Mistral AI',
            description: 'European AI company with efficient models',
            website: 'https://mistral.ai',
            apiKeyRequired: true,
            localModel: false,
            supportedTypes: ['chat'],
            models: [
                {
                    id: 'mistral-large-latest',
                    name: 'Mistral Large',
                    provider: 'mistral',
                    type: 'chat',
                    description: 'Most capable Mistral model',
                    contextLength: 32768,
                    maxTokens: 8192,
                    supportsVision: false,
                    supportsStreaming: true,
                    costPer1kTokens: { input: 0.008, output: 0.024 },
                    capabilities: ['reasoning', 'coding', 'analysis', 'multilingual'],
                    limitations: []
                }
            ]
        });
    }
    addDeepSeekProvider() {
        this.registerProvider({
            id: 'deepseek',
            name: 'DeepSeek',
            description: 'Advanced AI models from DeepSeek',
            website: 'https://deepseek.com',
            apiKeyRequired: true,
            localModel: false,
            supportedTypes: ['chat', 'code'],
            models: [
                {
                    id: 'deepseek-chat',
                    name: 'DeepSeek Chat',
                    provider: 'deepseek',
                    type: 'chat',
                    description: 'General purpose chat model',
                    contextLength: 32768,
                    maxTokens: 4096,
                    supportsVision: false,
                    supportsStreaming: true,
                    costPer1kTokens: { input: 0.0014, output: 0.0028 },
                    capabilities: ['conversation', 'reasoning', 'analysis'],
                    limitations: []
                }
            ]
        });
    }
    addLocalProviders() {
        this.registerProvider({
            id: 'lm-studio',
            name: 'LM Studio',
            description: 'Local model hosting platform',
            website: 'https://lmstudio.ai',
            apiKeyRequired: false,
            localModel: true,
            supportedTypes: ['chat', 'embedding'],
            models: [
                {
                    id: 'local-model',
                    name: 'Local Model',
                    provider: 'lm-studio',
                    type: 'chat',
                    description: 'Any model running in LM Studio',
                    contextLength: 4096,
                    maxTokens: 4096,
                    supportsVision: false,
                    supportsStreaming: true,
                    costPer1kTokens: { input: 0, output: 0 },
                    capabilities: ['depends on model'],
                    limitations: ['requires LM Studio installation']
                }
            ]
        });
    }
    registerProvider(provider) {
        this.providers.set(provider.id, provider);
        // Register all models from this provider
        for (const model of provider.models) {
            this.models.set(model.id, model);
        }
    }
    getProvider(id) {
        return this.providers.get(id);
    }
    getAllProviders() {
        return Array.from(this.providers.values());
    }
    getModel(id) {
        return this.models.get(id);
    }
    getModelsByProvider(providerId) {
        return Array.from(this.models.values()).filter(model => model.provider === providerId);
    }
    getModelsByType(type) {
        return Array.from(this.models.values()).filter(model => model.type === type);
    }
    searchModels(query) {
        const lowerQuery = query.toLowerCase();
        return Array.from(this.models.values()).filter(model => model.name.toLowerCase().includes(lowerQuery) ||
            model.description.toLowerCase().includes(lowerQuery) ||
            model.capabilities.some(cap => cap.toLowerCase().includes(lowerQuery)));
    }
    validateModelConfig(config) {
        const errors = [];
        const provider = this.getProvider(config.provider);
        if (!provider) {
            errors.push(`Unknown provider: ${config.provider}`);
            return { valid: false, errors };
        }
        const model = this.getModel(config.name);
        if (!model) {
            errors.push(`Unknown model: ${config.name}`);
        }
        else if (model.provider !== config.provider) {
            errors.push(`Model ${config.name} is not available from provider ${config.provider}`);
        }
        if (provider.apiKeyRequired && !this.hasApiKey(config.provider)) {
            errors.push(`API key required for provider: ${config.provider}`);
        }
        return { valid: errors.length === 0, errors };
    }
    hasApiKey(provider) {
        const envVars = {
            'openai': 'OPENAI_API_KEY',
            'anthropic': 'ANTHROPIC_API_KEY',
            'gemini': 'GOOGLE_API_KEY',
            'groq': 'GROQ_API_KEY',
            'mistral': 'MISTRAL_API_KEY',
            'deepseek': 'DEEPSEEK_API_KEY',
            'azure': 'AZURE_OPENAI_API_KEY',
            'openrouter': 'OPENROUTER_API_KEY',
            'sambanova': 'SAMBANOVA_API_KEY',
            'huggingface': 'HUGGINGFACE_API_KEY'
        };
        const envVar = envVars[provider];
        return envVar ? !!process.env[envVar] : true; // Local models don't need API keys
    }
    getRecommendedModels(task) {
        const taskCapabilities = {
            'coding': ['coding', 'code generation', 'debugging'],
            'analysis': ['analysis', 'reasoning'],
            'writing': ['writing', 'conversation'],
            'vision': ['vision'],
            'embedding': ['text similarity', 'search', 'clustering']
        };
        const requiredCapabilities = taskCapabilities[task] || [];
        return Array.from(this.models.values())
            .filter(model => requiredCapabilities.some(cap => model.capabilities.some(modelCap => modelCap.toLowerCase().includes(cap.toLowerCase()))))
            .sort((a, b) => {
            // Sort by cost efficiency (lower cost per token is better)
            const aCost = a.costPer1kTokens.input + a.costPer1kTokens.output;
            const bCost = b.costPer1kTokens.input + b.costPer1kTokens.output;
            return aCost - bCost;
        });
    }
}
exports.ModelRegistry = ModelRegistry;
//# sourceMappingURL=ModelRegistry.js.map