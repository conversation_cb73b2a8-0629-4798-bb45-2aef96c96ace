"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiService = void 0;
const vscode = __importStar(require("vscode"));
const WebSocketService_1 = require("./WebSocketService");
class ApiService {
    constructor(agentZeroService) {
        this.webSocketService = null;
        this.messageCallbacks = new Map();
        this.agentZeroService = agentZeroService;
        this.outputChannel = vscode.window.createOutputChannel('Agent Zero API');
        this.initializeWebSocket();
    }
    initializeWebSocket() {
        const serviceUrl = this.agentZeroService.getServiceUrl();
        const wsUrl = serviceUrl.replace('http://', 'ws://') + '/ws';
        this.webSocketService = new WebSocketService_1.WebSocketService({
            url: wsUrl,
            reconnectInterval: 5000,
            maxReconnectAttempts: 10,
            heartbeatInterval: 30000
        });
        // Set up message handlers
        this.setupWebSocketHandlers();
    }
    setupWebSocketHandlers() {
        if (!this.webSocketService)
            return;
        // Chat responses
        this.webSocketService.onMessage(WebSocketService_1.MessageTypes.CHAT_RESPONSE, (data) => {
            this.handleChatResponse(data);
        });
        // Agent status updates
        this.webSocketService.onMessage(WebSocketService_1.MessageTypes.AGENT_STATUS, (data) => {
            this.handleAgentStatusUpdate(data);
        });
        // Code execution results
        this.webSocketService.onMessage(WebSocketService_1.MessageTypes.CODE_RESULT, (data) => {
            this.handleCodeExecutionResult(data);
        });
        // System errors
        this.webSocketService.onMessage(WebSocketService_1.MessageTypes.SYSTEM_ERROR, (data) => {
            this.handleSystemError(data);
        });
        // System logs
        this.webSocketService.onMessage(WebSocketService_1.MessageTypes.SYSTEM_LOG, (data) => {
            this.outputChannel.appendLine(`[SYSTEM] ${data.message}`);
        });
    }
    async initialize() {
        try {
            // Start Agent Zero service if not running
            const serviceStatus = this.agentZeroService.getStatus();
            if (!serviceStatus.running) {
                const started = await this.agentZeroService.start();
                if (!started) {
                    throw new Error('Failed to start Agent Zero service');
                }
            }
            // Connect WebSocket
            if (this.webSocketService) {
                await this.webSocketService.connect();
            }
            this.outputChannel.appendLine('API Service initialized successfully');
            return true;
        }
        catch (error) {
            this.outputChannel.appendLine(`Failed to initialize API Service: ${error}`);
            return false;
        }
    }
    async sendChatMessage(message, agentId) {
        try {
            // Try WebSocket first for real-time communication
            if (this.webSocketService && this.webSocketService.getConnectionStatus().connected) {
                return await this.sendChatMessageViaWebSocket(message, agentId);
            }
            else {
                // Fallback to HTTP API
                return await this.sendChatMessageViaHttp(message, agentId);
            }
        }
        catch (error) {
            this.outputChannel.appendLine(`Failed to send chat message: ${error}`);
            throw error;
        }
    }
    async sendChatMessageViaWebSocket(message, agentId) {
        return new Promise((resolve, reject) => {
            const messageId = this.generateMessageId();
            // Set up response handler
            this.messageCallbacks.set(messageId, (response) => {
                if (response.success) {
                    resolve(response.content);
                }
                else {
                    reject(new Error(response.error || 'Unknown error'));
                }
            });
            // Send message
            const wsMessage = {
                type: WebSocketService_1.MessageTypes.CHAT_MESSAGE,
                data: {
                    message,
                    agentId,
                    messageId
                },
                id: messageId
            };
            const sent = this.webSocketService.send(wsMessage);
            if (!sent) {
                this.messageCallbacks.delete(messageId);
                reject(new Error('Failed to send message via WebSocket'));
            }
            // Set timeout
            setTimeout(() => {
                if (this.messageCallbacks.has(messageId)) {
                    this.messageCallbacks.delete(messageId);
                    reject(new Error('Message timeout'));
                }
            }, 30000);
        });
    }
    async sendChatMessageViaHttp(message, agentId) {
        const response = await this.agentZeroService.makeApiRequest('/api/chat', 'POST', {
            message,
            agentId
        });
        if (response.success) {
            return response.content;
        }
        else {
            throw new Error(response.error || 'Unknown error');
        }
    }
    async executeCode(request) {
        try {
            const response = await this.agentZeroService.makeApiRequest('/api/execute', 'POST', request);
            return response;
        }
        catch (error) {
            this.outputChannel.appendLine(`Code execution failed: ${error}`);
            throw error;
        }
    }
    async performFileOperation(operation) {
        try {
            const response = await this.agentZeroService.makeApiRequest('/api/files', 'POST', operation);
            return response;
        }
        catch (error) {
            this.outputChannel.appendLine(`File operation failed: ${error}`);
            throw error;
        }
    }
    async performMemoryOperation(operation) {
        try {
            const response = await this.agentZeroService.makeApiRequest('/api/memory', 'POST', operation);
            return response;
        }
        catch (error) {
            this.outputChannel.appendLine(`Memory operation failed: ${error}`);
            throw error;
        }
    }
    async getAgents() {
        try {
            const response = await this.agentZeroService.makeApiRequest('/api/agents', 'GET');
            return response.agents || [];
        }
        catch (error) {
            this.outputChannel.appendLine(`Failed to get agents: ${error}`);
            throw error;
        }
    }
    async createAgent(name, type, parentId) {
        try {
            const response = await this.agentZeroService.makeApiRequest('/api/agents', 'POST', {
                name,
                type,
                parentId
            });
            return response.agent;
        }
        catch (error) {
            this.outputChannel.appendLine(`Failed to create agent: ${error}`);
            throw error;
        }
    }
    async deleteAgent(agentId) {
        try {
            const response = await this.agentZeroService.makeApiRequest(`/api/agents/${agentId}`, 'DELETE');
            return response.success;
        }
        catch (error) {
            this.outputChannel.appendLine(`Failed to delete agent: ${error}`);
            throw error;
        }
    }
    async getAgentStatus(agentId) {
        try {
            const response = await this.agentZeroService.makeApiRequest(`/api/agents/${agentId}`, 'GET');
            return response.agent;
        }
        catch (error) {
            this.outputChannel.appendLine(`Failed to get agent status: ${error}`);
            throw error;
        }
    }
    async getTools() {
        try {
            const response = await this.agentZeroService.makeApiRequest('/api/tools', 'GET');
            return response.tools || [];
        }
        catch (error) {
            this.outputChannel.appendLine(`Failed to get tools: ${error}`);
            throw error;
        }
    }
    async executeTool(toolId, parameters) {
        try {
            const response = await this.agentZeroService.makeApiRequest('/api/tools/execute', 'POST', {
                toolId,
                parameters
            });
            return response;
        }
        catch (error) {
            this.outputChannel.appendLine(`Tool execution failed: ${error}`);
            throw error;
        }
    }
    async getSystemStatus() {
        try {
            const response = await this.agentZeroService.makeApiRequest('/api/status', 'GET');
            return response;
        }
        catch (error) {
            this.outputChannel.appendLine(`Failed to get system status: ${error}`);
            throw error;
        }
    }
    handleChatResponse(data) {
        const messageId = data.messageId;
        if (messageId && this.messageCallbacks.has(messageId)) {
            const callback = this.messageCallbacks.get(messageId);
            this.messageCallbacks.delete(messageId);
            callback(data);
        }
    }
    handleAgentStatusUpdate(data) {
        // Emit event for agent status updates
        vscode.commands.executeCommand('agent-zero.agentStatusUpdated', data);
    }
    handleCodeExecutionResult(data) {
        // Emit event for code execution results
        vscode.commands.executeCommand('agent-zero.codeExecutionResult', data);
    }
    handleSystemError(data) {
        this.outputChannel.appendLine(`[ERROR] ${data.message}`);
        vscode.window.showErrorMessage(`Agent Zero Error: ${data.message}`);
    }
    generateMessageId() {
        return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    getConnectionStatus() {
        const serviceStatus = this.agentZeroService.getStatus();
        const wsStatus = this.webSocketService?.getConnectionStatus();
        return {
            serviceRunning: serviceStatus.running,
            webSocketConnected: wsStatus?.connected || false,
            serviceUrl: this.agentZeroService.getServiceUrl()
        };
    }
    dispose() {
        if (this.webSocketService) {
            this.webSocketService.dispose();
        }
        this.messageCallbacks.clear();
        this.outputChannel.dispose();
    }
}
exports.ApiService = ApiService;
//# sourceMappingURL=ApiService.js.map