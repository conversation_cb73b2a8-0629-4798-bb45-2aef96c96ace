import * as vscode from 'vscode';
import { spawn } from 'child_process';
import * as path from 'path';

export interface GitOperation {
    command: 'status' | 'add' | 'commit' | 'push' | 'pull' | 'branch' | 'checkout' | 'diff' | 'log' | 'clone';
    args?: string[];
    message?: string;
    files?: string[];
    branch?: string;
    remote?: string;
    url?: string;
}

export interface GitResult {
    success: boolean;
    output: string;
    error?: string;
    data?: any;
}

export interface GitStatus {
    branch: string;
    ahead: number;
    behind: number;
    staged: string[];
    modified: string[];
    untracked: string[];
    conflicted: string[];
}

export class VSCodeGitIntegration {
    private outputChannel: vscode.OutputChannel;
    private gitExtension: vscode.Extension<any> | undefined;

    constructor() {
        this.outputChannel = vscode.window.createOutputChannel('Agent Zero Git');
        this.gitExtension = vscode.extensions.getExtension('vscode.git');
    }

    public async executeGitOperation(operation: GitOperation): Promise<GitResult> {
        try {
            this.outputChannel.appendLine(`Executing Git operation: ${operation.command}`);

            // Try to use VS Code Git extension first
            if (this.gitExtension && this.gitExtension.isActive) {
                const result = await this.executeViaGitExtension(operation);
                if (result) {
                    return result;
                }
            }

            // Fallback to command line git
            return await this.executeViaCommandLine(operation);

        } catch (error) {
            this.outputChannel.appendLine(`Git operation failed: ${error}`);
            return {
                success: false,
                output: '',
                error: error.toString()
            };
        }
    }

    private async executeViaGitExtension(operation: GitOperation): Promise<GitResult | null> {
        try {
            const gitApi = this.gitExtension?.exports.getAPI(1);
            if (!gitApi) {
                return null;
            }

            const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
            if (!workspaceFolder) {
                throw new Error('No workspace folder found');
            }

            const repository = gitApi.getRepository(workspaceFolder.uri);
            if (!repository) {
                throw new Error('No Git repository found in workspace');
            }

            switch (operation.command) {
                case 'status':
                    return await this.getStatusViaExtension(repository);
                case 'add':
                    return await this.addFilesViaExtension(repository, operation.files || []);
                case 'commit':
                    return await this.commitViaExtension(repository, operation.message || '');
                case 'push':
                    return await this.pushViaExtension(repository);
                case 'pull':
                    return await this.pullViaExtension(repository);
                case 'branch':
                    return await this.getBranchesViaExtension(repository);
                case 'checkout':
                    return await this.checkoutViaExtension(repository, operation.branch || '');
                default:
                    return null; // Fallback to command line
            }

        } catch (error) {
            this.outputChannel.appendLine(`Git extension operation failed: ${error}`);
            return null;
        }
    }

    private async getStatusViaExtension(repository: any): Promise<GitResult> {
        try {
            const state = repository.state;
            const status: GitStatus = {
                branch: state.HEAD?.name || 'unknown',
                ahead: state.HEAD?.ahead || 0,
                behind: state.HEAD?.behind || 0,
                staged: state.indexChanges.map((change: any) => change.uri.fsPath),
                modified: state.workingTreeChanges.map((change: any) => change.uri.fsPath),
                untracked: state.untrackedChanges?.map((change: any) => change.uri.fsPath) || [],
                conflicted: state.mergeChanges?.map((change: any) => change.uri.fsPath) || []
            };

            return {
                success: true,
                output: JSON.stringify(status, null, 2),
                data: status
            };

        } catch (error) {
            throw new Error(`Failed to get status: ${error}`);
        }
    }

    private async addFilesViaExtension(repository: any, files: string[]): Promise<GitResult> {
        try {
            if (files.length === 0) {
                // Add all changes
                await repository.add([]);
            } else {
                // Add specific files
                const uris = files.map(file => vscode.Uri.file(file));
                await repository.add(uris);
            }

            return {
                success: true,
                output: `Added ${files.length || 'all'} files to staging area`
            };

        } catch (error) {
            throw new Error(`Failed to add files: ${error}`);
        }
    }

    private async commitViaExtension(repository: any, message: string): Promise<GitResult> {
        try {
            if (!message) {
                throw new Error('Commit message is required');
            }

            await repository.commit(message);

            return {
                success: true,
                output: `Committed with message: "${message}"`
            };

        } catch (error) {
            throw new Error(`Failed to commit: ${error}`);
        }
    }

    private async pushViaExtension(repository: any): Promise<GitResult> {
        try {
            await repository.push();

            return {
                success: true,
                output: 'Successfully pushed to remote repository'
            };

        } catch (error) {
            throw new Error(`Failed to push: ${error}`);
        }
    }

    private async pullViaExtension(repository: any): Promise<GitResult> {
        try {
            await repository.pull();

            return {
                success: true,
                output: 'Successfully pulled from remote repository'
            };

        } catch (error) {
            throw new Error(`Failed to pull: ${error}`);
        }
    }

    private async getBranchesViaExtension(repository: any): Promise<GitResult> {
        try {
            const refs = repository.state.refs;
            const branches = refs.filter((ref: any) => ref.type === 0); // Local branches
            const remoteBranches = refs.filter((ref: any) => ref.type === 1); // Remote branches

            const branchInfo = {
                current: repository.state.HEAD?.name,
                local: branches.map((branch: any) => branch.name),
                remote: remoteBranches.map((branch: any) => branch.name)
            };

            return {
                success: true,
                output: JSON.stringify(branchInfo, null, 2),
                data: branchInfo
            };

        } catch (error) {
            throw new Error(`Failed to get branches: ${error}`);
        }
    }

    private async checkoutViaExtension(repository: any, branch: string): Promise<GitResult> {
        try {
            await repository.checkout(branch);

            return {
                success: true,
                output: `Checked out branch: ${branch}`
            };

        } catch (error) {
            throw new Error(`Failed to checkout branch: ${error}`);
        }
    }

    private async executeViaCommandLine(operation: GitOperation): Promise<GitResult> {
        return new Promise((resolve) => {
            const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
            const cwd = workspaceFolder ? workspaceFolder.uri.fsPath : process.cwd();

            let args: string[] = [];

            switch (operation.command) {
                case 'status':
                    args = ['status', '--porcelain', '-b'];
                    break;
                case 'add':
                    args = ['add', ...(operation.files || ['.'])];
                    break;
                case 'commit':
                    args = ['commit', '-m', operation.message || 'Automated commit'];
                    break;
                case 'push':
                    args = ['push', operation.remote || 'origin', operation.branch || 'HEAD'];
                    break;
                case 'pull':
                    args = ['pull', operation.remote || 'origin', operation.branch || 'HEAD'];
                    break;
                case 'branch':
                    args = ['branch', '-a'];
                    break;
                case 'checkout':
                    args = ['checkout', operation.branch || 'main'];
                    break;
                case 'diff':
                    args = ['diff', ...(operation.files || [])];
                    break;
                case 'log':
                    args = ['log', '--oneline', '-10'];
                    break;
                case 'clone':
                    args = ['clone', operation.url || '', operation.args?.[0] || ''];
                    break;
                default:
                    args = [operation.command, ...(operation.args || [])];
            }

            const process = spawn('git', args, { cwd });

            let stdout = '';
            let stderr = '';

            process.stdout?.on('data', (data) => {
                stdout += data.toString();
            });

            process.stderr?.on('data', (data) => {
                stderr += data.toString();
            });

            process.on('close', (code) => {
                const success = code === 0;
                
                resolve({
                    success,
                    output: stdout,
                    error: success ? undefined : stderr,
                    data: success ? this.parseGitOutput(operation.command, stdout) : undefined
                });
            });

            process.on('error', (error) => {
                resolve({
                    success: false,
                    output: '',
                    error: error.toString()
                });
            });
        });
    }

    private parseGitOutput(command: string, output: string): any {
        switch (command) {
            case 'status':
                return this.parseStatusOutput(output);
            case 'branch':
                return this.parseBranchOutput(output);
            case 'log':
                return this.parseLogOutput(output);
            default:
                return { raw: output };
        }
    }

    private parseStatusOutput(output: string): GitStatus {
        const lines = output.split('\n').filter(line => line.trim());
        const branchLine = lines[0];
        const branch = branchLine.match(/## (.+?)(?:\.\.\.|$)/)?.[1] || 'unknown';

        const staged: string[] = [];
        const modified: string[] = [];
        const untracked: string[] = [];
        const conflicted: string[] = [];

        for (let i = 1; i < lines.length; i++) {
            const line = lines[i];
            const status = line.substring(0, 2);
            const file = line.substring(3);

            if (status.includes('U') || status.includes('A') || status.includes('D')) {
                conflicted.push(file);
            } else if (status[0] !== ' ') {
                staged.push(file);
            } else if (status[1] !== ' ') {
                modified.push(file);
            } else if (status === '??') {
                untracked.push(file);
            }
        }

        return {
            branch,
            ahead: 0, // Would need additional parsing
            behind: 0, // Would need additional parsing
            staged,
            modified,
            untracked,
            conflicted
        };
    }

    private parseBranchOutput(output: string): any {
        const lines = output.split('\n').filter(line => line.trim());
        const local: string[] = [];
        const remote: string[] = [];
        let current = '';

        for (const line of lines) {
            const trimmed = line.trim();
            if (trimmed.startsWith('* ')) {
                current = trimmed.substring(2);
                local.push(current);
            } else if (trimmed.startsWith('remotes/')) {
                remote.push(trimmed.substring(8));
            } else if (trimmed && !trimmed.startsWith('remotes/')) {
                local.push(trimmed);
            }
        }

        return { current, local, remote };
    }

    private parseLogOutput(output: string): any {
        const lines = output.split('\n').filter(line => line.trim());
        return {
            commits: lines.map(line => {
                const [hash, ...messageParts] = line.split(' ');
                return {
                    hash: hash,
                    message: messageParts.join(' ')
                };
            })
        };
    }

    public async showGitStatus(): Promise<void> {
        const result = await this.executeGitOperation({ command: 'status' });
        
        if (result.success && result.data) {
            const status = result.data as GitStatus;
            
            const panel = vscode.window.createWebviewPanel(
                'gitStatus',
                'Git Status',
                vscode.ViewColumn.Two,
                { enableScripts: true }
            );

            panel.webview.html = this.getGitStatusHtml(status);
        } else {
            vscode.window.showErrorMessage(`Failed to get Git status: ${result.error}`);
        }
    }

    private getGitStatusHtml(status: GitStatus): string {
        return `
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body { font-family: var(--vscode-font-family); padding: 20px; }
                .status-section { margin-bottom: 20px; }
                .file-list { list-style: none; padding: 0; }
                .file-item { padding: 5px; margin: 2px 0; border-radius: 3px; }
                .staged { background-color: var(--vscode-gitDecoration-addedResourceForeground); }
                .modified { background-color: var(--vscode-gitDecoration-modifiedResourceForeground); }
                .untracked { background-color: var(--vscode-gitDecoration-untrackedResourceForeground); }
                .conflicted { background-color: var(--vscode-gitDecoration-conflictingResourceForeground); }
            </style>
        </head>
        <body>
            <h1>Git Status</h1>
            <div class="status-section">
                <h2>Branch: ${status.branch}</h2>
                <p>Ahead: ${status.ahead} | Behind: ${status.behind}</p>
            </div>
            
            ${this.renderFileSection('Staged Changes', status.staged, 'staged')}
            ${this.renderFileSection('Modified Files', status.modified, 'modified')}
            ${this.renderFileSection('Untracked Files', status.untracked, 'untracked')}
            ${this.renderFileSection('Conflicted Files', status.conflicted, 'conflicted')}
        </body>
        </html>`;
    }

    private renderFileSection(title: string, files: string[], className: string): string {
        if (files.length === 0) {
            return '';
        }

        const fileItems = files.map(file => 
            `<li class="file-item ${className}">${file}</li>`
        ).join('');

        return `
        <div class="status-section">
            <h3>${title} (${files.length})</h3>
            <ul class="file-list">${fileItems}</ul>
        </div>`;
    }

    public dispose(): void {
        this.outputChannel.dispose();
    }
}
