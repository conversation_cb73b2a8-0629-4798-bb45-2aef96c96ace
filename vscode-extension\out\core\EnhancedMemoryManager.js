"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnhancedMemoryManager = void 0;
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
class EnhancedMemoryManager {
    constructor(context, modelManager) {
        this.context = context;
        this.modelManager = modelManager;
        this._onDidChangeTreeData = new vscode.EventEmitter();
        this.onDidChangeTreeData = this._onDidChangeTreeData.event;
        this.memories = new Map();
        this.workspaceKnowledge = new Map();
        this.memoryCounter = 0;
        this.outputChannel = vscode.window.createOutputChannel('Agent Zero Enhanced Memory');
        this.initializeMemorySystem();
        this.setupWorkspaceWatchers();
        this.setupWorkspaceContextGeneration();
    }
    async initializeMemorySystem() {
        await this.loadMemoryFromStorage();
        await this.loadWorkspaceKnowledge();
        await this.indexCurrentWorkspace();
        await this.generateProjectContext();
    }
    setupWorkspaceWatchers() {
        // Watch for file changes
        const fileWatcher = vscode.workspace.createFileSystemWatcher('**/*');
        fileWatcher.onDidCreate(async (uri) => {
            await this.handleFileCreated(uri);
        });
        fileWatcher.onDidChange(async (uri) => {
            await this.handleFileChanged(uri);
        });
        fileWatcher.onDidDelete(async (uri) => {
            await this.handleFileDeleted(uri);
        });
        this.context.subscriptions.push(fileWatcher);
        // Watch for workspace folder changes
        vscode.workspace.onDidChangeWorkspaceFolders(async (event) => {
            for (const folder of event.added) {
                await this.indexWorkspaceFolder(folder);
            }
            for (const folder of event.removed) {
                this.removeWorkspaceKnowledge(folder.uri.fsPath);
            }
        });
    }
    async saveMemory(content, type, title, tags = [], importance = 5, metadata = {}) {
        const memory = {
            id: `memory-${++this.memoryCounter}`,
            type,
            content,
            title: title || this.generateTitle(content, type),
            tags,
            timestamp: new Date(),
            importance,
            metadata: {
                workspaceFolder: vscode.workspace.workspaceFolders?.[0]?.uri.fsPath,
                ...metadata
            },
            relationships: {
                childIds: [],
                relatedIds: []
            }
        };
        // Generate embedding for semantic search
        if (this.modelManager) {
            try {
                memory.embedding = await this.modelManager.generateEmbeddings(content);
            }
            catch (error) {
                this.outputChannel.appendLine(`Failed to generate embedding: ${error}`);
            }
        }
        this.memories.set(memory.id, memory);
        await this.saveMemoryToStorage();
        this.refresh();
        this.outputChannel.appendLine(`Saved memory: ${memory.title}`);
        return memory.id;
    }
    async loadMemory(query, type, limit = 10) {
        let results = [];
        // First try semantic search if embeddings are available
        if (this.modelManager) {
            try {
                const queryEmbedding = await this.modelManager.generateEmbeddings(query);
                results = await this.semanticSearch(queryEmbedding, type, limit);
            }
            catch (error) {
                this.outputChannel.appendLine(`Semantic search failed: ${error}`);
            }
        }
        // Fallback to text search if semantic search fails or returns few results
        if (results.length < limit / 2) {
            const textResults = this.textSearch(query, type, limit - results.length);
            results = [...results, ...textResults];
        }
        // Remove duplicates and sort by relevance
        const uniqueResults = Array.from(new Map(results.map(r => [r.id, r])).values());
        return uniqueResults.slice(0, limit);
    }
    async semanticSearch(queryEmbedding, type, limit = 10) {
        const candidates = [];
        for (const memory of this.memories.values()) {
            if (type && memory.type !== type)
                continue;
            if (!memory.embedding)
                continue;
            const similarity = this.cosineSimilarity(queryEmbedding, memory.embedding);
            candidates.push({ memory, similarity });
        }
        return candidates
            .sort((a, b) => b.similarity - a.similarity)
            .slice(0, limit)
            .map(c => c.memory);
    }
    textSearch(query, type, limit = 10) {
        const lowerQuery = query.toLowerCase();
        const results = [];
        for (const memory of this.memories.values()) {
            if (type && memory.type !== type)
                continue;
            let score = 0;
            // Title match (highest weight)
            if (memory.title.toLowerCase().includes(lowerQuery)) {
                score += 10;
            }
            // Content match
            if (memory.content.toLowerCase().includes(lowerQuery)) {
                score += 5;
            }
            // Tag match
            for (const tag of memory.tags) {
                if (tag.toLowerCase().includes(lowerQuery)) {
                    score += 3;
                }
            }
            // Importance boost
            score += memory.importance / 10;
            if (score > 0) {
                results.push({ memory, score });
            }
        }
        return Promise.resolve(results
            .sort((a, b) => b.score - a.score)
            .slice(0, limit)
            .map(r => r.memory));
    }
    async indexCurrentWorkspace() {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders)
            return;
        for (const folder of workspaceFolders) {
            await this.indexWorkspaceFolder(folder);
        }
    }
    async indexWorkspaceFolder(folder) {
        this.outputChannel.appendLine(`Indexing workspace: ${folder.name}`);
        const knowledge = {
            id: folder.uri.fsPath,
            workspacePath: folder.uri.fsPath,
            projectName: folder.name,
            description: '',
            technologies: [],
            structure: {
                directories: [],
                importantFiles: [],
                configFiles: []
            },
            patterns: {
                codingStyle: '',
                architecture: '',
                conventions: []
            },
            lastUpdated: new Date(),
            fileIndex: new Map()
        };
        // Analyze workspace structure
        await this.analyzeWorkspaceStructure(folder.uri, knowledge);
        // Detect technologies and patterns
        await this.detectTechnologies(knowledge);
        this.workspaceKnowledge.set(folder.uri.fsPath, knowledge);
        await this.saveWorkspaceKnowledge();
        this.outputChannel.appendLine(`Indexed ${knowledge.fileIndex.size} files in ${folder.name}`);
    }
    async analyzeWorkspaceStructure(uri, knowledge) {
        try {
            const entries = await vscode.workspace.fs.readDirectory(uri);
            for (const [name, type] of entries) {
                if (name.startsWith('.') && !this.isImportantDotFile(name))
                    continue;
                const fullPath = path.join(uri.fsPath, name);
                const relativePath = path.relative(knowledge.workspacePath, fullPath);
                if (type === vscode.FileType.Directory) {
                    knowledge.structure.directories.push(relativePath);
                    // Recursively analyze subdirectories (with depth limit)
                    if (this.shouldAnalyzeDirectory(name) && relativePath.split(path.sep).length < 5) {
                        await this.analyzeWorkspaceStructure(vscode.Uri.file(fullPath), knowledge);
                    }
                }
                else if (type === vscode.FileType.File) {
                    await this.analyzeFile(fullPath, knowledge);
                }
            }
        }
        catch (error) {
            this.outputChannel.appendLine(`Error analyzing ${uri.fsPath}: ${error}`);
        }
    }
    async analyzeFile(filePath, knowledge) {
        try {
            const stat = await vscode.workspace.fs.stat(vscode.Uri.file(filePath));
            const relativePath = path.relative(knowledge.workspacePath, filePath);
            const ext = path.extname(filePath);
            const fileKnowledge = {
                path: relativePath,
                type: this.getFileType(filePath),
                language: this.getLanguageFromExtension(ext),
                size: stat.size,
                lastModified: new Date(stat.mtime),
                summary: '',
                functions: [],
                classes: [],
                imports: [],
                exports: [],
                dependencies: [],
                complexity: 0
            };
            // Analyze file content for source files
            if (fileKnowledge.type === 'source' && stat.size < 1024 * 1024) { // Max 1MB
                await this.analyzeSourceFile(filePath, fileKnowledge);
            }
            knowledge.fileIndex.set(relativePath, fileKnowledge);
            // Categorize important files
            if (this.isConfigFile(filePath)) {
                knowledge.structure.configFiles.push(relativePath);
            }
            if (this.isImportantFile(filePath)) {
                knowledge.structure.importantFiles.push(relativePath);
            }
        }
        catch (error) {
            this.outputChannel.appendLine(`Error analyzing file ${filePath}: ${error}`);
        }
    }
    async analyzeSourceFile(filePath, fileKnowledge) {
        try {
            const document = await vscode.workspace.openTextDocument(filePath);
            const content = document.getText();
            // Basic code analysis
            fileKnowledge.summary = this.generateFileSummary(content, fileKnowledge.language);
            fileKnowledge.functions = this.extractFunctions(content, fileKnowledge.language);
            fileKnowledge.classes = this.extractClasses(content, fileKnowledge.language);
            fileKnowledge.imports = this.extractImports(content, fileKnowledge.language);
            fileKnowledge.exports = this.extractExports(content, fileKnowledge.language);
            fileKnowledge.complexity = this.calculateComplexity(content);
            // Generate embedding for semantic search
            if (this.modelManager && content.length < 10000) {
                try {
                    fileKnowledge.embedding = await this.modelManager.generateEmbeddings(`${fileKnowledge.summary}\n${fileKnowledge.functions.join(' ')}\n${fileKnowledge.classes.join(' ')}`);
                }
                catch (error) {
                    this.outputChannel.appendLine(`Failed to generate file embedding: ${error}`);
                }
            }
        }
        catch (error) {
            this.outputChannel.appendLine(`Error analyzing source file ${filePath}: ${error}`);
        }
    }
    generateFileSummary(content, language) {
        const lines = content.split('\n');
        const firstComment = this.extractFirstComment(lines, language);
        if (firstComment)
            return firstComment;
        // Generate basic summary based on content
        const summary = `${language} file with ${lines.length} lines`;
        return summary;
    }
    extractFirstComment(lines, language) {
        const commentPatterns = {
            'javascript': [/^\s*\/\*\*(.*?)\*\//s, /^\s*\/\/(.*)/],
            'typescript': [/^\s*\/\*\*(.*?)\*\//s, /^\s*\/\/(.*)/],
            'python': [/^\s*"""(.*?)"""/s, /^\s*#(.*)/],
            'java': [/^\s*\/\*\*(.*?)\*\//s, /^\s*\/\/(.*)/],
            'cpp': [/^\s*\/\*\*(.*?)\*\//s, /^\s*\/\/(.*)/],
            'c': [/^\s*\/\*\*(.*?)\*\//s, /^\s*\/\/(.*)/]
        };
        const patterns = commentPatterns[language] || commentPatterns['javascript'];
        const content = lines.slice(0, 10).join('\n');
        for (const pattern of patterns) {
            const match = content.match(pattern);
            if (match) {
                return match[1].trim().substring(0, 200);
            }
        }
        return '';
    }
    extractFunctions(content, language) {
        const functionPatterns = {
            'javascript': /function\s+(\w+)|(\w+)\s*=\s*function|(\w+)\s*=\s*\(.*?\)\s*=>/g,
            'typescript': /function\s+(\w+)|(\w+)\s*=\s*function|(\w+)\s*=\s*\(.*?\)\s*=>|(\w+)\s*\(.*?\):\s*\w+/g,
            'python': /def\s+(\w+)/g,
            'java': /(?:public|private|protected)?\s*(?:static)?\s*\w+\s+(\w+)\s*\(/g,
            'cpp': /\w+\s+(\w+)\s*\(/g,
            'c': /\w+\s+(\w+)\s*\(/g
        };
        const pattern = functionPatterns[language];
        if (!pattern)
            return [];
        const functions = [];
        let match;
        while ((match = pattern.exec(content)) !== null) {
            const functionName = match[1] || match[2] || match[3] || match[4];
            if (functionName && !functions.includes(functionName)) {
                functions.push(functionName);
            }
        }
        return functions;
    }
    extractClasses(content, language) {
        const classPatterns = {
            'javascript': /class\s+(\w+)/g,
            'typescript': /class\s+(\w+)/g,
            'python': /class\s+(\w+)/g,
            'java': /(?:public|private|protected)?\s*class\s+(\w+)/g,
            'cpp': /class\s+(\w+)/g,
            'c': /struct\s+(\w+)/g
        };
        const pattern = classPatterns[language];
        if (!pattern)
            return [];
        const classes = [];
        let match;
        while ((match = pattern.exec(content)) !== null) {
            if (!classes.includes(match[1])) {
                classes.push(match[1]);
            }
        }
        return classes;
    }
    extractImports(content, language) {
        const importPatterns = {
            'javascript': /import.*?from\s+['"]([^'"]+)['"]/g,
            'typescript': /import.*?from\s+['"]([^'"]+)['"]/g,
            'python': /(?:from\s+(\w+)|import\s+(\w+))/g,
            'java': /import\s+([\w.]+)/g,
            'cpp': /#include\s*[<"]([^>"]+)[>"]/g,
            'c': /#include\s*[<"]([^>"]+)[>"]/g
        };
        const pattern = importPatterns[language];
        if (!pattern)
            return [];
        const imports = [];
        let match;
        while ((match = pattern.exec(content)) !== null) {
            const importName = match[1] || match[2];
            if (importName && !imports.includes(importName)) {
                imports.push(importName);
            }
        }
        return imports;
    }
    extractExports(content, language) {
        const exportPatterns = {
            'javascript': /export\s+(?:default\s+)?(?:class|function|const|let|var)?\s*(\w+)/g,
            'typescript': /export\s+(?:default\s+)?(?:class|function|const|let|var|interface|type)?\s*(\w+)/g
        };
        const pattern = exportPatterns[language];
        if (!pattern)
            return [];
        const exports = [];
        let match;
        while ((match = pattern.exec(content)) !== null) {
            if (!exports.includes(match[1])) {
                exports.push(match[1]);
            }
        }
        return exports;
    }
    calculateComplexity(content) {
        // Simple complexity calculation based on control structures
        const complexityPatterns = [
            /\bif\b/g,
            /\belse\b/g,
            /\bfor\b/g,
            /\bwhile\b/g,
            /\bswitch\b/g,
            /\bcatch\b/g,
            /\btry\b/g
        ];
        let complexity = 1; // Base complexity
        for (const pattern of complexityPatterns) {
            const matches = content.match(pattern);
            if (matches) {
                complexity += matches.length;
            }
        }
        return complexity;
    }
    async detectTechnologies(knowledge) {
        const technologies = new Set();
        // Check package files
        for (const [filePath, fileInfo] of knowledge.fileIndex) {
            if (filePath.includes('package.json')) {
                technologies.add('Node.js');
                technologies.add('JavaScript');
            }
            else if (filePath.includes('requirements.txt') || filePath.includes('setup.py')) {
                technologies.add('Python');
            }
            else if (filePath.includes('Cargo.toml')) {
                technologies.add('Rust');
            }
            else if (filePath.includes('go.mod')) {
                technologies.add('Go');
            }
            else if (filePath.includes('pom.xml') || filePath.includes('build.gradle')) {
                technologies.add('Java');
            }
            // Check by file extension
            switch (fileInfo.language) {
                case 'typescript':
                    technologies.add('TypeScript');
                    break;
                case 'python':
                    technologies.add('Python');
                    break;
                case 'java':
                    technologies.add('Java');
                    break;
                case 'cpp':
                    technologies.add('C++');
                    break;
                case 'rust':
                    technologies.add('Rust');
                    break;
                case 'go':
                    technologies.add('Go');
                    break;
            }
        }
        knowledge.technologies = Array.from(technologies);
    }
    // Helper methods
    isImportantDotFile(name) {
        const important = ['.gitignore', '.env', '.vscode', '.github', '.eslintrc', '.prettierrc'];
        return important.some(pattern => name.startsWith(pattern));
    }
    shouldAnalyzeDirectory(name) {
        const skip = ['node_modules', '.git', 'dist', 'build', '__pycache__', '.pytest_cache'];
        return !skip.includes(name);
    }
    getFileType(filePath) {
        const name = path.basename(filePath).toLowerCase();
        const ext = path.extname(filePath).toLowerCase();
        if (name.includes('test') || name.includes('spec'))
            return 'test';
        if (name.includes('config') || name.includes('setting'))
            return 'config';
        if (name.includes('readme') || ext === '.md')
            return 'documentation';
        if (['.js', '.ts', '.py', '.java', '.cpp', '.c', '.rs', '.go'].includes(ext))
            return 'source';
        return 'asset';
    }
    getLanguageFromExtension(ext) {
        const mapping = {
            '.js': 'javascript',
            '.ts': 'typescript',
            '.py': 'python',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c',
            '.rs': 'rust',
            '.go': 'go',
            '.php': 'php',
            '.rb': 'ruby',
            '.cs': 'csharp',
            '.swift': 'swift',
            '.kt': 'kotlin'
        };
        return mapping[ext] || 'text';
    }
    isConfigFile(filePath) {
        const configFiles = [
            'package.json', 'tsconfig.json', 'webpack.config.js', '.eslintrc',
            'requirements.txt', 'setup.py', 'Cargo.toml', 'go.mod', 'pom.xml'
        ];
        const name = path.basename(filePath);
        return configFiles.some(config => name.includes(config));
    }
    isImportantFile(filePath) {
        const important = ['README', 'LICENSE', 'CHANGELOG', 'index', 'main', 'app'];
        const name = path.basename(filePath, path.extname(filePath)).toLowerCase();
        return important.some(pattern => name.includes(pattern));
    }
    generateTitle(content, type) {
        const firstLine = content.split('\n')[0].trim();
        const maxLength = 50;
        if (firstLine.length <= maxLength) {
            return firstLine;
        }
        return firstLine.substring(0, maxLength - 3) + '...';
    }
    cosineSimilarity(a, b) {
        if (a.length !== b.length)
            return 0;
        let dotProduct = 0;
        let normA = 0;
        let normB = 0;
        for (let i = 0; i < a.length; i++) {
            dotProduct += a[i] * b[i];
            normA += a[i] * a[i];
            normB += b[i] * b[i];
        }
        return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
    }
    // Event handlers
    async handleFileCreated(uri) {
        const workspaceFolder = vscode.workspace.getWorkspaceFolder(uri);
        if (!workspaceFolder)
            return;
        const knowledge = this.workspaceKnowledge.get(workspaceFolder.uri.fsPath);
        if (knowledge) {
            await this.analyzeFile(uri.fsPath, knowledge);
            await this.saveWorkspaceKnowledge();
        }
    }
    async handleFileChanged(uri) {
        // Re-analyze changed files
        await this.handleFileCreated(uri);
    }
    async handleFileDeleted(uri) {
        const workspaceFolder = vscode.workspace.getWorkspaceFolder(uri);
        if (!workspaceFolder)
            return;
        const knowledge = this.workspaceKnowledge.get(workspaceFolder.uri.fsPath);
        if (knowledge) {
            const relativePath = path.relative(workspaceFolder.uri.fsPath, uri.fsPath);
            knowledge.fileIndex.delete(relativePath);
            await this.saveWorkspaceKnowledge();
        }
    }
    removeWorkspaceKnowledge(workspacePath) {
        this.workspaceKnowledge.delete(workspacePath);
    }
    // Storage methods
    async loadMemoryFromStorage() {
        try {
            const memoryData = this.context.globalState.get('agent-zero.enhanced-memories', {});
            for (const [id, memory] of Object.entries(memoryData)) {
                memory.timestamp = new Date(memory.timestamp);
                this.memories.set(id, memory);
            }
            this.memoryCounter = this.memories.size;
        }
        catch (error) {
            this.outputChannel.appendLine(`Failed to load memory: ${error}`);
        }
    }
    async saveMemoryToStorage() {
        try {
            const memoryData = {};
            for (const [id, memory] of this.memories.entries()) {
                memoryData[id] = memory;
            }
            await this.context.globalState.update('agent-zero.enhanced-memories', memoryData);
        }
        catch (error) {
            this.outputChannel.appendLine(`Failed to save memory: ${error}`);
        }
    }
    async loadWorkspaceKnowledge() {
        try {
            const workspaceData = this.context.workspaceState.get('agent-zero.workspace-knowledge', {});
            for (const [path, data] of Object.entries(workspaceData)) {
                const knowledge = {
                    ...data,
                    lastUpdated: new Date(data.lastUpdated),
                    fileIndex: new Map(Object.entries(data.fileIndex || {}))
                };
                this.workspaceKnowledge.set(path, knowledge);
            }
        }
        catch (error) {
            this.outputChannel.appendLine(`Failed to load workspace knowledge: ${error}`);
        }
    }
    async saveWorkspaceKnowledge() {
        try {
            const workspaceData = {};
            for (const [path, knowledge] of this.workspaceKnowledge.entries()) {
                workspaceData[path] = {
                    ...knowledge,
                    fileIndex: Object.fromEntries(knowledge.fileIndex)
                };
            }
            await this.context.workspaceState.update('agent-zero.workspace-knowledge', workspaceData);
        }
        catch (error) {
            this.outputChannel.appendLine(`Failed to save workspace knowledge: ${error}`);
        }
    }
    // TreeDataProvider implementation
    getTreeItem(element) {
        const item = new vscode.TreeItem(element.title, vscode.TreeItemCollapsibleState.None);
        item.description = `${element.type} - ${element.importance}/10`;
        item.tooltip = `${element.content.substring(0, 200)}...\nTags: ${element.tags.join(', ')}\nCreated: ${element.timestamp.toLocaleString()}`;
        item.contextValue = 'enhancedMemoryItem';
        // Set icon based on type
        const iconMap = {
            'fact': 'info',
            'code': 'code',
            'solution': 'lightbulb',
            'instruction': 'list-ordered',
            'knowledge': 'book',
            'conversation': 'comment',
            'file-context': 'file'
        };
        item.iconPath = new vscode.ThemeIcon(iconMap[element.type] || 'circle');
        return item;
    }
    getChildren(element) {
        if (!element) {
            return Promise.resolve(Array.from(this.memories.values()));
        }
        return Promise.resolve([]);
    }
    refresh() {
        this._onDidChangeTreeData.fire();
    }
    setupWorkspaceContextGeneration() {
        // Listen for workspace folder changes to regenerate context
        vscode.workspace.onDidChangeWorkspaceFolders(async (event) => {
            for (const folder of event.added) {
                await this.generateProjectContextForFolder(folder);
            }
        });
        // Listen for configuration file changes to update context
        const configWatcher = vscode.workspace.createFileSystemWatcher('**/{package.json,requirements.txt,Cargo.toml,go.mod,pom.xml,*.config.js,*.config.ts}');
        configWatcher.onDidChange(async (uri) => {
            await this.updateProjectContextFromConfig(uri);
        });
        this.context.subscriptions.push(configWatcher);
    }
    async generateProjectContext() {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders)
            return;
        for (const folder of workspaceFolders) {
            await this.generateProjectContextForFolder(folder);
        }
    }
    async generateProjectContextForFolder(folder) {
        try {
            this.outputChannel.appendLine(`Generating project context for: ${folder.name}`);
            const knowledge = this.workspaceKnowledge.get(folder.uri.fsPath);
            if (!knowledge) {
                await this.indexWorkspaceFolder(folder);
                return;
            }
            // Generate comprehensive project understanding
            const projectContext = await this.buildProjectContext(knowledge);
            // Save as memory for the AI to use
            await this.saveMemory(projectContext.summary, 'knowledge', `Project Context: ${knowledge.projectName}`, ['project', 'context', 'workspace', ...knowledge.technologies.map(t => t.toLowerCase())], 10, // Highest importance
            {
                workspaceFolder: folder.uri.fsPath,
                projectName: knowledge.projectName,
                technologies: knowledge.technologies,
                lastUpdated: new Date().toISOString()
            });
            // Generate specific context for different aspects
            await this.generateArchitectureContext(knowledge);
            await this.generateTechnologyContext(knowledge);
            await this.generatePatternsContext(knowledge);
            this.outputChannel.appendLine(`Project context generated for: ${folder.name}`);
        }
        catch (error) {
            this.outputChannel.appendLine(`Failed to generate project context: ${error}`);
        }
    }
    async buildProjectContext(knowledge) {
        const context = {
            projectName: knowledge.projectName,
            description: knowledge.description,
            technologies: knowledge.technologies,
            structure: knowledge.structure,
            patterns: knowledge.patterns,
            fileCount: knowledge.fileIndex.size,
            lastUpdated: knowledge.lastUpdated
        };
        // Generate AI-powered project summary
        const prompt = `
Analyze this project structure and generate a comprehensive understanding:

Project: ${context.projectName}
Technologies: ${context.technologies.join(', ')}
File Count: ${context.fileCount}

Directory Structure:
${context.structure.directories.slice(0, 20).map(dir => `- ${dir}`).join('\n')}

Important Files:
${context.structure.importantFiles.map(file => `- ${file}`).join('\n')}

Config Files:
${context.structure.configFiles.map(file => `- ${file}`).join('\n')}

Please provide:
1. Project purpose and type (web app, library, tool, etc.)
2. Main architecture pattern used
3. Key features and capabilities
4. Development workflow and conventions
5. Dependencies and integrations
6. Potential areas for improvement

Be concise but comprehensive. This will be used as context for an AI assistant.
        `.trim();
        let summary = '';
        try {
            if (this.modelManager) {
                summary = await this.modelManager.processMessage(prompt, {
                    systemMessage: 'You are a senior software architect analyzing a codebase. Provide clear, actionable insights.'
                });
            }
            else {
                summary = this.generateBasicProjectSummary(context);
            }
        }
        catch (error) {
            this.outputChannel.appendLine(`Failed to generate AI summary: ${error}`);
            summary = this.generateBasicProjectSummary(context);
        }
        return { summary, details: context };
    }
    generateBasicProjectSummary(context) {
        return `
Project: ${context.projectName}

This is a ${context.technologies.join(', ')} project with ${context.fileCount} files.

Technologies Used:
${context.technologies.map((tech) => `- ${tech}`).join('\n')}

Project Structure:
- ${context.structure.directories.length} directories
- ${context.structure.importantFiles.length} important files
- ${context.structure.configFiles.length} configuration files

Key Directories:
${context.structure.directories.slice(0, 10).map((dir) => `- ${dir}`).join('\n')}

This project appears to be a ${this.inferProjectType(context)} based on the file structure and technologies used.
        `.trim();
    }
    inferProjectType(context) {
        const technologies = context.technologies.map((t) => t.toLowerCase());
        const directories = context.structure.directories.map((d) => d.toLowerCase());
        if (technologies.includes('react') || technologies.includes('vue') || technologies.includes('angular')) {
            return 'frontend web application';
        }
        if (technologies.includes('express') || technologies.includes('fastapi') || technologies.includes('django')) {
            return 'backend web service';
        }
        if (directories.some((d) => d.includes('test') || d.includes('spec'))) {
            return 'software library with comprehensive testing';
        }
        if (technologies.includes('electron')) {
            return 'desktop application';
        }
        if (technologies.includes('react native') || technologies.includes('flutter')) {
            return 'mobile application';
        }
        return 'software project';
    }
    async generateArchitectureContext(knowledge) {
        const architectureInfo = this.analyzeArchitecture(knowledge);
        await this.saveMemory(architectureInfo, 'knowledge', `Architecture: ${knowledge.projectName}`, ['architecture', 'structure', knowledge.projectName.toLowerCase()], 8, {
            workspaceFolder: knowledge.workspacePath,
            type: 'architecture'
        });
    }
    analyzeArchitecture(knowledge) {
        const directories = knowledge.structure.directories;
        const files = Array.from(knowledge.fileIndex.keys());
        let architecture = `Architecture Analysis for ${knowledge.projectName}:\n\n`;
        // Analyze folder structure patterns
        if (directories.some(d => d.includes('src') || d.includes('lib'))) {
            architecture += '- Source code is organized in src/ or lib/ directory\n';
        }
        if (directories.some(d => d.includes('components'))) {
            architecture += '- Component-based architecture (likely React/Vue)\n';
        }
        if (directories.some(d => d.includes('services') || d.includes('api'))) {
            architecture += '- Service layer architecture\n';
        }
        if (directories.some(d => d.includes('models') || d.includes('entities'))) {
            architecture += '- Data model layer present\n';
        }
        if (directories.some(d => d.includes('controllers') || d.includes('routes'))) {
            architecture += '- MVC or similar pattern used\n';
        }
        if (directories.some(d => d.includes('middleware'))) {
            architecture += '- Middleware pattern implemented\n';
        }
        if (directories.some(d => d.includes('utils') || d.includes('helpers'))) {
            architecture += '- Utility/helper functions organized separately\n';
        }
        if (directories.some(d => d.includes('config') || d.includes('settings'))) {
            architecture += '- Configuration management implemented\n';
        }
        return architecture;
    }
    async generateTechnologyContext(knowledge) {
        const techStack = this.analyzeTechnologyStack(knowledge);
        await this.saveMemory(techStack, 'knowledge', `Technology Stack: ${knowledge.projectName}`, ['technology', 'stack', 'dependencies', ...knowledge.technologies.map(t => t.toLowerCase())], 7, {
            workspaceFolder: knowledge.workspacePath,
            type: 'technology'
        });
    }
    analyzeTechnologyStack(knowledge) {
        let analysis = `Technology Stack for ${knowledge.projectName}:\n\n`;
        analysis += `Primary Technologies:\n`;
        knowledge.technologies.forEach(tech => {
            analysis += `- ${tech}\n`;
        });
        // Analyze configuration files for more details
        const configFiles = knowledge.structure.configFiles;
        if (configFiles.some(f => f.includes('package.json'))) {
            analysis += `\nNode.js project with npm/yarn package management\n`;
        }
        if (configFiles.some(f => f.includes('requirements.txt'))) {
            analysis += `\nPython project with pip package management\n`;
        }
        if (configFiles.some(f => f.includes('Cargo.toml'))) {
            analysis += `\nRust project with Cargo package management\n`;
        }
        if (configFiles.some(f => f.includes('go.mod'))) {
            analysis += `\nGo project with Go modules\n`;
        }
        // Analyze build tools and frameworks
        if (configFiles.some(f => f.includes('webpack') || f.includes('vite') || f.includes('rollup'))) {
            analysis += `\nModern build tooling configured\n`;
        }
        if (configFiles.some(f => f.includes('jest') || f.includes('vitest') || f.includes('cypress'))) {
            analysis += `\nTesting framework configured\n`;
        }
        if (configFiles.some(f => f.includes('eslint') || f.includes('prettier'))) {
            analysis += `\nCode quality tools configured\n`;
        }
        return analysis;
    }
    async generatePatternsContext(knowledge) {
        const patterns = this.analyzeCodePatterns(knowledge);
        await this.saveMemory(patterns, 'knowledge', `Code Patterns: ${knowledge.projectName}`, ['patterns', 'conventions', 'style', knowledge.projectName.toLowerCase()], 6, {
            workspaceFolder: knowledge.workspacePath,
            type: 'patterns'
        });
    }
    analyzeCodePatterns(knowledge) {
        let patterns = `Code Patterns and Conventions for ${knowledge.projectName}:\n\n`;
        // Analyze file naming patterns
        const files = Array.from(knowledge.fileIndex.keys());
        if (files.some(f => f.includes('.component.') || f.includes('.service.') || f.includes('.module.'))) {
            patterns += '- Angular-style file naming conventions\n';
        }
        if (files.some(f => f.includes('.test.') || f.includes('.spec.'))) {
            patterns += '- Test files co-located with source files\n';
        }
        if (files.some(f => f.includes('index.') && files.some(g => g.includes(f.replace('index.', ''))))) {
            patterns += '- Index file pattern for module exports\n';
        }
        // Analyze directory patterns
        const directories = knowledge.structure.directories;
        if (directories.some(d => d.includes('__tests__') || d.includes('tests'))) {
            patterns += '- Dedicated test directories\n';
        }
        if (directories.some(d => d.includes('assets') || d.includes('static'))) {
            patterns += '- Static assets organized separately\n';
        }
        if (directories.some(d => d.includes('docs') || d.includes('documentation'))) {
            patterns += '- Documentation maintained in project\n';
        }
        return patterns;
    }
    async updateProjectContextFromConfig(uri) {
        const workspaceFolder = vscode.workspace.getWorkspaceFolder(uri);
        if (!workspaceFolder)
            return;
        // Re-analyze the project when config files change
        await this.generateProjectContextForFolder(workspaceFolder);
    }
    async getProjectContext(workspacePath) {
        const targetPath = workspacePath || vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        if (!targetPath)
            return '';
        // Get project-related memories
        const projectMemories = await this.loadMemory(`workspaceFolder:"${targetPath}"`, 'knowledge', 10);
        if (projectMemories.length === 0) {
            return 'No project context available. The workspace is being analyzed...';
        }
        // Combine all project context
        let context = `Current Project Context:\n\n`;
        for (const memory of projectMemories) {
            if (memory.title.includes('Project Context:')) {
                context += `${memory.content}\n\n`;
                break; // Use the main project context
            }
        }
        // Add specific contexts
        const architectureMemory = projectMemories.find(m => m.title.includes('Architecture:'));
        if (architectureMemory) {
            context += `${architectureMemory.content}\n\n`;
        }
        const techMemory = projectMemories.find(m => m.title.includes('Technology Stack:'));
        if (techMemory) {
            context += `${techMemory.content}\n\n`;
        }
        return context.trim();
    }
    async getRelevantContext(query) {
        // Get project context
        const projectContext = await this.getProjectContext();
        // Get relevant memories based on query
        const relevantMemories = await this.loadMemory(query, undefined, 5);
        let context = projectContext;
        if (relevantMemories.length > 0) {
            context += `\n\nRelevant Information:\n`;
            for (const memory of relevantMemories) {
                context += `- ${memory.title}: ${memory.content.substring(0, 200)}...\n`;
            }
        }
        return context;
    }
    dispose() {
        this.memories.clear();
        this.workspaceKnowledge.clear();
        this.outputChannel.dispose();
    }
}
exports.EnhancedMemoryManager = EnhancedMemoryManager;
//# sourceMappingURL=EnhancedMemoryManager.js.map