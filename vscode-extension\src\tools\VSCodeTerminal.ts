import * as vscode from 'vscode';

export interface TerminalCommand {
    command: string;
    args?: string[];
    cwd?: string;
    env?: Record<string, string>;
    shell?: string;
    timeout?: number;
    interactive?: boolean;
}

export interface TerminalResult {
    success: boolean;
    output?: string;
    error?: string;
    exitCode?: number;
    terminalId?: string;
}

export interface TerminalSession {
    id: string;
    name: string;
    terminal: vscode.Terminal;
    isActive: boolean;
    createdAt: Date;
    lastUsed: Date;
    commands: string[];
}

export class VSCodeTerminal {
    private outputChannel: vscode.OutputChannel;
    private terminals = new Map<string, TerminalSession>();
    private terminalCounter = 0;
    private activeTerminalId: string | null = null;

    constructor() {
        this.outputChannel = vscode.window.createOutputChannel('Agent Zero Terminal');
        this.setupTerminalEventHandlers();
    }

    private setupTerminalEventHandlers(): void {
        // Listen for terminal close events
        vscode.window.onDidCloseTerminal((terminal) => {
            for (const [id, session] of this.terminals) {
                if (session.terminal === terminal) {
                    this.terminals.delete(id);
                    if (this.activeTerminalId === id) {
                        this.activeTerminalId = null;
                    }
                    this.outputChannel.appendLine(`Terminal session ${id} closed`);
                    break;
                }
            }
        });

        // Listen for active terminal changes
        vscode.window.onDidChangeActiveTerminal((terminal) => {
            if (terminal) {
                for (const [id, session] of this.terminals) {
                    if (session.terminal === terminal) {
                        this.activeTerminalId = id;
                        session.lastUsed = new Date();
                        break;
                    }
                }
            }
        });
    }

    public async executeCommand(command: TerminalCommand): Promise<TerminalResult> {
        try {
            this.outputChannel.appendLine(`Executing terminal command: ${command.command}`);

            if (command.interactive) {
                return await this.executeInteractiveCommand(command);
            } else {
                return await this.executeNonInteractiveCommand(command);
            }

        } catch (error) {
            this.outputChannel.appendLine(`Terminal command failed: ${error}`);
            return {
                success: false,
                error: error.toString()
            };
        }
    }

    private async executeInteractiveCommand(command: TerminalCommand): Promise<TerminalResult> {
        const terminalId = await this.createOrGetTerminal(command);
        const session = this.terminals.get(terminalId);
        
        if (!session) {
            throw new Error('Failed to create terminal session');
        }

        // Build full command string
        let fullCommand = command.command;
        if (command.args && command.args.length > 0) {
            fullCommand += ' ' + command.args.join(' ');
        }

        // Send command to terminal
        session.terminal.sendText(fullCommand);
        session.terminal.show();
        session.commands.push(fullCommand);
        session.lastUsed = new Date();

        return {
            success: true,
            terminalId: terminalId,
            output: `Command sent to terminal: ${fullCommand}`
        };
    }

    private async executeNonInteractiveCommand(command: TerminalCommand): Promise<TerminalResult> {
        // For non-interactive commands, we'll create a temporary terminal
        // and try to capture output (limited in VS Code)
        
        const terminalOptions: vscode.TerminalOptions = {
            name: `Agent Zero Temp ${++this.terminalCounter}`,
            cwd: command.cwd,
            env: command.env,
            shellPath: command.shell
        };

        const terminal = vscode.window.createTerminal(terminalOptions);
        
        // Build full command string
        let fullCommand = command.command;
        if (command.args && command.args.length > 0) {
            fullCommand += ' ' + command.args.join(' ');
        }

        // Send command
        terminal.sendText(fullCommand);
        terminal.show();

        // Note: VS Code doesn't provide direct access to terminal output
        // This is a limitation of the VS Code API
        return {
            success: true,
            output: `Command executed in terminal: ${fullCommand}`,
            terminalId: `temp-${this.terminalCounter}`
        };
    }

    public async createTerminal(name?: string, options?: Partial<TerminalCommand>): Promise<string> {
        const terminalId = `terminal-${++this.terminalCounter}`;
        const terminalName = name || `Agent Zero ${this.terminalCounter}`;

        const terminalOptions: vscode.TerminalOptions = {
            name: terminalName,
            cwd: options?.cwd,
            env: options?.env,
            shellPath: options?.shell
        };

        const terminal = vscode.window.createTerminal(terminalOptions);

        const session: TerminalSession = {
            id: terminalId,
            name: terminalName,
            terminal: terminal,
            isActive: true,
            createdAt: new Date(),
            lastUsed: new Date(),
            commands: []
        };

        this.terminals.set(terminalId, session);
        this.activeTerminalId = terminalId;

        this.outputChannel.appendLine(`Created terminal session: ${terminalId}`);
        return terminalId;
    }

    private async createOrGetTerminal(command: TerminalCommand): Promise<string> {
        // Try to reuse existing terminal if no specific requirements
        if (!command.cwd && !command.env && !command.shell && this.activeTerminalId) {
            const session = this.terminals.get(this.activeTerminalId);
            if (session && session.isActive) {
                return this.activeTerminalId;
            }
        }

        // Create new terminal
        return await this.createTerminal(undefined, command);
    }

    public async switchToTerminal(terminalId: string): Promise<boolean> {
        const session = this.terminals.get(terminalId);
        if (!session) {
            return false;
        }

        session.terminal.show();
        this.activeTerminalId = terminalId;
        session.lastUsed = new Date();

        this.outputChannel.appendLine(`Switched to terminal: ${terminalId}`);
        return true;
    }

    public async closeTerminal(terminalId: string): Promise<boolean> {
        const session = this.terminals.get(terminalId);
        if (!session) {
            return false;
        }

        session.terminal.dispose();
        this.terminals.delete(terminalId);

        if (this.activeTerminalId === terminalId) {
            this.activeTerminalId = null;
        }

        this.outputChannel.appendLine(`Closed terminal: ${terminalId}`);
        return true;
    }

    public async sendTextToTerminal(terminalId: string, text: string): Promise<boolean> {
        const session = this.terminals.get(terminalId);
        if (!session) {
            return false;
        }

        session.terminal.sendText(text);
        session.commands.push(text);
        session.lastUsed = new Date();

        this.outputChannel.appendLine(`Sent text to terminal ${terminalId}: ${text}`);
        return true;
    }

    public getTerminalSessions(): TerminalSession[] {
        return Array.from(this.terminals.values());
    }

    public getActiveTerminal(): TerminalSession | null {
        if (!this.activeTerminalId) {
            return null;
        }
        return this.terminals.get(this.activeTerminalId) || null;
    }

    public async runScript(scriptPath: string, args?: string[], terminalId?: string): Promise<TerminalResult> {
        const targetTerminalId = terminalId || this.activeTerminalId;
        
        if (!targetTerminalId) {
            // Create new terminal for script execution
            const newTerminalId = await this.createTerminal('Script Execution');
            return await this.runScript(scriptPath, args, newTerminalId);
        }

        const session = this.terminals.get(targetTerminalId);
        if (!session) {
            throw new Error(`Terminal session ${targetTerminalId} not found`);
        }

        // Determine script execution command based on file extension
        const extension = scriptPath.split('.').pop()?.toLowerCase();
        let command: string;

        switch (extension) {
            case 'py':
                command = `python "${scriptPath}"`;
                break;
            case 'js':
                command = `node "${scriptPath}"`;
                break;
            case 'ts':
                command = `ts-node "${scriptPath}"`;
                break;
            case 'sh':
                command = `bash "${scriptPath}"`;
                break;
            case 'ps1':
                command = `powershell -File "${scriptPath}"`;
                break;
            case 'bat':
                command = `"${scriptPath}"`;
                break;
            default:
                command = `"${scriptPath}"`;
        }

        if (args && args.length > 0) {
            command += ' ' + args.join(' ');
        }

        session.terminal.sendText(command);
        session.terminal.show();
        session.commands.push(command);
        session.lastUsed = new Date();

        return {
            success: true,
            terminalId: targetTerminalId,
            output: `Script executed: ${command}`
        };
    }

    public async clearTerminal(terminalId?: string): Promise<boolean> {
        const targetTerminalId = terminalId || this.activeTerminalId;
        
        if (!targetTerminalId) {
            return false;
        }

        const session = this.terminals.get(targetTerminalId);
        if (!session) {
            return false;
        }

        // Send clear command (works on most shells)
        session.terminal.sendText('clear');
        session.lastUsed = new Date();

        return true;
    }

    public async killProcess(terminalId?: string): Promise<boolean> {
        const targetTerminalId = terminalId || this.activeTerminalId;
        
        if (!targetTerminalId) {
            return false;
        }

        const session = this.terminals.get(targetTerminalId);
        if (!session) {
            return false;
        }

        // Send Ctrl+C to interrupt current process
        session.terminal.sendText('\x03'); // Ctrl+C
        session.lastUsed = new Date();

        return true;
    }

    public getTerminalHistory(terminalId: string): string[] {
        const session = this.terminals.get(terminalId);
        return session ? [...session.commands] : [];
    }

    public async showTerminalManager(): Promise<void> {
        const panel = vscode.window.createWebviewPanel(
            'terminalManager',
            'Terminal Manager',
            vscode.ViewColumn.Two,
            {
                enableScripts: true,
                retainContextWhenHidden: true
            }
        );

        panel.webview.html = this.getTerminalManagerHtml();

        // Handle webview messages
        panel.webview.onDidReceiveMessage(async (message) => {
            switch (message.command) {
                case 'switchTerminal':
                    await this.switchToTerminal(message.terminalId);
                    break;
                case 'closeTerminal':
                    await this.closeTerminal(message.terminalId);
                    panel.webview.html = this.getTerminalManagerHtml(); // Refresh
                    break;
                case 'createTerminal':
                    await this.createTerminal(message.name);
                    panel.webview.html = this.getTerminalManagerHtml(); // Refresh
                    break;
                case 'clearTerminal':
                    await this.clearTerminal(message.terminalId);
                    break;
            }
        });
    }

    private getTerminalManagerHtml(): string {
        const sessions = this.getTerminalSessions();
        
        const sessionRows = sessions.map(session => `
            <tr class="${session.id === this.activeTerminalId ? 'active' : ''}">
                <td>${session.name}</td>
                <td>${session.id}</td>
                <td>${session.createdAt.toLocaleString()}</td>
                <td>${session.lastUsed.toLocaleString()}</td>
                <td>${session.commands.length}</td>
                <td>
                    <button onclick="switchTerminal('${session.id}')">Switch</button>
                    <button onclick="clearTerminal('${session.id}')">Clear</button>
                    <button onclick="closeTerminal('${session.id}')">Close</button>
                </td>
            </tr>
        `).join('');

        return `
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body { font-family: var(--vscode-font-family); padding: 20px; }
                table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                th, td { padding: 8px; text-align: left; border-bottom: 1px solid var(--vscode-panel-border); }
                th { background-color: var(--vscode-editor-inactiveSelectionBackground); }
                .active { background-color: var(--vscode-list-activeSelectionBackground); }
                button { margin: 2px; padding: 4px 8px; }
                .create-section { margin-bottom: 20px; }
                input { padding: 5px; margin: 5px; }
            </style>
        </head>
        <body>
            <h1>Terminal Manager</h1>
            
            <div class="create-section">
                <input type="text" id="terminalName" placeholder="Terminal name" />
                <button onclick="createTerminal()">Create New Terminal</button>
            </div>
            
            <table>
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>ID</th>
                        <th>Created</th>
                        <th>Last Used</th>
                        <th>Commands</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    ${sessionRows}
                </tbody>
            </table>
            
            <script>
                const vscode = acquireVsCodeApi();
                
                function switchTerminal(terminalId) {
                    vscode.postMessage({ command: 'switchTerminal', terminalId });
                }
                
                function closeTerminal(terminalId) {
                    vscode.postMessage({ command: 'closeTerminal', terminalId });
                }
                
                function clearTerminal(terminalId) {
                    vscode.postMessage({ command: 'clearTerminal', terminalId });
                }
                
                function createTerminal() {
                    const name = document.getElementById('terminalName').value || 'New Terminal';
                    vscode.postMessage({ command: 'createTerminal', name });
                }
            </script>
        </body>
        </html>`;
    }

    public dispose(): void {
        // Close all managed terminals
        for (const session of this.terminals.values()) {
            session.terminal.dispose();
        }
        this.terminals.clear();
        this.outputChannel.dispose();
    }
}
