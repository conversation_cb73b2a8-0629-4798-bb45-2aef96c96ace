import * as vscode from 'vscode';
import * as path from 'path';

export interface FileEditOperation {
    type: 'read' | 'write' | 'append' | 'delete' | 'create' | 'move' | 'copy';
    path: string;
    content?: string;
    newPath?: string;
    encoding?: string;
}

export interface FileEditResult {
    success: boolean;
    content?: string;
    error?: string;
    metadata?: {
        size: number;
        modified: Date;
        created: Date;
        isDirectory: boolean;
        permissions: string;
    };
}

export class VSCodeFileEditor {
    private outputChannel: vscode.OutputChannel;

    constructor() {
        this.outputChannel = vscode.window.createOutputChannel('Agent Zero File Editor');
    }

    public async executeOperation(operation: FileEditOperation): Promise<FileEditResult> {
        try {
            this.outputChannel.appendLine(`Executing file operation: ${operation.type} on ${operation.path}`);

            switch (operation.type) {
                case 'read':
                    return await this.readFile(operation.path);
                case 'write':
                    return await this.writeFile(operation.path, operation.content || '');
                case 'append':
                    return await this.appendToFile(operation.path, operation.content || '');
                case 'delete':
                    return await this.deleteFile(operation.path);
                case 'create':
                    return await this.createFile(operation.path, operation.content || '');
                case 'move':
                    return await this.moveFile(operation.path, operation.newPath || '');
                case 'copy':
                    return await this.copyFile(operation.path, operation.newPath || '');
                default:
                    throw new Error(`Unsupported operation: ${operation.type}`);
            }
        } catch (error) {
            this.outputChannel.appendLine(`File operation failed: ${error}`);
            return {
                success: false,
                error: String(error)
            };
        }
    }

    private async readFile(filePath: string): Promise<FileEditResult> {
        try {
            const uri = this.resolveUri(filePath);
            const fileData = await vscode.workspace.fs.readFile(uri);
            const content = Buffer.from(fileData).toString('utf8');
            const stat = await vscode.workspace.fs.stat(uri);

            return {
                success: true,
                content,
                metadata: {
                    size: stat.size,
                    modified: new Date(stat.mtime),
                    created: new Date(stat.ctime),
                    isDirectory: stat.type === vscode.FileType.Directory,
                    permissions: this.getPermissionsString(stat)
                }
            };
        } catch (error) {
            return {
                success: false,
                error: `Failed to read file: ${error}`
            };
        }
    }

    private async writeFile(filePath: string, content: string): Promise<FileEditResult> {
        try {
            const uri = this.resolveUri(filePath);
            const encoder = new TextEncoder();
            await vscode.workspace.fs.writeFile(uri, encoder.encode(content));

            // Open the file in editor if it's in the workspace
            if (this.isInWorkspace(filePath)) {
                const document = await vscode.workspace.openTextDocument(uri);
                await vscode.window.showTextDocument(document);
            }

            const stat = await vscode.workspace.fs.stat(uri);
            return {
                success: true,
                metadata: {
                    size: stat.size,
                    modified: new Date(stat.mtime),
                    created: new Date(stat.ctime),
                    isDirectory: false,
                    permissions: this.getPermissionsString(stat)
                }
            };
        } catch (error) {
            return {
                success: false,
                error: `Failed to write file: ${error}`
            };
        }
    }

    private async appendToFile(filePath: string, content: string): Promise<FileEditResult> {
        try {
            // Read existing content first
            const readResult = await this.readFile(filePath);
            if (!readResult.success) {
                // File doesn't exist, create it
                return await this.writeFile(filePath, content);
            }

            const newContent = (readResult.content || '') + content;
            return await this.writeFile(filePath, newContent);
        } catch (error) {
            return {
                success: false,
                error: `Failed to append to file: ${error}`
            };
        }
    }

    private async deleteFile(filePath: string): Promise<FileEditResult> {
        try {
            const uri = this.resolveUri(filePath);
            await vscode.workspace.fs.delete(uri, { recursive: true, useTrash: true });

            return {
                success: true
            };
        } catch (error) {
            return {
                success: false,
                error: `Failed to delete file: ${error}`
            };
        }
    }

    private async createFile(filePath: string, content: string = ''): Promise<FileEditResult> {
        try {
            const uri = this.resolveUri(filePath);
            
            // Create directory if it doesn't exist
            const dirUri = vscode.Uri.file(path.dirname(uri.fsPath));
            try {
                await vscode.workspace.fs.createDirectory(dirUri);
            } catch {
                // Directory might already exist
            }

            return await this.writeFile(filePath, content);
        } catch (error) {
            return {
                success: false,
                error: `Failed to create file: ${error}`
            };
        }
    }

    private async moveFile(sourcePath: string, targetPath: string): Promise<FileEditResult> {
        try {
            const sourceUri = this.resolveUri(sourcePath);
            const targetUri = this.resolveUri(targetPath);
            
            // Create target directory if it doesn't exist
            const targetDirUri = vscode.Uri.file(path.dirname(targetUri.fsPath));
            try {
                await vscode.workspace.fs.createDirectory(targetDirUri);
            } catch {
                // Directory might already exist
            }

            await vscode.workspace.fs.rename(sourceUri, targetUri, { overwrite: false });

            return {
                success: true
            };
        } catch (error) {
            return {
                success: false,
                error: `Failed to move file: ${error}`
            };
        }
    }

    private async copyFile(sourcePath: string, targetPath: string): Promise<FileEditResult> {
        try {
            const sourceUri = this.resolveUri(sourcePath);
            const targetUri = this.resolveUri(targetPath);
            
            // Create target directory if it doesn't exist
            const targetDirUri = vscode.Uri.file(path.dirname(targetUri.fsPath));
            try {
                await vscode.workspace.fs.createDirectory(targetDirUri);
            } catch {
                // Directory might already exist
            }

            await vscode.workspace.fs.copy(sourceUri, targetUri, { overwrite: false });

            return {
                success: true
            };
        } catch (error) {
            return {
                success: false,
                error: `Failed to copy file: ${error}`
            };
        }
    }

    public async listFiles(directoryPath: string, pattern?: string): Promise<FileEditResult> {
        try {
            const uri = this.resolveUri(directoryPath);
            const entries = await vscode.workspace.fs.readDirectory(uri);
            
            let files = entries.map(([name, type]) => ({
                name,
                path: path.join(directoryPath, name),
                isDirectory: type === vscode.FileType.Directory,
                isFile: type === vscode.FileType.File
            }));

            // Apply pattern filter if provided
            if (pattern) {
                const regex = new RegExp(pattern, 'i');
                files = files.filter(file => regex.test(file.name));
            }

            return {
                success: true,
                content: JSON.stringify(files, null, 2)
            };
        } catch (error) {
            return {
                success: false,
                error: `Failed to list files: ${error}`
            };
        }
    }

    public async searchInFiles(searchPath: string, query: string, filePattern?: string): Promise<FileEditResult> {
        try {
            const results: any[] = [];
            
            // Use VS Code's search functionality
            // Use VS Code's findFiles and then search within files
            const files = await vscode.workspace.findFiles(filePattern || '**/*', '**/node_modules/**', 1000);
            const searchResults: any[] = [];

            for (const file of files) {
                try {
                    const document = await vscode.workspace.openTextDocument(file);
                    const text = document.getText();
                    const lines = text.split('\n');

                    lines.forEach((line, index) => {
                        if (line.toLowerCase().includes(query.toLowerCase())) {
                            searchResults.push([file, [{
                                range: new vscode.Range(index, 0, index, line.length),
                                preview: { text: line, matches: [] }
                            }]]);
                        }
                    });
                } catch (error) {
                    // Skip files that can't be read
                }
            }

            for (const [uri, matches] of searchResults) {
                for (const match of matches) {
                    results.push({
                        file: uri.fsPath,
                        line: match.range.start.line + 1,
                        column: match.range.start.character + 1,
                        text: match.text,
                        preview: match.preview.text
                    });
                }
            }

            return {
                success: true,
                content: JSON.stringify(results, null, 2)
            };
        } catch (error) {
            return {
                success: false,
                error: `Failed to search in files: ${error}`
            };
        }
    }

    private resolveUri(filePath: string): vscode.Uri {
        if (path.isAbsolute(filePath)) {
            return vscode.Uri.file(filePath);
        }

        // Resolve relative to workspace
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (workspaceFolder) {
            return vscode.Uri.file(path.join(workspaceFolder.uri.fsPath, filePath));
        }

        // Fallback to absolute path
        return vscode.Uri.file(path.resolve(filePath));
    }

    private isInWorkspace(filePath: string): boolean {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            return false;
        }

        const resolvedPath = path.resolve(filePath);
        return resolvedPath.startsWith(workspaceFolder.uri.fsPath);
    }

    private getPermissionsString(stat: vscode.FileStat): string {
        // VS Code doesn't provide detailed permissions, so we'll return a basic string
        let permissions = '';
        permissions += stat.type === vscode.FileType.Directory ? 'd' : '-';
        permissions += 'rwx'; // Assume read/write/execute for owner
        permissions += 'r--'; // Assume read-only for group
        permissions += 'r--'; // Assume read-only for others
        return permissions;
    }

    public dispose(): void {
        this.outputChannel.dispose();
    }
}
