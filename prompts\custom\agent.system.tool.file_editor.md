# File Editor Tool

**Tool name:** `file_editor`

This tool allows you to quickly edit files directly without opening external editors like VS Code. It's much faster than using the `code` command.

## Usage:

```json
{
    "tool_name": "file_editor",
    "tool_args": {
        "action": "read|write|append|replace|create",
        "file_path": "path/to/file",
        "content": "file content (for write/append/create)",
        "old_text": "text to replace (for replace action)",
        "new_text": "replacement text (for replace action)"
    }
}
```

## Actions:

### 1. **read** - قراءة محتوى الملف
```json
{
    "tool_name": "file_editor",
    "tool_args": {
        "action": "read",
        "file_path": "mobile_app/lib/main.dart"
    }
}
```

### 2. **write** - كتابة محتوى جديد (استبدال المحتوى الحالي)
```json
{
    "tool_name": "file_editor",
    "tool_args": {
        "action": "write",
        "file_path": "mobile_app/lib/main.dart",
        "content": "import 'package:flutter/material.dart';\n\nvoid main() {\n  runApp(MyApp());\n}"
    }
}
```

### 3. **append** - إضافة محتوى لنهاية الملف
```json
{
    "tool_name": "file_editor",
    "tool_args": {
        "action": "append",
        "file_path": "mobile_app/lib/main.dart",
        "content": "\n\n// Additional code here"
    }
}
```

### 4. **replace** - استبدال نص محدد في الملف
```json
{
    "tool_name": "file_editor",
    "tool_args": {
        "action": "replace",
        "file_path": "mobile_app/lib/main.dart",
        "old_text": "MyApp",
        "new_text": "TaskManagerApp"
    }
}
```

### 5. **create** - إنشاء ملف جديد
```json
{
    "tool_name": "file_editor",
    "tool_args": {
        "action": "create",
        "file_path": "mobile_app/lib/screens/home_screen.dart",
        "content": "import 'package:flutter/material.dart';\n\nclass HomeScreen extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold();\n  }\n}"
    }
}
```

## Benefits:
- **Much faster** than opening VS Code with `code` command
- **Direct file manipulation** without external dependencies
- **Immediate feedback** on success/failure
- **Perfect for quick edits** and file creation
- **Works with any file type** (Dart, Python, HTML, CSS, etc.)

## When to use:
- Creating new files quickly
- Making small edits to existing files
- Reading file contents
- Replacing specific text in files
- Adding content to files

**Use this tool instead of `code` command for faster development!**
