import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import { ModelManager } from './ModelManager';

export interface EnhancedMemoryItem {
    id: string;
    type: 'fact' | 'code' | 'solution' | 'instruction' | 'knowledge' | 'conversation' | 'file-context';
    content: string;
    title: string;
    tags: string[];
    timestamp: Date;
    importance: number; // 1-10 scale
    embedding?: number[];
    metadata: {
        source?: string;
        filePath?: string;
        language?: string;
        lineNumbers?: { start: number; end: number };
        relatedFiles?: string[];
        agentId?: string;
        sessionId?: string;
        workspaceFolder?: string;
    };
    relationships: {
        parentId?: string;
        childIds: string[];
        relatedIds: string[];
    };
}

export interface WorkspaceKnowledge {
    id: string;
    workspacePath: string;
    projectName: string;
    description: string;
    technologies: string[];
    structure: {
        directories: string[];
        importantFiles: string[];
        configFiles: string[];
    };
    patterns: {
        codingStyle: string;
        architecture: string;
        conventions: string[];
    };
    lastUpdated: Date;
    fileIndex: Map<string, FileKnowledge>;
}

export interface FileKnowledge {
    path: string;
    type: 'source' | 'config' | 'documentation' | 'test' | 'asset';
    language: string;
    size: number;
    lastModified: Date;
    summary: string;
    functions: string[];
    classes: string[];
    imports: string[];
    exports: string[];
    dependencies: string[];
    complexity: number;
    embedding?: number[];
}

export class EnhancedMemoryManager implements vscode.TreeDataProvider<EnhancedMemoryItem> {
    private _onDidChangeTreeData: vscode.EventEmitter<EnhancedMemoryItem | undefined | null | void> = new vscode.EventEmitter<EnhancedMemoryItem | undefined | null | void>();
    readonly onDidChangeTreeData: vscode.Event<EnhancedMemoryItem | undefined | null | void> = this._onDidChangeTreeData.event;

    private memories: Map<string, EnhancedMemoryItem> = new Map();
    private workspaceKnowledge: Map<string, WorkspaceKnowledge> = new Map();
    private memoryCounter = 0;
    private outputChannel: vscode.OutputChannel;

    constructor(
        private context: vscode.ExtensionContext,
        private modelManager: ModelManager
    ) {
        this.outputChannel = vscode.window.createOutputChannel('Agent Zero Enhanced Memory');
        this.initializeMemorySystem();
        this.setupWorkspaceWatchers();
    }

    private async initializeMemorySystem(): Promise<void> {
        await this.loadMemoryFromStorage();
        await this.loadWorkspaceKnowledge();
        await this.indexCurrentWorkspace();
    }

    private setupWorkspaceWatchers(): void {
        // Watch for file changes
        const fileWatcher = vscode.workspace.createFileSystemWatcher('**/*');
        
        fileWatcher.onDidCreate(async (uri) => {
            await this.handleFileCreated(uri);
        });

        fileWatcher.onDidChange(async (uri) => {
            await this.handleFileChanged(uri);
        });

        fileWatcher.onDidDelete(async (uri) => {
            await this.handleFileDeleted(uri);
        });

        this.context.subscriptions.push(fileWatcher);

        // Watch for workspace folder changes
        vscode.workspace.onDidChangeWorkspaceFolders(async (event) => {
            for (const folder of event.added) {
                await this.indexWorkspaceFolder(folder);
            }
            for (const folder of event.removed) {
                this.removeWorkspaceKnowledge(folder.uri.fsPath);
            }
        });
    }

    public async saveMemory(
        content: string, 
        type: EnhancedMemoryItem['type'], 
        title?: string,
        tags: string[] = [], 
        importance: number = 5,
        metadata: Partial<EnhancedMemoryItem['metadata']> = {}
    ): Promise<string> {
        const memory: EnhancedMemoryItem = {
            id: `memory-${++this.memoryCounter}`,
            type,
            content,
            title: title || this.generateTitle(content, type),
            tags,
            timestamp: new Date(),
            importance,
            metadata: {
                workspaceFolder: vscode.workspace.workspaceFolders?.[0]?.uri.fsPath,
                ...metadata
            },
            relationships: {
                childIds: [],
                relatedIds: []
            }
        };

        // Generate embedding for semantic search
        if (this.modelManager) {
            try {
                memory.embedding = await this.modelManager.generateEmbeddings(content);
            } catch (error) {
                this.outputChannel.appendLine(`Failed to generate embedding: ${error}`);
            }
        }

        this.memories.set(memory.id, memory);
        await this.saveMemoryToStorage();
        this.refresh();

        this.outputChannel.appendLine(`Saved memory: ${memory.title}`);
        return memory.id;
    }

    public async loadMemory(query: string, type?: EnhancedMemoryItem['type'], limit: number = 10): Promise<EnhancedMemoryItem[]> {
        let results: EnhancedMemoryItem[] = [];

        // First try semantic search if embeddings are available
        if (this.modelManager) {
            try {
                const queryEmbedding = await this.modelManager.generateEmbeddings(query);
                results = await this.semanticSearch(queryEmbedding, type, limit);
            } catch (error) {
                this.outputChannel.appendLine(`Semantic search failed: ${error}`);
            }
        }

        // Fallback to text search if semantic search fails or returns few results
        if (results.length < limit / 2) {
            const textResults = this.textSearch(query, type, limit - results.length);
            results = [...results, ...textResults];
        }

        // Remove duplicates and sort by relevance
        const uniqueResults = Array.from(new Map(results.map(r => [r.id, r])).values());
        return uniqueResults.slice(0, limit);
    }

    private async semanticSearch(queryEmbedding: number[], type?: EnhancedMemoryItem['type'], limit: number = 10): Promise<EnhancedMemoryItem[]> {
        const candidates: Array<{ memory: EnhancedMemoryItem; similarity: number }> = [];

        for (const memory of this.memories.values()) {
            if (type && memory.type !== type) continue;
            if (!memory.embedding) continue;

            const similarity = this.cosineSimilarity(queryEmbedding, memory.embedding);
            candidates.push({ memory, similarity });
        }

        return candidates
            .sort((a, b) => b.similarity - a.similarity)
            .slice(0, limit)
            .map(c => c.memory);
    }

    private textSearch(query: string, type?: EnhancedMemoryItem['type'], limit: number = 10): Promise<EnhancedMemoryItem[]> {
        const lowerQuery = query.toLowerCase();
        const results: Array<{ memory: EnhancedMemoryItem; score: number }> = [];

        for (const memory of this.memories.values()) {
            if (type && memory.type !== type) continue;

            let score = 0;

            // Title match (highest weight)
            if (memory.title.toLowerCase().includes(lowerQuery)) {
                score += 10;
            }

            // Content match
            if (memory.content.toLowerCase().includes(lowerQuery)) {
                score += 5;
            }

            // Tag match
            for (const tag of memory.tags) {
                if (tag.toLowerCase().includes(lowerQuery)) {
                    score += 3;
                }
            }

            // Importance boost
            score += memory.importance / 10;

            if (score > 0) {
                results.push({ memory, score });
            }
        }

        return Promise.resolve(
            results
                .sort((a, b) => b.score - a.score)
                .slice(0, limit)
                .map(r => r.memory)
        );
    }

    public async indexCurrentWorkspace(): Promise<void> {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders) return;

        for (const folder of workspaceFolders) {
            await this.indexWorkspaceFolder(folder);
        }
    }

    private async indexWorkspaceFolder(folder: vscode.WorkspaceFolder): Promise<void> {
        this.outputChannel.appendLine(`Indexing workspace: ${folder.name}`);

        const knowledge: WorkspaceKnowledge = {
            id: folder.uri.fsPath,
            workspacePath: folder.uri.fsPath,
            projectName: folder.name,
            description: '',
            technologies: [],
            structure: {
                directories: [],
                importantFiles: [],
                configFiles: []
            },
            patterns: {
                codingStyle: '',
                architecture: '',
                conventions: []
            },
            lastUpdated: new Date(),
            fileIndex: new Map()
        };

        // Analyze workspace structure
        await this.analyzeWorkspaceStructure(folder.uri, knowledge);
        
        // Detect technologies and patterns
        await this.detectTechnologies(knowledge);
        
        this.workspaceKnowledge.set(folder.uri.fsPath, knowledge);
        await this.saveWorkspaceKnowledge();

        this.outputChannel.appendLine(`Indexed ${knowledge.fileIndex.size} files in ${folder.name}`);
    }

    private async analyzeWorkspaceStructure(uri: vscode.Uri, knowledge: WorkspaceKnowledge): Promise<void> {
        try {
            const entries = await vscode.workspace.fs.readDirectory(uri);
            
            for (const [name, type] of entries) {
                if (name.startsWith('.') && !this.isImportantDotFile(name)) continue;

                const fullPath = path.join(uri.fsPath, name);
                const relativePath = path.relative(knowledge.workspacePath, fullPath);

                if (type === vscode.FileType.Directory) {
                    knowledge.structure.directories.push(relativePath);
                    
                    // Recursively analyze subdirectories (with depth limit)
                    if (this.shouldAnalyzeDirectory(name) && relativePath.split(path.sep).length < 5) {
                        await this.analyzeWorkspaceStructure(vscode.Uri.file(fullPath), knowledge);
                    }
                } else if (type === vscode.FileType.File) {
                    await this.analyzeFile(fullPath, knowledge);
                }
            }
        } catch (error) {
            this.outputChannel.appendLine(`Error analyzing ${uri.fsPath}: ${error}`);
        }
    }

    private async analyzeFile(filePath: string, knowledge: WorkspaceKnowledge): Promise<void> {
        try {
            const stat = await vscode.workspace.fs.stat(vscode.Uri.file(filePath));
            const relativePath = path.relative(knowledge.workspacePath, filePath);
            const ext = path.extname(filePath);
            
            const fileKnowledge: FileKnowledge = {
                path: relativePath,
                type: this.getFileType(filePath),
                language: this.getLanguageFromExtension(ext),
                size: stat.size,
                lastModified: new Date(stat.mtime),
                summary: '',
                functions: [],
                classes: [],
                imports: [],
                exports: [],
                dependencies: [],
                complexity: 0
            };

            // Analyze file content for source files
            if (fileKnowledge.type === 'source' && stat.size < 1024 * 1024) { // Max 1MB
                await this.analyzeSourceFile(filePath, fileKnowledge);
            }

            knowledge.fileIndex.set(relativePath, fileKnowledge);

            // Categorize important files
            if (this.isConfigFile(filePath)) {
                knowledge.structure.configFiles.push(relativePath);
            }
            if (this.isImportantFile(filePath)) {
                knowledge.structure.importantFiles.push(relativePath);
            }

        } catch (error) {
            this.outputChannel.appendLine(`Error analyzing file ${filePath}: ${error}`);
        }
    }

    private async analyzeSourceFile(filePath: string, fileKnowledge: FileKnowledge): Promise<void> {
        try {
            const document = await vscode.workspace.openTextDocument(filePath);
            const content = document.getText();

            // Basic code analysis
            fileKnowledge.summary = this.generateFileSummary(content, fileKnowledge.language);
            fileKnowledge.functions = this.extractFunctions(content, fileKnowledge.language);
            fileKnowledge.classes = this.extractClasses(content, fileKnowledge.language);
            fileKnowledge.imports = this.extractImports(content, fileKnowledge.language);
            fileKnowledge.exports = this.extractExports(content, fileKnowledge.language);
            fileKnowledge.complexity = this.calculateComplexity(content);

            // Generate embedding for semantic search
            if (this.modelManager && content.length < 10000) {
                try {
                    fileKnowledge.embedding = await this.modelManager.generateEmbeddings(
                        `${fileKnowledge.summary}\n${fileKnowledge.functions.join(' ')}\n${fileKnowledge.classes.join(' ')}`
                    );
                } catch (error) {
                    this.outputChannel.appendLine(`Failed to generate file embedding: ${error}`);
                }
            }

        } catch (error) {
            this.outputChannel.appendLine(`Error analyzing source file ${filePath}: ${error}`);
        }
    }

    private generateFileSummary(content: string, language: string): string {
        const lines = content.split('\n');
        const firstComment = this.extractFirstComment(lines, language);
        if (firstComment) return firstComment;

        // Generate basic summary based on content
        const summary = `${language} file with ${lines.length} lines`;
        return summary;
    }

    private extractFirstComment(lines: string[], language: string): string {
        const commentPatterns: Record<string, RegExp[]> = {
            'javascript': [/^\s*\/\*\*(.*?)\*\//s, /^\s*\/\/(.*)/],
            'typescript': [/^\s*\/\*\*(.*?)\*\//s, /^\s*\/\/(.*)/],
            'python': [/^\s*"""(.*?)"""/s, /^\s*#(.*)/],
            'java': [/^\s*\/\*\*(.*?)\*\//s, /^\s*\/\/(.*)/],
            'cpp': [/^\s*\/\*\*(.*?)\*\//s, /^\s*\/\/(.*)/],
            'c': [/^\s*\/\*\*(.*?)\*\//s, /^\s*\/\/(.*)/]
        };

        const patterns = commentPatterns[language] || commentPatterns['javascript'];
        const content = lines.slice(0, 10).join('\n');

        for (const pattern of patterns) {
            const match = content.match(pattern);
            if (match) {
                return match[1].trim().substring(0, 200);
            }
        }

        return '';
    }

    private extractFunctions(content: string, language: string): string[] {
        const functionPatterns: Record<string, RegExp> = {
            'javascript': /function\s+(\w+)|(\w+)\s*=\s*function|(\w+)\s*=\s*\(.*?\)\s*=>/g,
            'typescript': /function\s+(\w+)|(\w+)\s*=\s*function|(\w+)\s*=\s*\(.*?\)\s*=>|(\w+)\s*\(.*?\):\s*\w+/g,
            'python': /def\s+(\w+)/g,
            'java': /(?:public|private|protected)?\s*(?:static)?\s*\w+\s+(\w+)\s*\(/g,
            'cpp': /\w+\s+(\w+)\s*\(/g,
            'c': /\w+\s+(\w+)\s*\(/g
        };

        const pattern = functionPatterns[language];
        if (!pattern) return [];

        const functions: string[] = [];
        let match;
        while ((match = pattern.exec(content)) !== null) {
            const functionName = match[1] || match[2] || match[3] || match[4];
            if (functionName && !functions.includes(functionName)) {
                functions.push(functionName);
            }
        }

        return functions;
    }

    private extractClasses(content: string, language: string): string[] {
        const classPatterns: Record<string, RegExp> = {
            'javascript': /class\s+(\w+)/g,
            'typescript': /class\s+(\w+)/g,
            'python': /class\s+(\w+)/g,
            'java': /(?:public|private|protected)?\s*class\s+(\w+)/g,
            'cpp': /class\s+(\w+)/g,
            'c': /struct\s+(\w+)/g
        };

        const pattern = classPatterns[language];
        if (!pattern) return [];

        const classes: string[] = [];
        let match;
        while ((match = pattern.exec(content)) !== null) {
            if (!classes.includes(match[1])) {
                classes.push(match[1]);
            }
        }

        return classes;
    }

    private extractImports(content: string, language: string): string[] {
        const importPatterns: Record<string, RegExp> = {
            'javascript': /import.*?from\s+['"]([^'"]+)['"]/g,
            'typescript': /import.*?from\s+['"]([^'"]+)['"]/g,
            'python': /(?:from\s+(\w+)|import\s+(\w+))/g,
            'java': /import\s+([\w.]+)/g,
            'cpp': /#include\s*[<"]([^>"]+)[>"]/g,
            'c': /#include\s*[<"]([^>"]+)[>"]/g
        };

        const pattern = importPatterns[language];
        if (!pattern) return [];

        const imports: string[] = [];
        let match;
        while ((match = pattern.exec(content)) !== null) {
            const importName = match[1] || match[2];
            if (importName && !imports.includes(importName)) {
                imports.push(importName);
            }
        }

        return imports;
    }

    private extractExports(content: string, language: string): string[] {
        const exportPatterns: Record<string, RegExp> = {
            'javascript': /export\s+(?:default\s+)?(?:class|function|const|let|var)?\s*(\w+)/g,
            'typescript': /export\s+(?:default\s+)?(?:class|function|const|let|var|interface|type)?\s*(\w+)/g
        };

        const pattern = exportPatterns[language];
        if (!pattern) return [];

        const exports: string[] = [];
        let match;
        while ((match = pattern.exec(content)) !== null) {
            if (!exports.includes(match[1])) {
                exports.push(match[1]);
            }
        }

        return exports;
    }

    private calculateComplexity(content: string): number {
        // Simple complexity calculation based on control structures
        const complexityPatterns = [
            /\bif\b/g,
            /\belse\b/g,
            /\bfor\b/g,
            /\bwhile\b/g,
            /\bswitch\b/g,
            /\bcatch\b/g,
            /\btry\b/g
        ];

        let complexity = 1; // Base complexity
        for (const pattern of complexityPatterns) {
            const matches = content.match(pattern);
            if (matches) {
                complexity += matches.length;
            }
        }

        return complexity;
    }

    private async detectTechnologies(knowledge: WorkspaceKnowledge): Promise<void> {
        const technologies = new Set<string>();

        // Check package files
        for (const [filePath, fileInfo] of knowledge.fileIndex) {
            if (filePath.includes('package.json')) {
                technologies.add('Node.js');
                technologies.add('JavaScript');
            } else if (filePath.includes('requirements.txt') || filePath.includes('setup.py')) {
                technologies.add('Python');
            } else if (filePath.includes('Cargo.toml')) {
                technologies.add('Rust');
            } else if (filePath.includes('go.mod')) {
                technologies.add('Go');
            } else if (filePath.includes('pom.xml') || filePath.includes('build.gradle')) {
                technologies.add('Java');
            }

            // Check by file extension
            switch (fileInfo.language) {
                case 'typescript':
                    technologies.add('TypeScript');
                    break;
                case 'python':
                    technologies.add('Python');
                    break;
                case 'java':
                    technologies.add('Java');
                    break;
                case 'cpp':
                    technologies.add('C++');
                    break;
                case 'rust':
                    technologies.add('Rust');
                    break;
                case 'go':
                    technologies.add('Go');
                    break;
            }
        }

        knowledge.technologies = Array.from(technologies);
    }

    // Helper methods
    private isImportantDotFile(name: string): boolean {
        const important = ['.gitignore', '.env', '.vscode', '.github', '.eslintrc', '.prettierrc'];
        return important.some(pattern => name.startsWith(pattern));
    }

    private shouldAnalyzeDirectory(name: string): boolean {
        const skip = ['node_modules', '.git', 'dist', 'build', '__pycache__', '.pytest_cache'];
        return !skip.includes(name);
    }

    private getFileType(filePath: string): FileKnowledge['type'] {
        const name = path.basename(filePath).toLowerCase();
        const ext = path.extname(filePath).toLowerCase();

        if (name.includes('test') || name.includes('spec')) return 'test';
        if (name.includes('config') || name.includes('setting')) return 'config';
        if (name.includes('readme') || ext === '.md') return 'documentation';
        if (['.js', '.ts', '.py', '.java', '.cpp', '.c', '.rs', '.go'].includes(ext)) return 'source';
        
        return 'asset';
    }

    private getLanguageFromExtension(ext: string): string {
        const mapping: Record<string, string> = {
            '.js': 'javascript',
            '.ts': 'typescript',
            '.py': 'python',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c',
            '.rs': 'rust',
            '.go': 'go',
            '.php': 'php',
            '.rb': 'ruby',
            '.cs': 'csharp',
            '.swift': 'swift',
            '.kt': 'kotlin'
        };
        return mapping[ext] || 'text';
    }

    private isConfigFile(filePath: string): boolean {
        const configFiles = [
            'package.json', 'tsconfig.json', 'webpack.config.js', '.eslintrc',
            'requirements.txt', 'setup.py', 'Cargo.toml', 'go.mod', 'pom.xml'
        ];
        const name = path.basename(filePath);
        return configFiles.some(config => name.includes(config));
    }

    private isImportantFile(filePath: string): boolean {
        const important = ['README', 'LICENSE', 'CHANGELOG', 'index', 'main', 'app'];
        const name = path.basename(filePath, path.extname(filePath)).toLowerCase();
        return important.some(pattern => name.includes(pattern));
    }

    private generateTitle(content: string, type: EnhancedMemoryItem['type']): string {
        const firstLine = content.split('\n')[0].trim();
        const maxLength = 50;
        
        if (firstLine.length <= maxLength) {
            return firstLine;
        }
        
        return firstLine.substring(0, maxLength - 3) + '...';
    }

    private cosineSimilarity(a: number[], b: number[]): number {
        if (a.length !== b.length) return 0;

        let dotProduct = 0;
        let normA = 0;
        let normB = 0;

        for (let i = 0; i < a.length; i++) {
            dotProduct += a[i] * b[i];
            normA += a[i] * a[i];
            normB += b[i] * b[i];
        }

        return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
    }

    // Event handlers
    private async handleFileCreated(uri: vscode.Uri): Promise<void> {
        const workspaceFolder = vscode.workspace.getWorkspaceFolder(uri);
        if (!workspaceFolder) return;

        const knowledge = this.workspaceKnowledge.get(workspaceFolder.uri.fsPath);
        if (knowledge) {
            await this.analyzeFile(uri.fsPath, knowledge);
            await this.saveWorkspaceKnowledge();
        }
    }

    private async handleFileChanged(uri: vscode.Uri): Promise<void> {
        // Re-analyze changed files
        await this.handleFileCreated(uri);
    }

    private async handleFileDeleted(uri: vscode.Uri): Promise<void> {
        const workspaceFolder = vscode.workspace.getWorkspaceFolder(uri);
        if (!workspaceFolder) return;

        const knowledge = this.workspaceKnowledge.get(workspaceFolder.uri.fsPath);
        if (knowledge) {
            const relativePath = path.relative(workspaceFolder.uri.fsPath, uri.fsPath);
            knowledge.fileIndex.delete(relativePath);
            await this.saveWorkspaceKnowledge();
        }
    }

    private removeWorkspaceKnowledge(workspacePath: string): void {
        this.workspaceKnowledge.delete(workspacePath);
    }

    // Storage methods
    private async loadMemoryFromStorage(): Promise<void> {
        try {
            const memoryData = this.context.globalState.get<Record<string, EnhancedMemoryItem>>('agent-zero.enhanced-memories', {});
            for (const [id, memory] of Object.entries(memoryData)) {
                memory.timestamp = new Date(memory.timestamp);
                this.memories.set(id, memory);
            }
            this.memoryCounter = this.memories.size;
        } catch (error) {
            this.outputChannel.appendLine(`Failed to load memory: ${error}`);
        }
    }

    private async saveMemoryToStorage(): Promise<void> {
        try {
            const memoryData: Record<string, EnhancedMemoryItem> = {};
            for (const [id, memory] of this.memories.entries()) {
                memoryData[id] = memory;
            }
            await this.context.globalState.update('agent-zero.enhanced-memories', memoryData);
        } catch (error) {
            this.outputChannel.appendLine(`Failed to save memory: ${error}`);
        }
    }

    private async loadWorkspaceKnowledge(): Promise<void> {
        try {
            const workspaceData = this.context.workspaceState.get<Record<string, any>>('agent-zero.workspace-knowledge', {});
            for (const [path, data] of Object.entries(workspaceData)) {
                const knowledge: WorkspaceKnowledge = {
                    ...data,
                    lastUpdated: new Date(data.lastUpdated),
                    fileIndex: new Map(Object.entries(data.fileIndex || {}))
                };
                this.workspaceKnowledge.set(path, knowledge);
            }
        } catch (error) {
            this.outputChannel.appendLine(`Failed to load workspace knowledge: ${error}`);
        }
    }

    private async saveWorkspaceKnowledge(): Promise<void> {
        try {
            const workspaceData: Record<string, any> = {};
            for (const [path, knowledge] of this.workspaceKnowledge.entries()) {
                workspaceData[path] = {
                    ...knowledge,
                    fileIndex: Object.fromEntries(knowledge.fileIndex)
                };
            }
            await this.context.workspaceState.update('agent-zero.workspace-knowledge', workspaceData);
        } catch (error) {
            this.outputChannel.appendLine(`Failed to save workspace knowledge: ${error}`);
        }
    }

    // TreeDataProvider implementation
    getTreeItem(element: EnhancedMemoryItem): vscode.TreeItem {
        const item = new vscode.TreeItem(element.title, vscode.TreeItemCollapsibleState.None);
        item.description = `${element.type} - ${element.importance}/10`;
        item.tooltip = `${element.content.substring(0, 200)}...\nTags: ${element.tags.join(', ')}\nCreated: ${element.timestamp.toLocaleString()}`;
        item.contextValue = 'enhancedMemoryItem';
        
        // Set icon based on type
        const iconMap: Record<string, string> = {
            'fact': 'info',
            'code': 'code',
            'solution': 'lightbulb',
            'instruction': 'list-ordered',
            'knowledge': 'book',
            'conversation': 'comment',
            'file-context': 'file'
        };
        item.iconPath = new vscode.ThemeIcon(iconMap[element.type] || 'circle');

        return item;
    }

    getChildren(element?: EnhancedMemoryItem): Thenable<EnhancedMemoryItem[]> {
        if (!element) {
            return Promise.resolve(Array.from(this.memories.values()));
        }
        return Promise.resolve([]);
    }

    refresh(): void {
        this._onDidChangeTreeData.fire();
    }

    public dispose(): void {
        this.memories.clear();
        this.workspaceKnowledge.clear();
        this.outputChannel.dispose();
    }
}
