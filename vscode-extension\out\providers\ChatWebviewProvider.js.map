{"version": 3, "file": "ChatWebviewProvider.js", "sourceRoot": "", "sources": ["../../src/providers/ChatWebviewProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAGjC,MAAa,mBAAmB;IAI5B,YACqB,OAAgC,EAChC,YAA0B;QAD1B,YAAO,GAAP,OAAO,CAAyB;QAChC,iBAAY,GAAZ,YAAY,CAAc;IAC5C,CAAC;IAEG,kBAAkB,CACrB,WAA+B,EAC/B,OAAyC,EACzC,MAAgC;QAEhC,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC;QAEzB,WAAW,CAAC,OAAO,CAAC,OAAO,GAAG;YAC1B,aAAa,EAAE,IAAI;YACnB,kBAAkB,EAAE;gBAChB,IAAI,CAAC,OAAO,CAAC,YAAY;aAC5B;SACJ,CAAC;QAEF,WAAW,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAEvE,mCAAmC;QACnC,WAAW,CAAC,OAAO,CAAC,mBAAmB,CACnC,KAAK,EAAE,IAAI,EAAE,EAAE;YACX,QAAQ,IAAI,CAAC,IAAI,EAAE;gBACf,KAAK,aAAa;oBACd,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBAC3C,MAAM;gBACV,KAAK,WAAW;oBACZ,IAAI,CAAC,SAAS,EAAE,CAAC;oBACjB,MAAM;gBACV,KAAK,YAAY;oBACb,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;oBACxB,MAAM;gBACV,KAAK,YAAY;oBACb,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;oBACxB,MAAM;gBACV,KAAK,aAAa;oBACd,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBACrC,MAAM;gBACV,KAAK,oBAAoB;oBACrB,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;oBACtC,MAAM;gBACV,KAAK,iBAAiB;oBAClB,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBAChD,MAAM;aACb;QACL,CAAC,EACD,SAAS,EACT,IAAI,CAAC,OAAO,CAAC,aAAa,CAC7B,CAAC;IACN,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,OAAe;QAC3C,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACb,OAAO;SACV;QAED,IAAI;YACA,gCAAgC;YAChC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC3B,IAAI,EAAE,YAAY;gBAClB,OAAO,EAAE;oBACL,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;oBACzB,OAAO,EAAE,OAAO;oBAChB,MAAM,EAAE,MAAM;oBACd,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACtC;aACJ,CAAC,CAAC;YAEH,wBAAwB;YACxB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC3B,IAAI,EAAE,WAAW;gBACjB,QAAQ,EAAE,IAAI;aACjB,CAAC,CAAC;YAEH,wBAAwB;YACxB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAE9D,wBAAwB;YACxB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC3B,IAAI,EAAE,WAAW;gBACjB,QAAQ,EAAE,KAAK;aAClB,CAAC,CAAC;YAEH,sBAAsB;YACtB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC3B,IAAI,EAAE,YAAY;gBAClB,OAAO,EAAE;oBACL,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE;oBAC/B,OAAO,EAAE,QAAQ;oBACjB,MAAM,EAAE,OAAO;oBACf,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACtC;aACJ,CAAC,CAAC;SAEN;QAAC,OAAO,KAAK,EAAE;YACZ,wBAAwB;YACxB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC3B,IAAI,EAAE,WAAW;gBACjB,QAAQ,EAAE,KAAK;aAClB,CAAC,CAAC;YAEH,qBAAqB;YACrB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC3B,IAAI,EAAE,YAAY;gBAClB,OAAO,EAAE;oBACL,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE;oBAC/B,OAAO,EAAE,UAAU,KAAK,EAAE;oBAC1B,MAAM,EAAE,QAAQ;oBAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACtC;aACJ,CAAC,CAAC;SACN;IACL,CAAC;IAEO,SAAS;QACb,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC3B,IAAI,EAAE,eAAe;aACxB,CAAC,CAAC;SACN;IACL,CAAC;IAEO,KAAK,CAAC,UAAU;QACpB,4CAA4C;QAC5C,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,wCAAwC,CAAC,CAAC;IACnF,CAAC;IAEO,KAAK,CAAC,UAAU;QACpB,4CAA4C;QAC5C,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,wCAAwC,CAAC,CAAC;IACnF,CAAC;IAEO,iBAAiB,CAAC,OAAe;QACrC,IAAI,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE;YAC3C,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAClD,IAAI,IAAI,CAAC,KAAK,IAAI,KAAK,EAAE;gBACrB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;oBAC3B,IAAI,EAAE,cAAc;oBACpB,KAAK,EAAE;wBACH,EAAE,EAAE,KAAK,CAAC,EAAE;wBACZ,IAAI,EAAE,KAAK,CAAC,IAAI;wBAChB,IAAI,EAAE,KAAK,CAAC,IAAI;wBAChB,MAAM,EAAE,KAAK,CAAC,MAAM;qBACvB;iBACJ,CAAC,CAAC;aACN;SACJ;IACL,CAAC;IAEO,KAAK,CAAC,wBAAwB;QAClC,IAAI;YACA,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE,CAAC;YAE5D,0CAA0C;YAC1C,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,iBAAiB,EAAE,OAAO,CAAC,CAAC;YACtD,IAAI,CAAC,aAAa,EAAE,CAAC;SACxB;QAAC,OAAO,KAAK,EAAE;YACZ,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,OAAO,EAAE,kCAAkC,KAAK,EAAE,CAAC,CAAC;YAC9E,IAAI,CAAC,aAAa,EAAE,CAAC;SACxB;IACL,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,QAAgB;QAChD,IAAI,CAAC,QAAQ;YAAE,OAAO;QAEtB,IAAI;YACA,4BAA4B;YAC5B,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;YACzC,IAAI,CAAC,aAAa,EAAE,CAAC;YAErB,wBAAwB;YACxB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;YAEjE,6BAA6B;YAC7B,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,YAAY,EAAE,MAAM,CAAC,CAAC;YACnD,IAAI,CAAC,aAAa,EAAE,CAAC;SACxB;QAAC,OAAO,KAAK,EAAE;YACZ,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,OAAO,EAAE,sCAAsC,KAAK,EAAE,CAAC,CAAC;YAClF,IAAI,CAAC,aAAa,EAAE,CAAC;SACxB;IACL,CAAC;IAEM,IAAI;QACP,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC;SAC3B;IACL,CAAC;IAEO,iBAAiB,CAAC,OAAuB;QAC7C,uDAAuD;QACvD,MAAM,gBAAgB,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;QAC5F,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;QAEzD,iCAAiC;QACjC,MAAM,eAAe,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;QAC5F,MAAM,QAAQ,GAAG,OAAO,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC;QAEvD,uDAAuD;QACvD,MAAM,KAAK,GAAG,QAAQ,EAAE,CAAC;QAEzB,OAAO;;;;gGAIiF,OAAO,CAAC,SAAS,uBAAuB,KAAK;;0BAEnH,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6BAuDL,KAAK,UAAU,SAAS;6BACxB,KAAK;;;;;;;;;;;;;;;;;;;gBAmBlB,CAAC;IACb,CAAC;;AAhSL,kDAiSC;AAhS0B,4BAAQ,GAAG,iBAAiB,CAAC;AAkSxD,SAAS,QAAQ;IACb,IAAI,IAAI,GAAG,EAAE,CAAC;IACd,MAAM,QAAQ,GAAG,gEAAgE,CAAC;IAClF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;QACzB,IAAI,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;KACxE;IACD,OAAO,IAAI,CAAC;AAChB,CAAC"}