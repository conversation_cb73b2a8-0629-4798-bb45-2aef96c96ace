{"name": "agent-zero-vscode", "displayName": "Agent Zero - AI Assistant", "description": "Autonomous AI Agent Framework integrated with Visual Studio Code", "version": "1.0.0", "publisher": "agent-zero-team", "engines": {"vscode": "^1.74.0"}, "categories": ["AI", "Machine Learning", "Other"], "keywords": ["ai", "agent", "automation", "assistant", "llm", "chatbot", "code-generation", "productivity"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "agent-zero.openChat", "title": "Open Agent Zero Chat", "category": "Agent <PERSON>", "icon": "$(robot)"}, {"command": "agent-zero.newAgent", "title": "Create New Agent", "category": "Agent <PERSON>", "icon": "$(add)"}, {"command": "agent-zero.showSettings", "title": "Agent Zero Settings", "category": "Agent <PERSON>", "icon": "$(settings-gear)"}, {"command": "agent-zero.executeCode", "title": "Execute Code with Agent Zero", "category": "Agent <PERSON>", "icon": "$(play)"}, {"command": "agent-zero.analyzeFile", "title": "Analyze File with Agent Zero", "category": "Agent <PERSON>", "icon": "$(search)"}, {"command": "agent-zero.generateCode", "title": "Generate Code", "category": "Agent <PERSON>", "icon": "$(code)"}, {"command": "agent-zero.searchKnowledge", "title": "Search Knowledge", "category": "Agent <PERSON>", "icon": "$(search)"}, {"command": "agent-zero.save<PERSON><PERSON><PERSON>", "title": "Save Memory", "category": "Agent <PERSON>", "icon": "$(save)"}, {"command": "agent-zero.showAgentDashboard", "title": "Show Agent Dashboard", "category": "Agent <PERSON>", "icon": "$(dashboard)"}, {"command": "agent-zero.createAgentFromTemplate", "title": "Create Agent from Template", "category": "Agent <PERSON>", "icon": "$(add)"}, {"command": "agent-zero.createCustomAgent", "title": "Create Custom Agent", "category": "Agent <PERSON>", "icon": "$(gear)"}], "menus": {"editor/context": [{"command": "agent-zero.analyzeFile", "when": "editorHasSelection", "group": "agent-zero"}, {"command": "agent-zero.generateCode", "group": "agent-zero"}], "explorer/context": [{"command": "agent-zero.analyzeFile", "when": "resourceExtname =~ /\\.(py|js|ts|java|cpp|c|cs|php|rb|go|rs|swift|kt)$/", "group": "agent-zero"}], "commandPalette": [{"command": "agent-zero.openChat"}, {"command": "agent-zero.newAgent"}, {"command": "agent-zero.showSettings"}, {"command": "agent-zero.executeCode"}, {"command": "agent-zero.analyzeFile"}, {"command": "agent-zero.generateCode"}]}, "viewsContainers": {"activitybar": [{"id": "agent-zero", "title": "Agent <PERSON>", "icon": "$(robot)"}]}, "views": {"agent-zero": [{"id": "agent-zero-chat", "name": "Cha<PERSON>", "when": "agent-zero:enabled"}, {"id": "agent-zero-agents", "name": "Active Agents", "when": "agent-zero:enabled"}, {"id": "agent-zero-memory", "name": "Memory & Knowledge", "when": "agent-zero:enabled"}, {"id": "agent-zero-tools", "name": "Tools", "when": "agent-zero:enabled"}, {"id": "agent-zero-enhanced-memory", "name": "Enhanced Memory", "when": "agent-zero:enabled"}, {"id": "agent-zero-multi-agents", "name": "Multi-Agent System", "when": "agent-zero:enabled"}]}, "configuration": {"title": "Agent <PERSON>", "properties": {"agent-zero.models.chat.provider": {"type": "string", "default": "openai", "enum": ["openai", "anthropic", "gemini", "groq", "ollama", "lm-studio", "mistral", "codestral", "deepseek", "azure", "openrouter", "sambanova", "huggingface", "other"], "description": "Chat model provider"}, "agent-zero.models.chat.name": {"type": "string", "default": "gpt-4", "description": "Chat model name"}, "agent-zero.models.utility.provider": {"type": "string", "default": "openai", "enum": ["openai", "anthropic", "gemini", "groq", "ollama", "lm-studio", "mistral", "codestral", "deepseek", "azure", "openrouter", "sambanova", "huggingface", "other"], "description": "Utility model provider"}, "agent-zero.models.utility.name": {"type": "string", "default": "gpt-3.5-turbo", "description": "Utility model name"}, "agent-zero.models.embeddings.provider": {"type": "string", "default": "openai", "enum": ["openai", "anthropic", "gemini", "groq", "ollama", "lm-studio", "mistral", "codestral", "deepseek", "azure", "openrouter", "sambanova", "huggingface", "sentence-transformers", "other"], "description": "Embeddings model provider"}, "agent-zero.models.embeddings.name": {"type": "string", "default": "text-embedding-ada-002", "description": "Embeddings model name"}, "agent-zero.agent.prompts": {"type": "string", "default": "agent0", "description": "Agent prompts subdirectory"}, "agent-zero.agent.memory": {"type": "string", "default": "default", "description": "Agent memory subdirectory"}, "agent-zero.agent.knowledge": {"type": "string", "default": "custom", "description": "Agent knowledge subdirectory"}, "agent-zero.execution.enableDocker": {"type": "boolean", "default": false, "description": "Enable Docker for code execution"}, "agent-zero.execution.enableSSH": {"type": "boolean", "default": false, "description": "Enable SSH for remote execution"}, "agent-zero.ui.theme": {"type": "string", "default": "auto", "enum": ["auto", "light", "dark"], "description": "UI theme preference"}, "agent-zero.ui.language": {"type": "string", "default": "en", "enum": ["en", "ar"], "description": "UI language"}}}, "keybindings": [{"command": "agent-zero.openChat", "key": "ctrl+shift+a", "mac": "cmd+shift+a"}, {"command": "agent-zero.newAgent", "key": "ctrl+shift+n", "mac": "cmd+shift+n"}, {"command": "agent-zero.executeCode", "key": "ctrl+shift+e", "mac": "cmd+shift+e", "when": "editorTextFocus"}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "package": "vsce package", "publish": "vsce publish"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "eslint": "^8.28.0", "typescript": "^4.9.4", "@vscode/test-electron": "^2.2.0", "@vscode/vsce": "^2.15.0"}, "dependencies": {"ws": "^8.13.0", "axios": "^1.4.0", "express": "^4.18.2", "socket.io": "^4.7.2", "socket.io-client": "^4.7.2"}, "repository": {"type": "git", "url": "https://github.com/agent-zero-team/agent-zero-vscode.git"}, "bugs": {"url": "https://github.com/agent-zero-team/agent-zero-vscode/issues"}, "homepage": "https://github.com/agent-zero-team/agent-zero-vscode#readme", "license": "MIT"}