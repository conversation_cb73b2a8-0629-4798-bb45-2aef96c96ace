"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageTypes = exports.WebSocketService = void 0;
const vscode = __importStar(require("vscode"));
const WebSocket = __importStar(require("ws"));
class WebSocketService {
    constructor(config) {
        this.ws = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.heartbeatTimer = null;
        this.reconnectTimer = null;
        this.messageHandlers = new Map();
        this.pendingMessages = [];
        this.config = config;
        this.outputChannel = vscode.window.createOutputChannel('Agent Zero WebSocket');
    }
    async connect() {
        if (this.isConnected) {
            return true;
        }
        try {
            this.outputChannel.appendLine(`Connecting to WebSocket: ${this.config.url}`);
            this.ws = new WebSocket(this.config.url);
            return new Promise((resolve, reject) => {
                if (!this.ws) {
                    reject(new Error('WebSocket instance is null'));
                    return;
                }
                const timeout = setTimeout(() => {
                    reject(new Error('Connection timeout'));
                }, 10000);
                this.ws.on('open', () => {
                    clearTimeout(timeout);
                    this.isConnected = true;
                    this.reconnectAttempts = 0;
                    this.outputChannel.appendLine('WebSocket connected successfully');
                    this.startHeartbeat();
                    this.processPendingMessages();
                    resolve(true);
                });
                this.ws.on('message', (data) => {
                    this.handleMessage(data);
                });
                this.ws.on('close', (code, reason) => {
                    this.handleClose(code, reason);
                });
                this.ws.on('error', (error) => {
                    clearTimeout(timeout);
                    this.handleError(error);
                    reject(error);
                });
            });
        }
        catch (error) {
            this.outputChannel.appendLine(`WebSocket connection failed: ${error}`);
            return false;
        }
    }
    disconnect() {
        if (this.ws) {
            this.isConnected = false;
            this.stopHeartbeat();
            this.stopReconnect();
            this.ws.close();
            this.ws = null;
            this.outputChannel.appendLine('WebSocket disconnected');
        }
    }
    send(message) {
        if (!this.isConnected || !this.ws) {
            // Queue message for later sending
            this.pendingMessages.push(message);
            this.outputChannel.appendLine(`Message queued (not connected): ${message.type}`);
            return false;
        }
        try {
            const messageWithMetadata = {
                ...message,
                id: message.id || this.generateMessageId(),
                timestamp: Date.now()
            };
            this.ws.send(JSON.stringify(messageWithMetadata));
            this.outputChannel.appendLine(`Message sent: ${message.type}`);
            return true;
        }
        catch (error) {
            this.outputChannel.appendLine(`Failed to send message: ${error}`);
            return false;
        }
    }
    onMessage(type, handler) {
        this.messageHandlers.set(type, handler);
    }
    removeMessageHandler(type) {
        this.messageHandlers.delete(type);
    }
    handleMessage(data) {
        try {
            const message = JSON.parse(data.toString());
            this.outputChannel.appendLine(`Message received: ${message.type}`);
            // Handle heartbeat responses
            if (message.type === 'pong') {
                return;
            }
            // Call registered handler
            const handler = this.messageHandlers.get(message.type);
            if (handler) {
                handler(message.data);
            }
            else {
                this.outputChannel.appendLine(`No handler for message type: ${message.type}`);
            }
        }
        catch (error) {
            this.outputChannel.appendLine(`Failed to parse message: ${error}`);
        }
    }
    handleClose(code, reason) {
        this.isConnected = false;
        this.stopHeartbeat();
        this.outputChannel.appendLine(`WebSocket closed: ${code} - ${reason}`);
        // Attempt to reconnect if not manually disconnected
        if (code !== 1000 && this.reconnectAttempts < this.config.maxReconnectAttempts) {
            this.scheduleReconnect();
        }
    }
    handleError(error) {
        this.outputChannel.appendLine(`WebSocket error: ${error.message}`);
        this.isConnected = false;
    }
    scheduleReconnect() {
        if (this.reconnectTimer) {
            return;
        }
        this.reconnectAttempts++;
        const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000); // Exponential backoff, max 30s
        this.outputChannel.appendLine(`Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);
        this.reconnectTimer = setTimeout(async () => {
            this.reconnectTimer = null;
            try {
                await this.connect();
            }
            catch (error) {
                this.outputChannel.appendLine(`Reconnect attempt ${this.reconnectAttempts} failed: ${error}`);
                if (this.reconnectAttempts < this.config.maxReconnectAttempts) {
                    this.scheduleReconnect();
                }
                else {
                    this.outputChannel.appendLine('Max reconnect attempts reached');
                    vscode.window.showErrorMessage('Agent Zero WebSocket connection lost. Please restart the service.');
                }
            }
        }, delay);
    }
    stopReconnect() {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
        this.reconnectAttempts = 0;
    }
    startHeartbeat() {
        this.stopHeartbeat();
        this.heartbeatTimer = setInterval(() => {
            if (this.isConnected && this.ws) {
                this.send({
                    type: 'ping',
                    data: {}
                });
            }
        }, this.config.heartbeatInterval);
    }
    stopHeartbeat() {
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
        }
    }
    processPendingMessages() {
        if (this.pendingMessages.length === 0) {
            return;
        }
        this.outputChannel.appendLine(`Processing ${this.pendingMessages.length} pending messages`);
        const messages = [...this.pendingMessages];
        this.pendingMessages = [];
        for (const message of messages) {
            this.send(message);
        }
    }
    generateMessageId() {
        return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    getConnectionStatus() {
        return {
            connected: this.isConnected,
            reconnectAttempts: this.reconnectAttempts,
            pendingMessages: this.pendingMessages.length
        };
    }
    dispose() {
        this.disconnect();
        this.outputChannel.dispose();
    }
}
exports.WebSocketService = WebSocketService;
// WebSocket message types for Agent Zero
exports.MessageTypes = {
    // Chat messages
    CHAT_MESSAGE: 'chat_message',
    CHAT_RESPONSE: 'chat_response',
    // Agent management
    AGENT_CREATE: 'agent_create',
    AGENT_DELETE: 'agent_delete',
    AGENT_STATUS: 'agent_status',
    AGENT_LIST: 'agent_list',
    // Code execution
    CODE_EXECUTE: 'code_execute',
    CODE_RESULT: 'code_result',
    // File operations
    FILE_READ: 'file_read',
    FILE_WRITE: 'file_write',
    FILE_LIST: 'file_list',
    // Memory operations
    MEMORY_SAVE: 'memory_save',
    MEMORY_LOAD: 'memory_load',
    MEMORY_SEARCH: 'memory_search',
    // Tool operations
    TOOL_EXECUTE: 'tool_execute',
    TOOL_RESULT: 'tool_result',
    TOOL_LIST: 'tool_list',
    // System messages
    SYSTEM_STATUS: 'system_status',
    SYSTEM_ERROR: 'system_error',
    SYSTEM_LOG: 'system_log',
    // Heartbeat
    PING: 'ping',
    PONG: 'pong'
};
//# sourceMappingURL=WebSocketService.js.map