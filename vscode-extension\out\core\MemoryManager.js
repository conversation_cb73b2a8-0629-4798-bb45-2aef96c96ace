"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MemoryManager = void 0;
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
class MemoryManager {
    constructor(context) {
        this.context = context;
        this._onDidChangeTreeData = new vscode.EventEmitter();
        this.onDidChangeTreeData = this._onDidChangeTreeData.event;
        this.memories = new Map();
        this.knowledge = new Map();
        this.memoryCounter = 0;
        this.knowledgeCounter = 0;
        this.loadMemoryFromStorage();
        this.loadKnowledgeFromWorkspace();
    }
    async saveMemory(content, type, tags = [], importance = 5) {
        const memory = {
            id: `memory-${++this.memoryCounter}`,
            type,
            content,
            tags,
            timestamp: new Date(),
            importance,
            metadata: {}
        };
        this.memories.set(memory.id, memory);
        await this.saveMemoryToStorage();
        this.refresh();
        return memory.id;
    }
    async loadMemory(query, type) {
        const results = [];
        for (const memory of this.memories.values()) {
            if (type && memory.type !== type) {
                continue;
            }
            // Simple text search - in real implementation, this would use embeddings
            if (memory.content.toLowerCase().includes(query.toLowerCase()) ||
                memory.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase()))) {
                results.push(memory);
            }
        }
        // Sort by importance and recency
        return results.sort((a, b) => {
            const importanceDiff = b.importance - a.importance;
            if (importanceDiff !== 0)
                return importanceDiff;
            return b.timestamp.getTime() - a.timestamp.getTime();
        });
    }
    async deleteMemory(id) {
        const deleted = this.memories.delete(id);
        if (deleted) {
            await this.saveMemoryToStorage();
            this.refresh();
        }
        return deleted;
    }
    async updateMemory(id, updates) {
        const memory = this.memories.get(id);
        if (!memory) {
            return false;
        }
        Object.assign(memory, updates);
        await this.saveMemoryToStorage();
        this.refresh();
        return true;
    }
    async addKnowledge(title, content, category, source) {
        const knowledge = {
            id: `knowledge-${++this.knowledgeCounter}`,
            title,
            content,
            category,
            source,
            lastUpdated: new Date()
        };
        this.knowledge.set(knowledge.id, knowledge);
        await this.saveKnowledgeToWorkspace();
        this.refresh();
        return knowledge.id;
    }
    async searchKnowledge(query, category) {
        const results = [];
        for (const item of this.knowledge.values()) {
            if (category && item.category !== category) {
                continue;
            }
            // Simple text search
            if (item.title.toLowerCase().includes(query.toLowerCase()) ||
                item.content.toLowerCase().includes(query.toLowerCase())) {
                results.push(item);
            }
        }
        return results.sort((a, b) => b.lastUpdated.getTime() - a.lastUpdated.getTime());
    }
    getMemoryStats() {
        const stats = {
            total: this.memories.size,
            byType: {},
            byImportance: {}
        };
        for (const memory of this.memories.values()) {
            stats.byType[memory.type] = (stats.byType[memory.type] || 0) + 1;
            const importanceRange = Math.floor(memory.importance / 2) * 2; // Group by 2s
            const key = `${importanceRange}-${importanceRange + 1}`;
            stats.byImportance[key] = (stats.byImportance[key] || 0) + 1;
        }
        return stats;
    }
    getKnowledgeStats() {
        const stats = {
            total: this.knowledge.size,
            byCategory: {}
        };
        for (const item of this.knowledge.values()) {
            stats.byCategory[item.category] = (stats.byCategory[item.category] || 0) + 1;
        }
        return stats;
    }
    async loadMemoryFromStorage() {
        try {
            const memoryData = this.context.globalState.get('agent-zero.memories', {});
            for (const [id, memory] of Object.entries(memoryData)) {
                // Convert timestamp back to Date object
                memory.timestamp = new Date(memory.timestamp);
                this.memories.set(id, memory);
            }
            this.memoryCounter = this.memories.size;
        }
        catch (error) {
            console.error('Failed to load memory from storage:', error);
        }
    }
    async saveMemoryToStorage() {
        try {
            const memoryData = {};
            for (const [id, memory] of this.memories.entries()) {
                memoryData[id] = memory;
            }
            await this.context.globalState.update('agent-zero.memories', memoryData);
        }
        catch (error) {
            console.error('Failed to save memory to storage:', error);
        }
    }
    loadKnowledgeFromWorkspace() {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            return;
        }
        const knowledgePath = path.join(workspaceFolder.uri.fsPath, '.agent-zero', 'knowledge');
        if (fs.existsSync(knowledgePath)) {
            try {
                const files = fs.readdirSync(knowledgePath);
                for (const file of files) {
                    if (file.endsWith('.json')) {
                        const filePath = path.join(knowledgePath, file);
                        const content = fs.readFileSync(filePath, 'utf8');
                        const knowledgeItem = JSON.parse(content);
                        knowledgeItem.lastUpdated = new Date(knowledgeItem.lastUpdated);
                        this.knowledge.set(knowledgeItem.id, knowledgeItem);
                    }
                }
                this.knowledgeCounter = this.knowledge.size;
            }
            catch (error) {
                console.error('Failed to load knowledge from workspace:', error);
            }
        }
    }
    async saveKnowledgeToWorkspace() {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            return;
        }
        const knowledgePath = path.join(workspaceFolder.uri.fsPath, '.agent-zero', 'knowledge');
        try {
            // Ensure directory exists
            if (!fs.existsSync(knowledgePath)) {
                fs.mkdirSync(knowledgePath, { recursive: true });
            }
            // Save each knowledge item as a separate file
            for (const [id, item] of this.knowledge.entries()) {
                const filePath = path.join(knowledgePath, `${id}.json`);
                fs.writeFileSync(filePath, JSON.stringify(item, null, 2));
            }
        }
        catch (error) {
            console.error('Failed to save knowledge to workspace:', error);
        }
    }
    // TreeDataProvider implementation
    getTreeItem(element) {
        const isMemory = 'type' in element;
        const item = new vscode.TreeItem(isMemory ? `${element.type}: ${element.content.substring(0, 50)}...` : element.title, vscode.TreeItemCollapsibleState.None);
        if (isMemory) {
            item.description = `Importance: ${element.importance}`;
            item.tooltip = `${element.content}\nTags: ${element.tags.join(', ')}\nCreated: ${element.timestamp.toLocaleString()}`;
            item.contextValue = 'memoryItem';
            item.iconPath = new vscode.ThemeIcon('brain');
        }
        else {
            item.description = element.category;
            item.tooltip = `${element.content.substring(0, 200)}...\nSource: ${element.source}\nUpdated: ${element.lastUpdated.toLocaleString()}`;
            item.contextValue = 'knowledgeItem';
            item.iconPath = new vscode.ThemeIcon('book');
        }
        return item;
    }
    getChildren(element) {
        if (!element) {
            // Return both memories and knowledge items
            const items = [
                ...Array.from(this.memories.values()),
                ...Array.from(this.knowledge.values())
            ];
            return Promise.resolve(items);
        }
        return Promise.resolve([]);
    }
    refresh() {
        this._onDidChangeTreeData.fire();
    }
    dispose() {
        this.memories.clear();
        this.knowledge.clear();
    }
}
exports.MemoryManager = MemoryManager;
//# sourceMappingURL=MemoryManager.js.map