import os
from python.helpers.tool import Tool, Response
from python.helpers.print_style import PrintStyle
from python.helpers import files

class FileEditor(Tool):
    """
    أداة سريعة لتحرير الملفات مباشرة بدون فتح محرر خارجي
    """

    async def execute(self, **kwargs):
        """
        تنفيذ عملية تحرير الملف
        """
        try:
            action = self.args.get("action", "").lower().strip()
            file_path = self.args.get("file_path", "").strip()
            
            if not file_path:
                return Response(message="❌ مسار الملف مطلوب", finished=True)
            
            # تحويل المسار النسبي إلى مطلق
            abs_path = files.get_abs_path(file_path)
            
            if action == "read":
                return await self._read_file(abs_path)
            elif action == "write":
                content = self.args.get("content", "")
                return await self._write_file(abs_path, content)
            elif action == "append":
                content = self.args.get("content", "")
                return await self._append_file(abs_path, content)
            elif action == "replace":
                old_text = self.args.get("old_text", "")
                new_text = self.args.get("new_text", "")
                return await self._replace_in_file(abs_path, old_text, new_text)
            elif action == "create":
                content = self.args.get("content", "")
                return await self._create_file(abs_path, content)
            else:
                return Response(
                    message="❌ عملية غير مدعومة. استخدم: read, write, append, replace, create",
                    finished=True
                )
                
        except Exception as e:
            PrintStyle.error(f"خطأ في تحرير الملف: {str(e)}")
            return Response(message=f"❌ خطأ: {str(e)}", finished=True)

    async def _read_file(self, file_path: str) -> Response:
        """
        قراءة محتوى الملف
        """
        try:
            if not os.path.exists(file_path):
                return Response(message="❌ الملف غير موجود", finished=True)
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # تحديد حجم المحتوى
            lines_count = len(content.split('\n'))
            size_kb = len(content.encode('utf-8')) / 1024
            
            message = f"✅ تم قراءة الملف بنجاح\n"
            message += f"📄 المسار: {file_path}\n"
            message += f"📊 عدد الأسطر: {lines_count}\n"
            message += f"💾 الحجم: {size_kb:.1f} KB\n\n"
            message += f"📝 المحتوى:\n```\n{content}\n```"
            
            return Response(message=message, finished=True)
            
        except Exception as e:
            return Response(message=f"❌ خطأ في قراءة الملف: {str(e)}", finished=True)

    async def _write_file(self, file_path: str, content: str) -> Response:
        """
        كتابة محتوى جديد للملف (استبدال المحتوى الحالي)
        """
        try:
            # إنشاء المجلد إذا لم يكن موجوداً
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            lines_count = len(content.split('\n'))
            size_kb = len(content.encode('utf-8')) / 1024
            
            message = f"✅ تم كتابة الملف بنجاح\n"
            message += f"📄 المسار: {file_path}\n"
            message += f"📊 عدد الأسطر: {lines_count}\n"
            message += f"💾 الحجم: {size_kb:.1f} KB"
            
            return Response(message=message, finished=True)
            
        except Exception as e:
            return Response(message=f"❌ خطأ في كتابة الملف: {str(e)}", finished=True)

    async def _append_file(self, file_path: str, content: str) -> Response:
        """
        إضافة محتوى لنهاية الملف
        """
        try:
            # إنشاء المجلد إذا لم يكن موجوداً
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            with open(file_path, 'a', encoding='utf-8') as f:
                f.write(content)
            
            message = f"✅ تم إضافة المحتوى للملف بنجاح\n"
            message += f"📄 المسار: {file_path}\n"
            message += f"➕ تم إضافة {len(content.split())} كلمة"
            
            return Response(message=message, finished=True)
            
        except Exception as e:
            return Response(message=f"❌ خطأ في إضافة المحتوى: {str(e)}", finished=True)

    async def _replace_in_file(self, file_path: str, old_text: str, new_text: str) -> Response:
        """
        استبدال نص في الملف
        """
        try:
            if not os.path.exists(file_path):
                return Response(message="❌ الملف غير موجود", finished=True)
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if old_text not in content:
                return Response(message="❌ النص المطلوب استبداله غير موجود", finished=True)
            
            new_content = content.replace(old_text, new_text)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            replacements_count = content.count(old_text)
            
            message = f"✅ تم استبدال النص بنجاح\n"
            message += f"📄 المسار: {file_path}\n"
            message += f"🔄 عدد الاستبدالات: {replacements_count}"
            
            return Response(message=message, finished=True)
            
        except Exception as e:
            return Response(message=f"❌ خطأ في استبدال النص: {str(e)}", finished=True)

    async def _create_file(self, file_path: str, content: str = "") -> Response:
        """
        إنشاء ملف جديد
        """
        try:
            if os.path.exists(file_path):
                return Response(message="❌ الملف موجود بالفعل", finished=True)
            
            # إنشاء المجلد إذا لم يكن موجوداً
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            message = f"✅ تم إنشاء الملف بنجاح\n"
            message += f"📄 المسار: {file_path}"
            
            if content:
                lines_count = len(content.split('\n'))
                message += f"\n📊 عدد الأسطر: {lines_count}"
            
            return Response(message=message, finished=True)
            
        except Exception as e:
            return Response(message=f"❌ خطأ في إنشاء الملف: {str(e)}", finished=True)
