# Agent Zero VS Code Extension - API Reference

## Core APIs

### AgentManager

The main interface for managing AI agents.

#### Methods

##### `createAgent(name: string, type: string, parentId?: string): Promise<Agent>`

Creates a new agent.

**Parameters:**
- `name` - The agent's display name
- `type` - The agent type (e.g., 'general', 'code-developer')
- `parentId` - Optional parent agent ID for hierarchical structure

**Returns:** Promise resolving to the created Agent object

**Example:**
```typescript
const agent = await agentManager.createAgent('Code Helper', 'code-developer');
console.log(`Created agent: ${agent.name} with ID: ${agent.id}`);
```

##### `sendMessage(message: string, agentId?: string): Promise<string>`

Sends a message to an agent and returns the response.

**Parameters:**
- `message` - The message to send
- `agentId` - Optional specific agent ID (uses active agent if not provided)

**Returns:** Promise resolving to the agent's response

**Example:**
```typescript
const response = await agentManager.sendMessage('Help me debug this function');
console.log('Agent response:', response);
```

##### `executeCode(code: string, language: string): Promise<void>`

Executes code through the active agent.

**Parameters:**
- `code` - The code to execute
- `language` - Programming language (e.g., 'python', 'javascript')

**Example:**
```typescript
await agentManager.executeCode('print("Hello, World!")', 'python');
```

### ModelManager

Manages AI model interactions across multiple providers.

#### Methods

##### `processMessage(message: string, context?: any): Promise<string>`

Processes a message using the configured chat model.

**Parameters:**
- `message` - The input message
- `context` - Optional context object with conversation history

**Returns:** Promise resolving to the model's response

**Example:**
```typescript
const response = await modelManager.processMessage('Explain async/await in JavaScript', {
    systemMessage: 'You are a helpful programming tutor'
});
```

##### `generateEmbeddings(text: string): Promise<number[]>`

Generates embeddings for the given text.

**Parameters:**
- `text` - Text to generate embeddings for

**Returns:** Promise resolving to embedding vector

**Example:**
```typescript
const embeddings = await modelManager.generateEmbeddings('This is sample text');
console.log('Embedding dimensions:', embeddings.length);
```

##### `switchModel(type: 'chat' | 'utility' | 'embeddings', config: ModelConfig): Promise<void>`

Switches to a different model configuration.

**Parameters:**
- `type` - Model type to switch
- `config` - New model configuration

**Example:**
```typescript
await modelManager.switchModel('chat', {
    provider: 'anthropic',
    name: 'claude-3-sonnet-20240229',
    apiBase: 'https://api.anthropic.com',
    limitOutput: 4000
});
```

### ToolManager

Manages and executes various tools and integrations.

#### Methods

##### `executeTool(toolId: string, parameters: Record<string, any>): Promise<ToolResult>`

Executes a specific tool with given parameters.

**Parameters:**
- `toolId` - Identifier of the tool to execute
- `parameters` - Tool-specific parameters

**Returns:** Promise resolving to ToolResult

**Example:**
```typescript
const result = await toolManager.executeTool('file-editor', {
    operation: 'read',
    path: './src/main.ts'
});

if (result.success) {
    console.log('File content:', result.output);
}
```

##### `executeCode(code: string, language: string): Promise<ToolResult>`

Executes code using the code execution tool.

**Parameters:**
- `code` - Code to execute
- `language` - Programming language

**Returns:** Promise resolving to execution result

**Example:**
```typescript
const result = await toolManager.executeCode(`
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

print(fibonacci(10))
`, 'python');

console.log('Output:', result.output);
```

### EnhancedMemoryManager

Advanced memory and knowledge management system.

#### Methods

##### `saveMemory(content: string, type: MemoryType, title?: string, tags?: string[], importance?: number): Promise<string>`

Saves content to the agent's memory.

**Parameters:**
- `content` - Content to save
- `type` - Memory type ('fact', 'code', 'solution', etc.)
- `title` - Optional title
- `tags` - Optional tags for categorization
- `importance` - Importance score (1-10)

**Returns:** Promise resolving to memory ID

**Example:**
```typescript
const memoryId = await memoryManager.saveMemory(
    'React hooks allow you to use state in functional components',
    'fact',
    'React Hooks Concept',
    ['react', 'hooks', 'javascript'],
    8
);
```

##### `loadMemory(query: string, type?: MemoryType, limit?: number): Promise<EnhancedMemoryItem[]>`

Searches and retrieves memories.

**Parameters:**
- `query` - Search query
- `type` - Optional memory type filter
- `limit` - Maximum number of results

**Returns:** Promise resolving to array of memory items

**Example:**
```typescript
const memories = await memoryManager.loadMemory('React hooks', 'fact', 5);
memories.forEach(memory => {
    console.log(`${memory.title}: ${memory.content}`);
});
```

### MultiAgentSystem

Hierarchical multi-agent management system.

#### Methods

##### `createAgentFromTemplate(templateId: string, customName?: string, parentId?: string): Promise<Agent>`

Creates an agent from a predefined template.

**Parameters:**
- `templateId` - Template identifier
- `customName` - Optional custom name
- `parentId` - Optional parent agent ID

**Returns:** Promise resolving to created agent

**Example:**
```typescript
const codeReviewer = await multiAgentSystem.createAgentFromTemplate(
    'code-reviewer',
    'Senior Code Reviewer',
    parentAgentId
);
```

##### `assignTask(agentId: string, task: Omit<AgentTask, 'id' | 'agentId' | 'createdAt' | 'updatedAt'>): Promise<string>`

Assigns a task to an agent.

**Parameters:**
- `agentId` - Target agent ID
- `task` - Task object without auto-generated fields

**Returns:** Promise resolving to task ID

**Example:**
```typescript
const taskId = await multiAgentSystem.assignTask(agentId, {
    title: 'Review authentication module',
    description: 'Review the user authentication code for security issues',
    status: 'pending',
    priority: 'high',
    dependencies: [],
    assignedBy: 'user'
});
```

## Service APIs

### AgentZeroService

Manages the Agent Zero backend service lifecycle.

#### Methods

##### `start(): Promise<boolean>`

Starts the Agent Zero backend service.

**Returns:** Promise resolving to success status

##### `stop(): Promise<boolean>`

Stops the Agent Zero backend service.

**Returns:** Promise resolving to success status

##### `getStatus(): ServiceStatus`

Gets the current service status.

**Returns:** ServiceStatus object

**Example:**
```typescript
const status = agentZeroService.getStatus();
console.log(`Service running: ${status.running}, Port: ${status.port}`);
```

### ApiService

High-level API for interacting with Agent Zero.

#### Methods

##### `sendChatMessage(message: string, agentId?: string): Promise<string>`

Sends a chat message via the API service.

**Parameters:**
- `message` - Message to send
- `agentId` - Optional target agent ID

**Returns:** Promise resolving to response

##### `executeCode(request: CodeExecutionRequest): Promise<CodeExecutionResult>`

Executes code via the API service.

**Parameters:**
- `request` - Code execution request object

**Returns:** Promise resolving to execution result

**Example:**
```typescript
const result = await apiService.executeCode({
    code: 'console.log("Hello from API");',
    language: 'javascript',
    timeout: 5000
});
```

## Tool APIs

### VSCodeFileEditor

Advanced file operations with VS Code integration.

#### Methods

##### `executeOperation(operation: FileEditOperation): Promise<FileEditResult>`

Executes a file operation.

**Parameters:**
- `operation` - File operation object

**Returns:** Promise resolving to operation result

**Example:**
```typescript
const result = await fileEditor.executeOperation({
    type: 'write',
    path: './output.txt',
    content: 'Generated content'
});
```

### VSCodeExecutor

Code execution with multiple language support.

#### Methods

##### `executeCode(request: ExecutionRequest): Promise<ExecutionResult>`

Executes code with advanced options.

**Parameters:**
- `request` - Execution request with code, language, and options

**Returns:** Promise resolving to execution result

**Example:**
```typescript
const result = await executor.executeCode({
    code: 'import time; time.sleep(1); print("Done")',
    language: 'python',
    timeout: 10000,
    workingDirectory: '/tmp'
});
```

### VSCodeSearchEngine

Multi-source search capabilities.

#### Methods

##### `search(request: SearchRequest): Promise<SearchResponse>`

Performs a search across multiple sources.

**Parameters:**
- `request` - Search request object

**Returns:** Promise resolving to search results

**Example:**
```typescript
const results = await searchEngine.search({
    query: 'React useState hook',
    engine: 'duckduckgo',
    maxResults: 10
});
```

## Event APIs

### Extension Events

The extension emits various events that can be listened to:

#### Agent Events

```typescript
// Listen for agent creation
vscode.commands.registerCommand('agent-zero.onAgentCreated', (agent: Agent) => {
    console.log(`New agent created: ${agent.name}`);
});

// Listen for agent status changes
vscode.commands.registerCommand('agent-zero.onAgentStatusChanged', (agentId: string, status: string) => {
    console.log(`Agent ${agentId} status changed to: ${status}`);
});
```

#### Memory Events

```typescript
// Listen for memory saves
vscode.commands.registerCommand('agent-zero.onMemorySaved', (memoryId: string) => {
    console.log(`Memory saved: ${memoryId}`);
});
```

#### Task Events

```typescript
// Listen for task completion
vscode.commands.registerCommand('agent-zero.onTaskCompleted', (taskId: string, result: string) => {
    console.log(`Task ${taskId} completed with result: ${result}`);
});
```

## Configuration API

### ConfigurationManager

#### Methods

##### `get<T>(key: string, defaultValue: T): T`

Gets a configuration value.

**Parameters:**
- `key` - Configuration key
- `defaultValue` - Default value if key not found

**Returns:** Configuration value

**Example:**
```typescript
const chatModel = configManager.get('models.chat.name', 'gpt-3.5-turbo');
```

##### `updateConfiguration(updates: Partial<AgentZeroConfiguration>): Promise<void>`

Updates configuration values.

**Parameters:**
- `updates` - Partial configuration object with updates

**Example:**
```typescript
await configManager.updateConfiguration({
    models: {
        chat: {
            provider: 'anthropic',
            name: 'claude-3-sonnet-20240229'
        }
    }
});
```

## Type Definitions

### Core Types

```typescript
interface Agent {
    id: string;
    name: string;
    type: string;
    status: 'idle' | 'working' | 'error';
    created: Date;
    lastActivity: Date;
    parentId?: string;
    children: string[];
    context: Record<string, any>;
}

interface ToolResult {
    success: boolean;
    output: string;
    error?: string;
    data?: any;
}

interface ModelConfig {
    provider: string;
    name: string;
    apiBase?: string;
    limitOutput?: number;
    kwargs?: Record<string, any>;
}

interface EnhancedMemoryItem {
    id: string;
    type: 'fact' | 'code' | 'solution' | 'instruction' | 'knowledge' | 'conversation' | 'file-context';
    content: string;
    title: string;
    tags: string[];
    timestamp: Date;
    importance: number;
    embedding?: number[];
    metadata: Record<string, any>;
    relationships: {
        parentId?: string;
        childIds: string[];
        relatedIds: string[];
    };
}

interface AgentTask {
    id: string;
    agentId: string;
    title: string;
    description: string;
    status: 'pending' | 'in-progress' | 'completed' | 'failed' | 'cancelled';
    priority: 'low' | 'medium' | 'high' | 'urgent';
    createdAt: Date;
    updatedAt: Date;
    completedAt?: Date;
    result?: string;
    dependencies: string[];
    assignedBy: string;
}
```

### Request/Response Types

```typescript
interface CodeExecutionRequest {
    code: string;
    language: string;
    agentId?: string;
    timeout?: number;
    workingDirectory?: string;
    environment?: Record<string, string>;
}

interface CodeExecutionResult {
    success: boolean;
    output: string;
    error?: string;
    exitCode?: number;
    executionTime: number;
}

interface SearchRequest {
    query: string;
    engine?: 'duckduckgo' | 'google' | 'bing' | 'stackoverflow' | 'github';
    maxResults?: number;
    language?: string;
    region?: string;
}

interface SearchResponse {
    success: boolean;
    query: string;
    results: SearchResult[];
    totalResults?: number;
    searchTime?: number;
    error?: string;
}
```

## Error Handling

All API methods follow consistent error handling patterns:

```typescript
try {
    const result = await apiMethod();
    if (result.success) {
        // Handle success
        console.log(result.output);
    } else {
        // Handle failure
        console.error(result.error);
    }
} catch (error) {
    // Handle exceptions
    console.error('API call failed:', error);
}
```

## Rate Limiting

Some APIs have rate limiting:

- Model API calls: Depends on provider limits
- Search API calls: 10 requests per minute
- File operations: No limit
- Memory operations: No limit

## Authentication

API keys are managed through environment variables:

```bash
export OPENAI_API_KEY="your-key"
export ANTHROPIC_API_KEY="your-key"
export GOOGLE_API_KEY="your-key"
```

The extension automatically uses these keys when configured.
