import * as vscode from 'vscode';
import { ConfigurationManager, ModelConfig } from './ConfigurationManager';

export interface ModelResponse {
    content: string;
    usage?: {
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
    };
}

export class ModelManager {
    private chatModel: any;
    private utilityModel: any;
    private embeddingsModel: any;
    private browserModel: any;

    constructor(private configManager: ConfigurationManager) {
        this.initializeModels();
    }

    private initializeModels(): void {
        const config = this.configManager.getConfiguration();
        
        // Initialize models based on configuration
        this.chatModel = this.createModel(config.models.chat);
        this.utilityModel = this.createModel(config.models.utility);
        this.embeddingsModel = this.createModel(config.models.embeddings);
        this.browserModel = this.createModel(config.models.browser);
    }

    private createModel(config: ModelConfig): any {
        // Create model instance based on provider
        switch (config.provider.toLowerCase()) {
            case 'openai':
                return this.createOpenAIModel(config);
            case 'anthropic':
                return this.createAnthropicModel(config);
            case 'gemini':
            case 'google':
                return this.createGeminiModel(config);
            case 'groq':
                return this.createGroqModel(config);
            case 'ollama':
                return this.createOllamaModel(config);
            case 'lm-studio':
                return this.createLMStudioModel(config);
            case 'mistral':
                return this.createMistralModel(config);
            case 'codestral':
                return this.createCodestralModel(config);
            case 'deepseek':
                return this.createDeepSeekModel(config);
            case 'azure':
                return this.createAzureModel(config);
            case 'openrouter':
                return this.createOpenRouterModel(config);
            case 'sambanova':
                return this.createSambanovaModel(config);
            case 'huggingface':
                return this.createHuggingFaceModel(config);
            case 'sentence-transformers':
                return this.createSentenceTransformersModel(config);
            case 'other':
                return this.createCustomModel(config);
            default:
                throw new Error(`Unsupported model provider: ${config.provider}`);
        }
    }

    private createOpenAIModel(config: ModelConfig): any {
        return {
            provider: 'openai',
            name: config.name,
            config: config,
            apiKey: process.env.OPENAI_API_KEY,
            baseURL: config.apiBase || 'https://api.openai.com/v1',
            type: this.getModelType(config.name)
        };
    }

    private createAnthropicModel(config: ModelConfig): any {
        return {
            provider: 'anthropic',
            name: config.name,
            config: config,
            apiKey: process.env.ANTHROPIC_API_KEY,
            baseURL: config.apiBase || 'https://api.anthropic.com',
            type: this.getModelType(config.name)
        };
    }

    private createGeminiModel(config: ModelConfig): any {
        return {
            provider: 'gemini',
            name: config.name,
            config: config,
            apiKey: process.env.GOOGLE_API_KEY,
            baseURL: config.apiBase || 'https://generativelanguage.googleapis.com/v1',
            type: this.getModelType(config.name)
        };
    }

    private createGroqModel(config: ModelConfig): any {
        return {
            provider: 'groq',
            name: config.name,
            config: config,
            apiKey: process.env.GROQ_API_KEY,
            baseURL: config.apiBase || 'https://api.groq.com/openai/v1',
            type: this.getModelType(config.name)
        };
    }

    private createOllamaModel(config: ModelConfig): any {
        return {
            provider: 'ollama',
            name: config.name,
            config: config,
            baseURL: config.apiBase || 'http://localhost:11434',
            type: this.getModelType(config.name)
        };
    }

    private createLMStudioModel(config: ModelConfig): any {
        return {
            provider: 'lm-studio',
            name: config.name,
            config: config,
            baseURL: config.apiBase || 'http://localhost:1234/v1',
            type: this.getModelType(config.name)
        };
    }

    private createMistralModel(config: ModelConfig): any {
        return {
            provider: 'mistral',
            name: config.name,
            config: config,
            apiKey: process.env.MISTRAL_API_KEY,
            baseURL: config.apiBase || 'https://api.mistral.ai/v1',
            type: this.getModelType(config.name)
        };
    }

    private createCodestralModel(config: ModelConfig): any {
        return {
            provider: 'codestral',
            name: config.name,
            config: config,
            apiKey: process.env.CODESTRAL_API_KEY,
            baseURL: config.apiBase || 'https://codestral.mistral.ai/v1',
            type: this.getModelType(config.name)
        };
    }

    private createDeepSeekModel(config: ModelConfig): any {
        return {
            provider: 'deepseek',
            name: config.name,
            config: config,
            apiKey: process.env.DEEPSEEK_API_KEY,
            baseURL: config.apiBase || 'https://api.deepseek.com/v1',
            type: this.getModelType(config.name)
        };
    }

    private createAzureModel(config: ModelConfig): any {
        return {
            provider: 'azure',
            name: config.name,
            config: config,
            apiKey: process.env.AZURE_OPENAI_API_KEY,
            baseURL: config.apiBase || process.env.AZURE_OPENAI_ENDPOINT,
            deployment: process.env.AZURE_OPENAI_DEPLOYMENT_NAME,
            apiVersion: process.env.AZURE_OPENAI_API_VERSION || '2024-02-15-preview',
            type: this.getModelType(config.name)
        };
    }

    private createOpenRouterModel(config: ModelConfig): any {
        return {
            provider: 'openrouter',
            name: config.name,
            config: config,
            apiKey: process.env.OPENROUTER_API_KEY,
            baseURL: config.apiBase || 'https://openrouter.ai/api/v1',
            type: this.getModelType(config.name)
        };
    }

    private createSambanovaModel(config: ModelConfig): any {
        return {
            provider: 'sambanova',
            name: config.name,
            config: config,
            apiKey: process.env.SAMBANOVA_API_KEY,
            baseURL: config.apiBase || 'https://api.sambanova.ai/v1',
            type: this.getModelType(config.name)
        };
    }

    private createHuggingFaceModel(config: ModelConfig): any {
        return {
            provider: 'huggingface',
            name: config.name,
            config: config,
            apiKey: process.env.HUGGINGFACE_API_KEY,
            baseURL: config.apiBase || 'https://api-inference.huggingface.co',
            type: this.getModelType(config.name)
        };
    }

    private createSentenceTransformersModel(config: ModelConfig): any {
        return {
            provider: 'sentence-transformers',
            name: config.name,
            config: config,
            type: 'embedding',
            local: true
        };
    }

    private createCustomModel(config: ModelConfig): any {
        return {
            provider: 'custom',
            name: config.name,
            config: config,
            apiKey: process.env.CUSTOM_API_KEY,
            baseURL: config.apiBase,
            type: this.getModelType(config.name)
        };
    }

    private getModelType(modelName: string): string {
        const name = modelName.toLowerCase();

        if (name.includes('embedding') || name.includes('embed')) {
            return 'embedding';
        } else if (name.includes('vision') || name.includes('gpt-4v') || name.includes('claude-3')) {
            return 'vision';
        } else if (name.includes('code') || name.includes('codestral')) {
            return 'code';
        } else {
            return 'chat';
        }
    }

    public async processMessage(message: string, context?: any): Promise<string> {
        try {
            // Simulate model processing
            // In the real implementation, this would call the actual LLM
            const response = await this.callChatModel(message, context);
            return response.content;
        } catch (error) {
            throw new Error(`Failed to process message: ${error}`);
        }
    }

    public async generateCode(prompt: string, language: string): Promise<string> {
        try {
            const enhancedPrompt = `Generate ${language} code for the following request:\n\n${prompt}\n\nPlease provide only the code without explanations.`;
            const response = await this.callChatModel(enhancedPrompt);
            return this.extractCodeFromResponse(response.content, language);
        } catch (error) {
            throw new Error(`Failed to generate code: ${error}`);
        }
    }

    public async analyzeCode(code: string, language: string): Promise<string> {
        try {
            const prompt = `Analyze the following ${language} code and provide insights about its functionality, potential issues, and suggestions for improvement:\n\n\`\`\`${language}\n${code}\n\`\`\``;
            const response = await this.callChatModel(prompt);
            return response.content;
        } catch (error) {
            throw new Error(`Failed to analyze code: ${error}`);
        }
    }

    public async generateEmbeddings(text: string): Promise<number[]> {
        try {
            // Simulate embeddings generation
            // In the real implementation, this would call the embeddings model
            return new Array(1536).fill(0).map(() => Math.random());
        } catch (error) {
            throw new Error(`Failed to generate embeddings: ${error}`);
        }
    }

    public async searchSimilar(query: string, documents: string[]): Promise<Array<{document: string, similarity: number}>> {
        try {
            const queryEmbedding = await this.generateEmbeddings(query);
            const results: Array<{document: string, similarity: number}> = [];

            for (const doc of documents) {
                const docEmbedding = await this.generateEmbeddings(doc);
                const similarity = this.cosineSimilarity(queryEmbedding, docEmbedding);
                results.push({ document: doc, similarity });
            }

            return results.sort((a, b) => b.similarity - a.similarity);
        } catch (error) {
            throw new Error(`Failed to search similar documents: ${error}`);
        }
    }

    private async callChatModel(prompt: string, context?: any): Promise<ModelResponse> {
        try {
            const model = this.chatModel;
            if (!model) {
                throw new Error('Chat model not initialized');
            }

            // Build messages array
            const messages = [];

            // Add system message if context has one
            if (context?.systemMessage) {
                messages.push({
                    role: 'system',
                    content: context.systemMessage
                });
            }

            // Add conversation history if available
            if (context?.history && Array.isArray(context.history)) {
                messages.push(...context.history);
            }

            // Add current prompt
            messages.push({
                role: 'user',
                content: prompt
            });

            // Call the appropriate API based on provider
            const response = await this.callModelAPI(model, messages);

            return {
                content: response.content,
                usage: response.usage
            };

        } catch (error) {
            throw new Error(`Chat model API call failed: ${error}`);
        }
    }

    private async callModelAPI(model: any, messages: any[]): Promise<any> {
        const provider = model.provider.toLowerCase();

        switch (provider) {
            case 'openai':
                return await this.callOpenAIAPI(model, messages);
            case 'anthropic':
                return await this.callAnthropicAPI(model, messages);
            case 'gemini':
            case 'google':
                return await this.callGeminiAPI(model, messages);
            case 'groq':
                return await this.callGroqAPI(model, messages);
            case 'ollama':
                return await this.callOllamaAPI(model, messages);
            case 'lm-studio':
                return await this.callLMStudioAPI(model, messages);
            case 'mistral':
                return await this.callMistralAPI(model, messages);
            case 'codestral':
                return await this.callCodestralAPI(model, messages);
            case 'deepseek':
                return await this.callDeepSeekAPI(model, messages);
            case 'azure':
                return await this.callAzureAPI(model, messages);
            case 'openrouter':
                return await this.callOpenRouterAPI(model, messages);
            case 'sambanova':
                return await this.callSambanovaAPI(model, messages);
            case 'huggingface':
                return await this.callHuggingFaceAPI(model, messages);
            case 'custom':
            case 'other':
                return await this.callCustomAPI(model, messages);
            default:
                throw new Error(`Unsupported provider: ${provider}`);
        }
    }

    private async callOpenAIAPI(model: any, messages: any[]): Promise<any> {
        const response = await fetch(`${model.baseURL}/chat/completions`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${model.apiKey}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model: model.name,
                messages: messages,
                max_tokens: model.config.limitOutput || 4000,
                temperature: 0.7,
                ...model.config.kwargs
            })
        });

        if (!response.ok) {
            throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        return {
            content: data.choices[0].message.content,
            usage: {
                promptTokens: data.usage.prompt_tokens,
                completionTokens: data.usage.completion_tokens,
                totalTokens: data.usage.total_tokens
            }
        };
    }

    private async callAnthropicAPI(model: any, messages: any[]): Promise<any> {
        // Convert OpenAI format to Anthropic format
        const systemMessage = messages.find(m => m.role === 'system')?.content || '';
        const conversationMessages = messages.filter(m => m.role !== 'system');

        const response = await fetch(`${model.baseURL}/v1/messages`, {
            method: 'POST',
            headers: {
                'x-api-key': model.apiKey,
                'Content-Type': 'application/json',
                'anthropic-version': '2023-06-01'
            },
            body: JSON.stringify({
                model: model.name,
                max_tokens: model.config.limitOutput || 4000,
                system: systemMessage,
                messages: conversationMessages,
                ...model.config.kwargs
            })
        });

        if (!response.ok) {
            throw new Error(`Anthropic API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        return {
            content: data.content[0].text,
            usage: {
                promptTokens: data.usage.input_tokens,
                completionTokens: data.usage.output_tokens,
                totalTokens: data.usage.input_tokens + data.usage.output_tokens
            }
        };
    }

    private async callGeminiAPI(model: any, messages: any[]): Promise<any> {
        // Convert to Gemini format
        const contents = messages.map(msg => ({
            role: msg.role === 'assistant' ? 'model' : 'user',
            parts: [{ text: msg.content }]
        }));

        const response = await fetch(`${model.baseURL}/models/${model.name}:generateContent?key=${model.apiKey}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                contents: contents,
                generationConfig: {
                    maxOutputTokens: model.config.limitOutput || 4000,
                    temperature: 0.7,
                    ...model.config.kwargs
                }
            })
        });

        if (!response.ok) {
            throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        return {
            content: data.candidates[0].content.parts[0].text,
            usage: {
                promptTokens: data.usageMetadata?.promptTokenCount || 0,
                completionTokens: data.usageMetadata?.candidatesTokenCount || 0,
                totalTokens: data.usageMetadata?.totalTokenCount || 0
            }
        };
    }

    private async callGroqAPI(model: any, messages: any[]): Promise<any> {
        // Groq uses OpenAI-compatible API
        return await this.callOpenAIAPI(model, messages);
    }

    private async callOllamaAPI(model: any, messages: any[]): Promise<any> {
        const response = await fetch(`${model.baseURL}/api/chat`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model: model.name,
                messages: messages,
                stream: false,
                options: {
                    num_predict: model.config.limitOutput || 4000,
                    temperature: 0.7,
                    ...model.config.kwargs
                }
            })
        });

        if (!response.ok) {
            throw new Error(`Ollama API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        return {
            content: data.message.content,
            usage: {
                promptTokens: data.prompt_eval_count || 0,
                completionTokens: data.eval_count || 0,
                totalTokens: (data.prompt_eval_count || 0) + (data.eval_count || 0)
            }
        };
    }

    private async callLMStudioAPI(model: any, messages: any[]): Promise<any> {
        // LM Studio uses OpenAI-compatible API
        return await this.callOpenAIAPI(model, messages);
    }

    private async callMistralAPI(model: any, messages: any[]): Promise<any> {
        // Mistral uses OpenAI-compatible API
        return await this.callOpenAIAPI(model, messages);
    }

    private async callCodestralAPI(model: any, messages: any[]): Promise<any> {
        // Codestral uses OpenAI-compatible API
        return await this.callOpenAIAPI(model, messages);
    }

    private async callDeepSeekAPI(model: any, messages: any[]): Promise<any> {
        // DeepSeek uses OpenAI-compatible API
        return await this.callOpenAIAPI(model, messages);
    }

    private async callAzureAPI(model: any, messages: any[]): Promise<any> {
        const url = `${model.baseURL}/openai/deployments/${model.deployment}/chat/completions?api-version=${model.apiVersion}`;

        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'api-key': model.apiKey,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                messages: messages,
                max_tokens: model.config.limitOutput || 4000,
                temperature: 0.7,
                ...model.config.kwargs
            })
        });

        if (!response.ok) {
            throw new Error(`Azure API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        return {
            content: data.choices[0].message.content,
            usage: {
                promptTokens: data.usage.prompt_tokens,
                completionTokens: data.usage.completion_tokens,
                totalTokens: data.usage.total_tokens
            }
        };
    }

    private async callOpenRouterAPI(model: any, messages: any[]): Promise<any> {
        // OpenRouter uses OpenAI-compatible API
        return await this.callOpenAIAPI(model, messages);
    }

    private async callSambanovaAPI(model: any, messages: any[]): Promise<any> {
        // SambaNova uses OpenAI-compatible API
        return await this.callOpenAIAPI(model, messages);
    }

    private async callHuggingFaceAPI(model: any, messages: any[]): Promise<any> {
        // Convert messages to single prompt for HuggingFace
        const prompt = messages.map(m => `${m.role}: ${m.content}`).join('\n');

        const response = await fetch(`${model.baseURL}/models/${model.name}`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${model.apiKey}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                inputs: prompt,
                parameters: {
                    max_new_tokens: model.config.limitOutput || 4000,
                    temperature: 0.7,
                    ...model.config.kwargs
                }
            })
        });

        if (!response.ok) {
            throw new Error(`HuggingFace API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        const content = Array.isArray(data) ? data[0].generated_text : data.generated_text;

        return {
            content: content.replace(prompt, '').trim(),
            usage: {
                promptTokens: prompt.length / 4,
                completionTokens: content.length / 4,
                totalTokens: (prompt.length + content.length) / 4
            }
        };
    }

    private async callCustomAPI(model: any, messages: any[]): Promise<any> {
        // Custom/Other providers - assume OpenAI-compatible
        return await this.callOpenAIAPI(model, messages);
    }

    private extractCodeFromResponse(response: string, language: string): string {
        // Extract code blocks from the response
        const codeBlockRegex = new RegExp(`\`\`\`${language}?\\s*([\\s\\S]*?)\`\`\``, 'gi');
        const matches = response.match(codeBlockRegex);
        
        if (matches && matches.length > 0) {
            // Remove the code block markers
            return matches[0].replace(/```[\w]*\s*/, '').replace(/```\s*$/, '').trim();
        }
        
        // If no code blocks found, return the response as is
        return response.trim();
    }

    private cosineSimilarity(a: number[], b: number[]): number {
        if (a.length !== b.length) {
            throw new Error('Vectors must have the same length');
        }

        let dotProduct = 0;
        let normA = 0;
        let normB = 0;

        for (let i = 0; i < a.length; i++) {
            dotProduct += a[i] * b[i];
            normA += a[i] * a[i];
            normB += b[i] * b[i];
        }

        return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
    }

    public updateConfiguration(): void {
        this.initializeModels();
    }

    public getModelInfo(type: 'chat' | 'utility' | 'embeddings' | 'browser'): ModelConfig {
        const config = this.configManager.getConfiguration();
        return config.models[type];
    }

    public async testConnection(type: 'chat' | 'utility' | 'embeddings' | 'browser'): Promise<boolean> {
        try {
            // Test the connection to the specified model
            // This would make a simple API call to verify connectivity
            return true;
        } catch (error) {
            return false;
        }
    }

    public dispose(): void {
        // Clean up model resources
        this.chatModel = null;
        this.utilityModel = null;
        this.embeddingsModel = null;
        this.browserModel = null;
    }
}
