import * as vscode from 'vscode';
import { EnhancedMemoryManager, EnhancedMemoryItem, WorkspaceKnowledge, FileKnowledge } from './EnhancedMemoryManager';
import { ModelManager } from './ModelManager';

export interface SearchQuery {
    text: string;
    type?: 'memory' | 'file' | 'workspace' | 'all';
    filters?: {
        memoryType?: EnhancedMemoryItem['type'];
        fileType?: FileKnowledge['type'];
        language?: string;
        timeRange?: { start: Date; end: Date };
        importance?: { min: number; max: number };
        tags?: string[];
    };
    limit?: number;
    includeContent?: boolean;
}

export interface SearchResult {
    id: string;
    type: 'memory' | 'file' | 'workspace';
    title: string;
    content: string;
    score: number;
    metadata: {
        path?: string;
        language?: string;
        timestamp?: Date;
        importance?: number;
        tags?: string[];
        fileType?: string;
        size?: number;
    };
    highlights: string[];
    relatedResults: string[];
}

export interface SearchResponse {
    query: SearchQuery;
    results: SearchResult[];
    totalResults: number;
    searchTime: number;
    suggestions: string[];
    facets: {
        types: Record<string, number>;
        languages: Record<string, number>;
        tags: Record<string, number>;
        timeRanges: Record<string, number>;
    };
}

export class KnowledgeSearchEngine {
    private outputChannel: vscode.OutputChannel;
    private searchHistory: SearchQuery[] = [];

    constructor(
        private memoryManager: EnhancedMemoryManager,
        private modelManager: ModelManager
    ) {
        this.outputChannel = vscode.window.createOutputChannel('Agent Zero Knowledge Search');
    }

    public async search(query: SearchQuery): Promise<SearchResponse> {
        const startTime = Date.now();
        
        try {
            this.outputChannel.appendLine(`Searching for: "${query.text}"`);
            
            // Add to search history
            this.searchHistory.push(query);
            if (this.searchHistory.length > 50) {
                this.searchHistory.shift();
            }

            const results: SearchResult[] = [];
            
            // Search in different sources based on query type
            if (query.type === 'memory' || query.type === 'all') {
                const memoryResults = await this.searchMemories(query);
                results.push(...memoryResults);
            }

            if (query.type === 'file' || query.type === 'all') {
                const fileResults = await this.searchFiles(query);
                results.push(...fileResults);
            }

            if (query.type === 'workspace' || query.type === 'all') {
                const workspaceResults = await this.searchWorkspace(query);
                results.push(...workspaceResults);
            }

            // Sort by relevance score
            results.sort((a, b) => b.score - a.score);

            // Apply limit
            const limitedResults = results.slice(0, query.limit || 20);

            // Generate suggestions and facets
            const suggestions = await this.generateSuggestions(query, results);
            const facets = this.generateFacets(results);

            const searchTime = Date.now() - startTime;
            this.outputChannel.appendLine(`Search completed in ${searchTime}ms, found ${results.length} results`);

            return {
                query,
                results: limitedResults,
                totalResults: results.length,
                searchTime,
                suggestions,
                facets
            };

        } catch (error) {
            const searchTime = Date.now() - startTime;
            this.outputChannel.appendLine(`Search failed: ${error}`);
            
            return {
                query,
                results: [],
                totalResults: 0,
                searchTime,
                suggestions: [],
                facets: {
                    types: {},
                    languages: {},
                    tags: {},
                    timeRanges: {}
                }
            };
        }
    }

    private async searchMemories(query: SearchQuery): Promise<SearchResult[]> {
        const memories = await this.memoryManager.loadMemory(
            query.text,
            query.filters?.memoryType,
            query.limit || 20
        );

        const results: SearchResult[] = [];

        for (const memory of memories) {
            if (!this.matchesFilters(memory, query.filters)) continue;

            const score = await this.calculateMemoryScore(memory, query);
            const highlights = this.extractHighlights(memory.content, query.text);

            results.push({
                id: memory.id,
                type: 'memory',
                title: memory.title,
                content: query.includeContent ? memory.content : memory.content.substring(0, 200) + '...',
                score,
                metadata: {
                    timestamp: memory.timestamp,
                    importance: memory.importance,
                    tags: memory.tags,
                    fileType: memory.type
                },
                highlights,
                relatedResults: memory.relationships.relatedIds
            });
        }

        return results;
    }

    private async searchFiles(query: SearchQuery): Promise<SearchResult[]> {
        const results: SearchResult[] = [];
        
        // Search in VS Code workspace
        try {
            // Use VS Code's findFiles and then search within files
            const filePattern = this.buildFilePattern(query.filters);
            const files = await vscode.workspace.findFiles(filePattern, '**/node_modules/**', 1000);
            const searchResults: any[] = [];

            for (const file of files) {
                try {
                    const document = await vscode.workspace.openTextDocument(file);
                    const text = document.getText();
                    const lines = text.split('\n');

                    lines.forEach((line, index) => {
                        if (line.toLowerCase().includes(query.text.toLowerCase())) {
                            searchResults.push([file, [{
                                range: new vscode.Range(index, 0, index, line.length),
                                preview: { text: line, matches: [] }
                            }]]);
                        }
                    });
                } catch (error) {
                    // Skip files that can't be read
                }
            }

            for (const [uri, matches] of searchResults) {
                for (const match of matches) {
                    const score = this.calculateFileScore(match, query);
                    
                    results.push({
                        id: uri.toString(),
                        type: 'file',
                        title: `${uri.fsPath}:${match.range.start.line + 1}`,
                        content: match.preview.text,
                        score,
                        metadata: {
                            path: uri.fsPath,
                            language: this.getLanguageFromPath(uri.fsPath)
                        },
                        highlights: [match.text],
                        relatedResults: []
                    });
                }
            }
        } catch (error) {
            this.outputChannel.appendLine(`File search error: ${error}`);
        }

        return results;
    }

    private async searchWorkspace(query: SearchQuery): Promise<SearchResult[]> {
        const results: SearchResult[] = [];
        
        // This would search in workspace knowledge
        // Implementation depends on how workspace knowledge is stored
        
        return results;
    }

    private async calculateMemoryScore(memory: EnhancedMemoryItem, query: SearchQuery): Promise<number> {
        let score = 0;

        // Text relevance (using semantic similarity if available)
        if (memory.embedding && this.modelManager) {
            try {
                const queryEmbedding = await this.modelManager.generateEmbeddings(query.text);
                const similarity = this.cosineSimilarity(memory.embedding, queryEmbedding);
                score += similarity * 50; // Weight semantic similarity highly
            } catch (error) {
                // Fallback to text matching
                score += this.calculateTextScore(memory.content, query.text);
            }
        } else {
            score += this.calculateTextScore(memory.content, query.text);
        }

        // Title relevance
        score += this.calculateTextScore(memory.title, query.text) * 2;

        // Tag relevance
        for (const tag of memory.tags) {
            if (tag.toLowerCase().includes(query.text.toLowerCase())) {
                score += 10;
            }
        }

        // Importance boost
        score += memory.importance;

        // Recency boost (newer memories get slight boost)
        const daysSinceCreation = (Date.now() - memory.timestamp.getTime()) / (1000 * 60 * 60 * 24);
        score += Math.max(0, 5 - daysSinceCreation * 0.1);

        return score;
    }

    private calculateFileScore(match: any, query: SearchQuery): number {
        let score = 0;

        // Exact match bonus
        if (match.text.toLowerCase() === query.text.toLowerCase()) {
            score += 20;
        }

        // Context relevance
        const contextWords = match.preview.text.toLowerCase().split(/\s+/);
        const queryWords = query.text.toLowerCase().split(/\s+/);
        
        for (const queryWord of queryWords) {
            for (const contextWord of contextWords) {
                if (contextWord.includes(queryWord)) {
                    score += 2;
                }
            }
        }

        // File type relevance
        const filePath = match.uri?.fsPath || '';
        if (this.isSourceFile(filePath)) {
            score += 5;
        }

        return score;
    }

    private calculateTextScore(text: string, query: string): number {
        const lowerText = text.toLowerCase();
        const lowerQuery = query.toLowerCase();
        
        let score = 0;

        // Exact phrase match
        if (lowerText.includes(lowerQuery)) {
            score += 15;
        }

        // Word matches
        const textWords = lowerText.split(/\s+/);
        const queryWords = lowerQuery.split(/\s+/);
        
        for (const queryWord of queryWords) {
            for (const textWord of textWords) {
                if (textWord === queryWord) {
                    score += 5;
                } else if (textWord.includes(queryWord)) {
                    score += 2;
                }
            }
        }

        return score;
    }

    private extractHighlights(content: string, query: string): string[] {
        const highlights: string[] = [];
        const words = query.toLowerCase().split(/\s+/);
        const sentences = content.split(/[.!?]+/);

        for (const sentence of sentences) {
            const lowerSentence = sentence.toLowerCase();
            let hasMatch = false;

            for (const word of words) {
                if (lowerSentence.includes(word)) {
                    hasMatch = true;
                    break;
                }
            }

            if (hasMatch) {
                highlights.push(sentence.trim());
                if (highlights.length >= 3) break;
            }
        }

        return highlights;
    }

    private matchesFilters(memory: EnhancedMemoryItem, filters?: SearchQuery['filters']): boolean {
        if (!filters) return true;

        // Memory type filter
        if (filters.memoryType && memory.type !== filters.memoryType) {
            return false;
        }

        // Time range filter
        if (filters.timeRange) {
            if (memory.timestamp < filters.timeRange.start || memory.timestamp > filters.timeRange.end) {
                return false;
            }
        }

        // Importance filter
        if (filters.importance) {
            if (memory.importance < filters.importance.min || memory.importance > filters.importance.max) {
                return false;
            }
        }

        // Tags filter
        if (filters.tags && filters.tags.length > 0) {
            const hasMatchingTag = filters.tags.some(tag => 
                memory.tags.some(memoryTag => 
                    memoryTag.toLowerCase().includes(tag.toLowerCase())
                )
            );
            if (!hasMatchingTag) {
                return false;
            }
        }

        return true;
    }

    private buildFilePattern(filters?: SearchQuery['filters']): string {
        if (!filters?.language) {
            return '**/*';
        }

        const extensions: Record<string, string[]> = {
            'javascript': ['js', 'jsx'],
            'typescript': ['ts', 'tsx'],
            'python': ['py'],
            'java': ['java'],
            'cpp': ['cpp', 'cc', 'cxx'],
            'c': ['c', 'h'],
            'rust': ['rs'],
            'go': ['go']
        };

        const exts = extensions[filters.language] || [filters.language];
        return `**/*.{${exts.join(',')}}`;
    }

    private getLanguageFromPath(filePath: string): string {
        const ext = filePath.split('.').pop()?.toLowerCase();
        const mapping: Record<string, string> = {
            'js': 'javascript',
            'ts': 'typescript',
            'py': 'python',
            'java': 'java',
            'cpp': 'cpp',
            'c': 'c',
            'rs': 'rust',
            'go': 'go'
        };
        return mapping[ext || ''] || 'text';
    }

    private isSourceFile(filePath: string): boolean {
        const sourceExts = ['.js', '.ts', '.py', '.java', '.cpp', '.c', '.rs', '.go'];
        return sourceExts.some(ext => filePath.endsWith(ext));
    }

    private async generateSuggestions(query: SearchQuery, results: SearchResult[]): Promise<string[]> {
        const suggestions: string[] = [];

        // Extract common terms from results
        const terms = new Map<string, number>();
        
        for (const result of results.slice(0, 10)) {
            const words = result.content.toLowerCase().split(/\s+/);
            for (const word of words) {
                if (word.length > 3 && !this.isStopWord(word)) {
                    terms.set(word, (terms.get(word) || 0) + 1);
                }
            }
        }

        // Sort by frequency and take top suggestions
        const sortedTerms = Array.from(terms.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, 5)
            .map(([term]) => term);

        suggestions.push(...sortedTerms);

        // Add related search suggestions from history
        const relatedQueries = this.searchHistory
            .filter(q => q.text !== query.text && this.isRelatedQuery(q.text, query.text))
            .slice(-3)
            .map(q => q.text);

        suggestions.push(...relatedQueries);

        return suggestions.slice(0, 8);
    }

    private generateFacets(results: SearchResult[]): SearchResponse['facets'] {
        const facets = {
            types: {} as Record<string, number>,
            languages: {} as Record<string, number>,
            tags: {} as Record<string, number>,
            timeRanges: {} as Record<string, number>
        };

        for (const result of results) {
            // Type facets
            facets.types[result.type] = (facets.types[result.type] || 0) + 1;

            // Language facets
            if (result.metadata.language) {
                facets.languages[result.metadata.language] = (facets.languages[result.metadata.language] || 0) + 1;
            }

            // Tag facets
            if (result.metadata.tags) {
                for (const tag of result.metadata.tags) {
                    facets.tags[tag] = (facets.tags[tag] || 0) + 1;
                }
            }

            // Time range facets
            if (result.metadata.timestamp) {
                const timeRange = this.getTimeRange(result.metadata.timestamp);
                facets.timeRanges[timeRange] = (facets.timeRanges[timeRange] || 0) + 1;
            }
        }

        return facets;
    }

    private isStopWord(word: string): boolean {
        const stopWords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'];
        return stopWords.includes(word);
    }

    private isRelatedQuery(query1: string, query2: string): boolean {
        const words1 = query1.toLowerCase().split(/\s+/);
        const words2 = query2.toLowerCase().split(/\s+/);
        
        const commonWords = words1.filter(word => words2.includes(word));
        return commonWords.length > 0;
    }

    private getTimeRange(timestamp: Date): string {
        const now = new Date();
        const diffDays = (now.getTime() - timestamp.getTime()) / (1000 * 60 * 60 * 24);

        if (diffDays < 1) return 'Today';
        if (diffDays < 7) return 'This week';
        if (diffDays < 30) return 'This month';
        if (diffDays < 365) return 'This year';
        return 'Older';
    }

    private cosineSimilarity(a: number[], b: number[]): number {
        if (a.length !== b.length) return 0;

        let dotProduct = 0;
        let normA = 0;
        let normB = 0;

        for (let i = 0; i < a.length; i++) {
            dotProduct += a[i] * b[i];
            normA += a[i] * a[i];
            normB += b[i] * b[i];
        }

        return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
    }

    public async showSearchInterface(): Promise<void> {
        const panel = vscode.window.createWebviewPanel(
            'knowledgeSearch',
            'Knowledge Search',
            vscode.ViewColumn.Two,
            {
                enableScripts: true,
                retainContextWhenHidden: true
            }
        );

        panel.webview.html = this.getSearchInterfaceHtml();

        // Handle search requests
        panel.webview.onDidReceiveMessage(async (message) => {
            if (message.command === 'search') {
                const results = await this.search(message.query);
                panel.webview.postMessage({
                    command: 'searchResults',
                    results: results
                });
            }
        });
    }

    private getSearchInterfaceHtml(): string {
        return `
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body { font-family: var(--vscode-font-family); padding: 20px; }
                .search-container { margin-bottom: 20px; }
                .search-input { width: 100%; padding: 10px; margin-bottom: 10px; }
                .filters { display: flex; gap: 10px; margin-bottom: 10px; }
                .filter-select { padding: 5px; }
                .results-container { margin-top: 20px; }
                .result-item { margin-bottom: 15px; padding: 10px; border: 1px solid var(--vscode-panel-border); border-radius: 5px; }
                .result-title { font-weight: bold; margin-bottom: 5px; }
                .result-content { margin-bottom: 5px; }
                .result-metadata { font-size: 0.9em; color: var(--vscode-descriptionForeground); }
                .facets { display: flex; gap: 20px; margin-top: 20px; }
                .facet-group { flex: 1; }
                .facet-title { font-weight: bold; margin-bottom: 5px; }
                .facet-item { padding: 2px 0; }
            </style>
        </head>
        <body>
            <div class="search-container">
                <h1>Knowledge Search</h1>
                <input type="text" class="search-input" id="searchInput" placeholder="Search memories, files, and workspace..." />
                <div class="filters">
                    <select class="filter-select" id="typeFilter">
                        <option value="all">All Types</option>
                        <option value="memory">Memories</option>
                        <option value="file">Files</option>
                        <option value="workspace">Workspace</option>
                    </select>
                    <select class="filter-select" id="languageFilter">
                        <option value="">All Languages</option>
                        <option value="javascript">JavaScript</option>
                        <option value="typescript">TypeScript</option>
                        <option value="python">Python</option>
                        <option value="java">Java</option>
                    </select>
                    <button onclick="performSearch()">Search</button>
                </div>
            </div>
            
            <div class="results-container" id="resultsContainer">
                <!-- Search results will appear here -->
            </div>
            
            <div class="facets" id="facetsContainer" style="display: none;">
                <!-- Facets will appear here -->
            </div>

            <script>
                const vscode = acquireVsCodeApi();
                
                function performSearch() {
                    const query = {
                        text: document.getElementById('searchInput').value,
                        type: document.getElementById('typeFilter').value,
                        filters: {
                            language: document.getElementById('languageFilter').value || undefined
                        },
                        limit: 20,
                        includeContent: true
                    };
                    
                    vscode.postMessage({
                        command: 'search',
                        query: query
                    });
                }
                
                // Handle Enter key
                document.getElementById('searchInput').addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        performSearch();
                    }
                });
                
                // Handle search results
                window.addEventListener('message', event => {
                    const message = event.data;
                    if (message.command === 'searchResults') {
                        displayResults(message.results);
                    }
                });
                
                function displayResults(searchResponse) {
                    const container = document.getElementById('resultsContainer');
                    const facetsContainer = document.getElementById('facetsContainer');
                    
                    if (searchResponse.results.length === 0) {
                        container.innerHTML = '<p>No results found.</p>';
                        facetsContainer.style.display = 'none';
                        return;
                    }
                    
                    let html = \`<h2>Found \${searchResponse.totalResults} results in \${searchResponse.searchTime}ms</h2>\`;
                    
                    for (const result of searchResponse.results) {
                        html += \`
                            <div class="result-item">
                                <div class="result-title">\${result.title}</div>
                                <div class="result-content">\${result.content}</div>
                                <div class="result-metadata">
                                    Type: \${result.type} | Score: \${result.score.toFixed(2)}
                                    \${result.metadata.language ? ' | Language: ' + result.metadata.language : ''}
                                    \${result.metadata.timestamp ? ' | ' + new Date(result.metadata.timestamp).toLocaleDateString() : ''}
                                </div>
                            </div>
                        \`;
                    }
                    
                    container.innerHTML = html;
                    
                    // Display facets
                    displayFacets(searchResponse.facets);
                    facetsContainer.style.display = 'flex';
                }
                
                function displayFacets(facets) {
                    const container = document.getElementById('facetsContainer');
                    let html = '';
                    
                    for (const [facetName, facetData] of Object.entries(facets)) {
                        if (Object.keys(facetData).length > 0) {
                            html += \`
                                <div class="facet-group">
                                    <div class="facet-title">\${facetName.charAt(0).toUpperCase() + facetName.slice(1)}</div>
                            \`;
                            
                            for (const [key, count] of Object.entries(facetData)) {
                                html += \`<div class="facet-item">\${key} (\${count})</div>\`;
                            }
                            
                            html += '</div>';
                        }
                    }
                    
                    container.innerHTML = html;
                }
            </script>
        </body>
        </html>`;
    }

    public getSearchHistory(): SearchQuery[] {
        return [...this.searchHistory];
    }

    public clearSearchHistory(): void {
        this.searchHistory = [];
    }

    public dispose(): void {
        this.outputChannel.dispose();
    }
}
