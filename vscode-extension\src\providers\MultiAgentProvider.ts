import * as vscode from 'vscode';
import { MultiAgentSystem, AgentTemplate, AgentType, AgentTask } from '../core/MultiAgentSystem';
import { Agent } from '../core/AgentManager';

export class MultiAgentProvider implements vscode.TreeDataProvider<Agent> {
    private _onDidChangeTreeData: vscode.EventEmitter<Agent | undefined | null | void> = new vscode.EventEmitter<Agent | undefined | null | void>();
    readonly onDidChangeTreeData: vscode.Event<Agent | undefined | null | void> = this._onDidChangeTreeData.event;

    constructor(
        private context: vscode.ExtensionContext,
        private multiAgentSystem: MultiAgentSystem
    ) {
        this.registerCommands();
    }

    private registerCommands(): void {
        // Agent creation commands
        const createAgentCommand = vscode.commands.registerCommand('agent-zero.createAgentFromTemplate', async () => {
            await this.showAgentCreationWizard();
        });

        const createCustomAgentCommand = vscode.commands.registerCommand('agent-zero.createCustomAgent', async () => {
            await this.showCustomAgentCreationDialog();
        });

        // Agent management commands
        const deleteAgentCommand = vscode.commands.registerCommand('agent-zero.deleteAgent', async (agent: Agent) => {
            const confirmation = await vscode.window.showWarningMessage(
                `Are you sure you want to delete agent "${agent.name}" and all its sub-agents?`,
                { modal: true },
                'Delete'
            );

            if (confirmation === 'Delete') {
                await this.multiAgentSystem.deleteAgent(agent.id);
                this.refresh();
                vscode.window.showInformationMessage(`Deleted agent: ${agent.name}`);
            }
        });

        const assignTaskCommand = vscode.commands.registerCommand('agent-zero.assignTask', async (agent: Agent) => {
            await this.showTaskAssignmentDialog(agent);
        });

        const viewAgentDetailsCommand = vscode.commands.registerCommand('agent-zero.viewAgentDetails', (agent: Agent) => {
            this.showAgentDetailsPanel(agent);
        });

        const viewAgentTasksCommand = vscode.commands.registerCommand('agent-zero.viewAgentTasks', (agent: Agent) => {
            this.showAgentTasksPanel(agent);
        });

        const createSubAgentCommand = vscode.commands.registerCommand('agent-zero.createSubAgent', async (parentAgent: Agent) => {
            await this.showAgentCreationWizard(parentAgent.id);
        });

        const showAgentDashboardCommand = vscode.commands.registerCommand('agent-zero.showAgentDashboard', () => {
            this.showAgentDashboard();
        });

        // Add commands to context subscriptions
        this.context.subscriptions.push(
            createAgentCommand,
            createCustomAgentCommand,
            deleteAgentCommand,
            assignTaskCommand,
            viewAgentDetailsCommand,
            viewAgentTasksCommand,
            createSubAgentCommand,
            showAgentDashboardCommand
        );
    }

    private async showAgentCreationWizard(parentId?: string): Promise<void> {
        const templates = this.multiAgentSystem.getAgentTemplates();
        
        const templateItems = templates.map(template => ({
            label: template.name,
            description: template.description,
            detail: `Capabilities: ${template.capabilities.join(', ')}`,
            template: template
        }));

        const selectedTemplate = await vscode.window.showQuickPick(templateItems, {
            placeHolder: 'Select an agent template',
            matchOnDescription: true,
            matchOnDetail: true
        });

        if (!selectedTemplate) {
            return;
        }

        const agentName = await vscode.window.showInputBox({
            prompt: 'Enter agent name',
            value: selectedTemplate.template.name,
            validateInput: (value) => {
                if (!value || value.trim().length === 0) {
                    return 'Agent name cannot be empty';
                }
                return null;
            }
        });

        if (!agentName) {
            return;
        }

        try {
            const agent = await this.multiAgentSystem.createAgentFromTemplate(
                selectedTemplate.template.id,
                agentName,
                parentId
            );
            
            this.refresh();
            vscode.window.showInformationMessage(`Created agent: ${agent.name}`);
            
            // Optionally show agent details
            const showDetails = await vscode.window.showInformationMessage(
                `Agent "${agent.name}" created successfully!`,
                'View Details'
            );
            
            if (showDetails === 'View Details') {
                this.showAgentDetailsPanel(agent);
            }
            
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to create agent: ${error}`);
        }
    }

    private async showCustomAgentCreationDialog(): Promise<void> {
        const panel = vscode.window.createWebviewPanel(
            'createCustomAgent',
            'Create Custom Agent',
            vscode.ViewColumn.Two,
            {
                enableScripts: true,
                retainContextWhenHidden: true
            }
        );

        panel.webview.html = this.getCustomAgentCreationHtml();

        panel.webview.onDidReceiveMessage(async (message) => {
            switch (message.command) {
                case 'createAgent':
                    try {
                        const agent = await this.multiAgentSystem.createCustomAgent(message.config);
                        this.refresh();
                        panel.dispose();
                        vscode.window.showInformationMessage(`Created custom agent: ${agent.name}`);
                    } catch (error) {
                        panel.webview.postMessage({
                            command: 'error',
                            message: String(error)
                        });
                    }
                    break;
            }
        });
    }

    private async showTaskAssignmentDialog(agent: Agent): Promise<void> {
        const title = await vscode.window.showInputBox({
            prompt: 'Enter task title',
            placeHolder: 'Task title'
        });

        if (!title) {
            return;
        }

        const description = await vscode.window.showInputBox({
            prompt: 'Enter task description',
            placeHolder: 'Detailed task description'
        });

        if (!description) {
            return;
        }

        const priorityItems = [
            { label: 'Low', value: 'low' as const },
            { label: 'Medium', value: 'medium' as const },
            { label: 'High', value: 'high' as const },
            { label: 'Urgent', value: 'urgent' as const }
        ];

        const selectedPriority = await vscode.window.showQuickPick(priorityItems, {
            placeHolder: 'Select task priority'
        });

        if (!selectedPriority) {
            return;
        }

        try {
            const taskId = await this.multiAgentSystem.assignTask(agent.id, {
                title,
                description,
                status: 'pending',
                priority: selectedPriority.value,
                dependencies: [],
                assignedBy: 'user'
            });

            this.refresh();
            vscode.window.showInformationMessage(`Task "${title}" assigned to ${agent.name}`);

        } catch (error) {
            vscode.window.showErrorMessage(`Failed to assign task: ${error}`);
        }
    }

    private showAgentDetailsPanel(agent: Agent): void {
        const panel = vscode.window.createWebviewPanel(
            'agentDetails',
            `Agent Details: ${agent.name}`,
            vscode.ViewColumn.Two,
            {
                enableScripts: true,
                retainContextWhenHidden: true
            }
        );

        panel.webview.html = this.getAgentDetailsHtml(agent);

        panel.webview.onDidReceiveMessage(async (message) => {
            switch (message.command) {
                case 'assignTask':
                    await this.showTaskAssignmentDialog(agent);
                    panel.webview.html = this.getAgentDetailsHtml(agent); // Refresh
                    break;
                case 'sendMessage':
                    // Handle inter-agent communication
                    break;
            }
        });
    }

    private showAgentTasksPanel(agent: Agent): void {
        const panel = vscode.window.createWebviewPanel(
            'agentTasks',
            `Tasks: ${agent.name}`,
            vscode.ViewColumn.Two,
            {
                enableScripts: true,
                retainContextWhenHidden: true
            }
        );

        const tasks = this.multiAgentSystem.getAgentTasks(agent.id);
        panel.webview.html = this.getAgentTasksHtml(agent, tasks);
    }

    private showAgentDashboard(): void {
        const panel = vscode.window.createWebviewPanel(
            'agentDashboard',
            'Multi-Agent Dashboard',
            vscode.ViewColumn.Two,
            {
                enableScripts: true,
                retainContextWhenHidden: true
            }
        );

        panel.webview.html = this.getAgentDashboardHtml();

        panel.webview.onDidReceiveMessage(async (message) => {
            switch (message.command) {
                case 'createAgent':
                    await this.showAgentCreationWizard();
                    panel.webview.html = this.getAgentDashboardHtml(); // Refresh
                    break;
                case 'viewAgent':
                    // Handle agent viewing
                    break;
            }
        });
    }

    private getCustomAgentCreationHtml(): string {
        return `
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body { font-family: var(--vscode-font-family); padding: 20px; }
                .form-group { margin-bottom: 15px; }
                label { display: block; margin-bottom: 5px; font-weight: bold; }
                input, textarea, select { width: 100%; padding: 8px; margin-bottom: 5px; }
                textarea { height: 100px; resize: vertical; }
                .capabilities, .tools { display: flex; flex-wrap: wrap; gap: 10px; }
                .checkbox-item { display: flex; align-items: center; margin-right: 15px; }
                .checkbox-item input { width: auto; margin-right: 5px; }
                button { padding: 10px 20px; background-color: var(--vscode-button-background); color: var(--vscode-button-foreground); border: none; border-radius: 3px; cursor: pointer; }
                button:hover { background-color: var(--vscode-button-hoverBackground); }
                .error { color: var(--vscode-errorForeground); margin-top: 10px; }
            </style>
        </head>
        <body>
            <h1>Create Custom Agent</h1>
            
            <div class="form-group">
                <label for="agentName">Agent Name:</label>
                <input type="text" id="agentName" placeholder="Enter agent name" required>
            </div>
            
            <div class="form-group">
                <label for="agentDescription">Description:</label>
                <textarea id="agentDescription" placeholder="Describe what this agent does"></textarea>
            </div>
            
            <div class="form-group">
                <label for="systemPrompt">System Prompt:</label>
                <textarea id="systemPrompt" placeholder="Enter the system prompt that defines the agent's behavior"></textarea>
            </div>
            
            <div class="form-group">
                <label>Capabilities:</label>
                <div class="capabilities">
                    <div class="checkbox-item"><input type="checkbox" value="conversation"> Conversation</div>
                    <div class="checkbox-item"><input type="checkbox" value="code-generation"> Code Generation</div>
                    <div class="checkbox-item"><input type="checkbox" value="debugging"> Debugging</div>
                    <div class="checkbox-item"><input type="checkbox" value="analysis"> Analysis</div>
                    <div class="checkbox-item"><input type="checkbox" value="research"> Research</div>
                    <div class="checkbox-item"><input type="checkbox" value="testing"> Testing</div>
                    <div class="checkbox-item"><input type="checkbox" value="documentation"> Documentation</div>
                    <div class="checkbox-item"><input type="checkbox" value="security"> Security</div>
                </div>
            </div>
            
            <div class="form-group">
                <label>Available Tools:</label>
                <div class="tools">
                    <div class="checkbox-item"><input type="checkbox" value="file-editor"> File Editor</div>
                    <div class="checkbox-item"><input type="checkbox" value="code-execution"> Code Execution</div>
                    <div class="checkbox-item"><input type="checkbox" value="terminal"> Terminal</div>
                    <div class="checkbox-item"><input type="checkbox" value="browser"> Browser</div>
                    <div class="checkbox-item"><input type="checkbox" value="search-engine"> Search Engine</div>
                    <div class="checkbox-item"><input type="checkbox" value="git-integration"> Git Integration</div>
                </div>
            </div>
            
            <div class="form-group">
                <label for="modelProvider">Model Provider:</label>
                <select id="modelProvider">
                    <option value="openai">OpenAI</option>
                    <option value="anthropic">Anthropic</option>
                    <option value="gemini">Google Gemini</option>
                    <option value="groq">Groq</option>
                    <option value="ollama">Ollama</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="modelName">Model Name:</label>
                <input type="text" id="modelName" value="gpt-4" placeholder="Model name">
            </div>
            
            <button onclick="createAgent()">Create Agent</button>
            <div class="error" id="errorMessage" style="display: none;"></div>
            
            <script>
                const vscode = acquireVsCodeApi();
                
                function createAgent() {
                    const name = document.getElementById('agentName').value;
                    const description = document.getElementById('agentDescription').value;
                    const systemPrompt = document.getElementById('systemPrompt').value;
                    
                    if (!name || !description || !systemPrompt) {
                        showError('Please fill in all required fields');
                        return;
                    }
                    
                    const capabilities = Array.from(document.querySelectorAll('.capabilities input:checked')).map(cb => cb.value);
                    const tools = Array.from(document.querySelectorAll('.tools input:checked')).map(cb => cb.value);
                    
                    const config = {
                        name,
                        description,
                        systemPrompt,
                        capabilities,
                        tools,
                        modelConfig: {
                            provider: document.getElementById('modelProvider').value,
                            model: document.getElementById('modelName').value,
                            temperature: 0.7,
                            maxTokens: 4000
                        }
                    };
                    
                    vscode.postMessage({
                        command: 'createAgent',
                        config: config
                    });
                }
                
                function showError(message) {
                    const errorDiv = document.getElementById('errorMessage');
                    errorDiv.textContent = message;
                    errorDiv.style.display = 'block';
                }
                
                // Handle messages from extension
                window.addEventListener('message', event => {
                    const message = event.data;
                    if (message.command === 'error') {
                        showError(message.message);
                    }
                });
            </script>
        </body>
        </html>`;
    }

    private getAgentDetailsHtml(agent: Agent): string {
        const tasks = this.multiAgentSystem.getAgentTasks(agent.id);
        const pendingTasks = tasks.filter(t => t.status === 'pending').length;
        const completedTasks = tasks.filter(t => t.status === 'completed').length;
        const template = agent.context.template as AgentTemplate;

        return `
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body { font-family: var(--vscode-font-family); padding: 20px; }
                .agent-header { display: flex; align-items: center; margin-bottom: 20px; }
                .agent-icon { font-size: 24px; margin-right: 10px; }
                .agent-status { padding: 4px 8px; border-radius: 12px; font-size: 12px; margin-left: 10px; }
                .status-idle { background-color: var(--vscode-charts-green); }
                .status-working { background-color: var(--vscode-charts-yellow); }
                .status-error { background-color: var(--vscode-charts-red); }
                .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px; }
                .info-card { background-color: var(--vscode-editor-inactiveSelectionBackground); padding: 15px; border-radius: 5px; }
                .info-card h3 { margin-top: 0; }
                .capabilities, .tools { display: flex; flex-wrap: wrap; gap: 5px; }
                .tag { background-color: var(--vscode-badge-background); color: var(--vscode-badge-foreground); padding: 2px 6px; border-radius: 3px; font-size: 11px; }
                button { padding: 8px 16px; margin: 5px; background-color: var(--vscode-button-background); color: var(--vscode-button-foreground); border: none; border-radius: 3px; cursor: pointer; }
            </style>
        </head>
        <body>
            <div class="agent-header">
                <div class="agent-icon">${template?.icon || '🤖'}</div>
                <h1>${agent.name}</h1>
                <span class="agent-status status-${agent.status}">${agent.status.toUpperCase()}</span>
            </div>
            
            <div class="info-grid">
                <div class="info-card">
                    <h3>Basic Information</h3>
                    <p><strong>Type:</strong> ${agent.type}</p>
                    <p><strong>Created:</strong> ${agent.created.toLocaleString()}</p>
                    <p><strong>Last Activity:</strong> ${agent.lastActivity.toLocaleString()}</p>
                    <p><strong>Children:</strong> ${agent.children.length}</p>
                </div>
                
                <div class="info-card">
                    <h3>Task Statistics</h3>
                    <p><strong>Pending Tasks:</strong> ${pendingTasks}</p>
                    <p><strong>Completed Tasks:</strong> ${completedTasks}</p>
                    <p><strong>Total Tasks:</strong> ${tasks.length}</p>
                </div>
            </div>
            
            <div class="info-card">
                <h3>Capabilities</h3>
                <div class="capabilities">
                    ${(agent.context.capabilities || []).map((cap: string) => `<span class="tag">${cap}</span>`).join('')}
                </div>
            </div>
            
            <div class="info-card">
                <h3>Available Tools</h3>
                <div class="tools">
                    ${(agent.context.tools || []).map((tool: string) => `<span class="tag">${tool}</span>`).join('')}
                </div>
            </div>
            
            <div class="info-card">
                <h3>Actions</h3>
                <button onclick="assignTask()">Assign Task</button>
                <button onclick="viewTasks()">View Tasks</button>
                <button onclick="sendMessage()">Send Message</button>
            </div>
            
            <script>
                const vscode = acquireVsCodeApi();
                
                function assignTask() {
                    vscode.postMessage({ command: 'assignTask' });
                }
                
                function viewTasks() {
                    // This would open the tasks view
                }
                
                function sendMessage() {
                    vscode.postMessage({ command: 'sendMessage' });
                }
            </script>
        </body>
        </html>`;
    }

    private getAgentTasksHtml(agent: Agent, tasks: AgentTask[]): string {
        const taskRows = tasks.map(task => `
            <tr class="task-${task.status}">
                <td>${task.title}</td>
                <td>${task.status}</td>
                <td>${task.priority}</td>
                <td>${task.createdAt.toLocaleDateString()}</td>
                <td>${task.completedAt ? task.completedAt.toLocaleDateString() : '-'}</td>
            </tr>
        `).join('');

        return `
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body { font-family: var(--vscode-font-family); padding: 20px; }
                table { width: 100%; border-collapse: collapse; }
                th, td { padding: 8px; text-align: left; border-bottom: 1px solid var(--vscode-panel-border); }
                th { background-color: var(--vscode-editor-inactiveSelectionBackground); }
                .task-completed { background-color: var(--vscode-charts-green); opacity: 0.7; }
                .task-in-progress { background-color: var(--vscode-charts-yellow); opacity: 0.7; }
                .task-failed { background-color: var(--vscode-charts-red); opacity: 0.7; }
            </style>
        </head>
        <body>
            <h1>Tasks for ${agent.name}</h1>
            
            <table>
                <thead>
                    <tr>
                        <th>Title</th>
                        <th>Status</th>
                        <th>Priority</th>
                        <th>Created</th>
                        <th>Completed</th>
                    </tr>
                </thead>
                <tbody>
                    ${taskRows}
                </tbody>
            </table>
        </body>
        </html>`;
    }

    private getAgentDashboardHtml(): string {
        // This would show an overview of all agents and their status
        return `
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body { font-family: var(--vscode-font-family); padding: 20px; }
                .dashboard-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
                .agent-card { background-color: var(--vscode-editor-inactiveSelectionBackground); padding: 15px; border-radius: 5px; }
                .agent-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; }
                .create-agent-btn { padding: 10px 20px; background-color: var(--vscode-button-background); color: var(--vscode-button-foreground); border: none; border-radius: 3px; cursor: pointer; margin-bottom: 20px; }
            </style>
        </head>
        <body>
            <h1>Multi-Agent Dashboard</h1>
            
            <button class="create-agent-btn" onclick="createAgent()">Create New Agent</button>
            
            <div class="dashboard-grid" id="agentGrid">
                <!-- Agent cards will be populated here -->
            </div>
            
            <script>
                const vscode = acquireVsCodeApi();
                
                function createAgent() {
                    vscode.postMessage({ command: 'createAgent' });
                }
            </script>
        </body>
        </html>`;
    }

    // TreeDataProvider implementation
    getTreeItem(element: Agent): vscode.TreeItem {
        return this.multiAgentSystem.getTreeItem(element);
    }

    getChildren(element?: Agent): Thenable<Agent[]> {
        return this.multiAgentSystem.getChildren(element);
    }

    refresh(): void {
        this._onDidChangeTreeData.fire();
        this.multiAgentSystem.refresh();
    }
}
